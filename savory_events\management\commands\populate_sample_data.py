from django.core.management.base import BaseCommand
from django.utils import timezone
from decimal import Decimal
from datetime import timedelta
from savory_events.models import MenuItem, SoundSystemService, Customer, Booking, BookingMenuItem, Payment


class Command(BaseCommand):
    help = 'Populate the database with sample data for testing'

    def handle(self, *args, **options):
        self.stdout.write('Creating sample data...')
        
        # Create Menu Items
        menu_items = [
            # Appetizers
            {
                'name': 'Bruschetta Trio',
                'description': 'Three varieties of our signature bruschetta with fresh tomatoes, herbs, and cheese.',
                'category': 'appetizer',
                'price_per_person': Decimal('8.50')
            },
            {
                'name': 'Stuffed Mushrooms',
                'description': 'Button mushrooms stuffed with herbed cream cheese and breadcrumbs.',
                'category': 'appetizer',
                'price_per_person': Decimal('9.00')
            },
            {
                'name': 'Shrimp Cocktail',
                'description': 'Fresh jumbo shrimp served with our house-made cocktail sauce.',
                'category': 'appetizer',
                'price_per_person': Decimal('12.00')
            },
            # Main Dishes
            {
                'name': 'Grilled Salmon',
                'description': 'Atlantic salmon grilled to perfection with lemon herb butter.',
                'category': 'main',
                'price_per_person': Decimal('28.00')
            },
            {
                'name': 'Beef Tenderloin',
                'description': 'Premium beef tenderloin with garlic mashed potatoes and seasonal vegetables.',
                'category': 'main',
                'price_per_person': Decimal('35.00')
            },
            {
                'name': 'Chicken Marsala',
                'description': 'Pan-seared chicken breast in rich marsala wine sauce with mushrooms.',
                'category': 'main',
                'price_per_person': Decimal('24.00')
            },
            {
                'name': 'Vegetarian Risotto',
                'description': 'Creamy arborio rice with seasonal vegetables and parmesan cheese.',
                'category': 'main',
                'price_per_person': Decimal('22.00')
            },
            # Desserts
            {
                'name': 'Chocolate Lava Cake',
                'description': 'Warm chocolate cake with molten center, served with vanilla ice cream.',
                'category': 'dessert',
                'price_per_person': Decimal('8.00')
            },
            {
                'name': 'Tiramisu',
                'description': 'Traditional Italian dessert with coffee-soaked ladyfingers and mascarpone.',
                'category': 'dessert',
                'price_per_person': Decimal('7.50')
            },
            {
                'name': 'Fresh Berry Tart',
                'description': 'Buttery pastry shell filled with pastry cream and fresh seasonal berries.',
                'category': 'dessert',
                'price_per_person': Decimal('6.50')
            },
            # Beverages
            {
                'name': 'Premium Coffee Service',
                'description': 'Freshly brewed coffee with cream, sugar, and gourmet add-ins.',
                'category': 'drink',
                'price_per_person': Decimal('3.50')
            },
            {
                'name': 'Signature Lemonade',
                'description': 'House-made lemonade with fresh herbs and fruit garnish.',
                'category': 'drink',
                'price_per_person': Decimal('4.00')
            },
            {
                'name': 'Wine Selection',
                'description': 'Curated selection of red and white wines from local vineyards.',
                'category': 'drink',
                'price_per_person': Decimal('12.00')
            }
        ]
        
        for item_data in menu_items:
            item, created = MenuItem.objects.get_or_create(
                name=item_data['name'],
                defaults=item_data
            )
            if created:
                self.stdout.write(f'Created menu item: {item.name}')
        
        # Create Sound System Services
        sound_systems = [
            {
                'name': 'Basic Microphone Setup',
                'package_type': 'microphones',
                'description': 'Basic microphone setup for speeches and announcements.',
                'price': Decimal('150.00')
            },
            {
                'name': 'Professional DJ Package',
                'package_type': 'dj',
                'description': 'Professional DJ with music library, microphones, and sound system.',
                'price': Decimal('400.00')
            },
            {
                'name': 'Complete Audio Experience',
                'package_type': 'full_audio',
                'description': 'Complete audio system with DJ, lighting, wireless microphones, and premium speakers.',
                'price': Decimal('600.00')
            }
        ]
        
        for system_data in sound_systems:
            system, created = SoundSystemService.objects.get_or_create(
                name=system_data['name'],
                defaults=system_data
            )
            if created:
                self.stdout.write(f'Created sound system: {system.name}')
        
        # Create Sample Customers and Bookings
        customers_data = [
            {
                'name': 'Sarah Johnson',
                'email': '<EMAIL>',
                'phone': '(*************'
            },
            {
                'name': 'Mike Chen',
                'email': '<EMAIL>',
                'phone': '(*************'
            },
            {
                'name': 'Emma Williams',
                'email': '<EMAIL>',
                'phone': '(*************'
            }
        ]
        
        for customer_data in customers_data:
            customer, created = Customer.objects.get_or_create(
                email=customer_data['email'],
                defaults=customer_data
            )
            if created:
                self.stdout.write(f'Created customer: {customer.name}')
        
        # Create Sample Bookings
        sarah = Customer.objects.get(email='<EMAIL>')
        mike = Customer.objects.get(email='<EMAIL>')
        
        # Sarah's Wedding Booking
        if not Booking.objects.filter(customer=sarah).exists():
            wedding_booking = Booking.objects.create(
                customer=sarah,
                event_date=timezone.now() + timedelta(days=45),
                event_type='wedding',
                guest_count=150,
                venue_address='123 Garden View Lane, Wedding Hall, City, State 12345',
                special_requests='Vegetarian options needed for 20 guests. No nuts due to allergies.',
                status='confirmed'
            )
            
            # Add menu items to wedding
            salmon = MenuItem.objects.get(name='Grilled Salmon')
            risotto = MenuItem.objects.get(name='Vegetarian Risotto')
            bruschetta = MenuItem.objects.get(name='Bruschetta Trio')
            tiramisu = MenuItem.objects.get(name='Tiramisu')
            wine = MenuItem.objects.get(name='Wine Selection')
            
            BookingMenuItem.objects.create(booking=wedding_booking, menu_item=salmon, quantity=1)
            BookingMenuItem.objects.create(booking=wedding_booking, menu_item=risotto, quantity=1)
            BookingMenuItem.objects.create(booking=wedding_booking, menu_item=bruschetta, quantity=1)
            BookingMenuItem.objects.create(booking=wedding_booking, menu_item=tiramisu, quantity=1)
            BookingMenuItem.objects.create(booking=wedding_booking, menu_item=wine, quantity=1)
            
            # Add DJ service
            dj_service = SoundSystemService.objects.get(package_type='dj')
            from savory_events.models import BookingSoundSystem
            BookingSoundSystem.objects.create(
                booking=wedding_booking,
                sound_system=dj_service
            )
            
            wedding_booking.update_total_amount()
            
            # Add partial payment
            deposit_amount = wedding_booking.get_deposit_amount().quantize(Decimal('0.01'))
            Payment.objects.create(
                booking=wedding_booking,
                amount=deposit_amount,
                payment_type='deposit',
                notes='Initial deposit payment via bank transfer'
            )
            
            self.stdout.write(f'Created wedding booking for {sarah.name}')
        
        # Mike's Corporate Event
        if not Booking.objects.filter(customer=mike).exists():
            corporate_booking = Booking.objects.create(
                customer=mike,
                event_date=timezone.now() + timedelta(days=30),
                event_type='corporate',
                guest_count=75,
                venue_address='456 Business Center Dr, Conference Room A, City, State 12345',
                special_requests='Need setup by 11 AM. Lunch service from 12-2 PM.',
                status='pending'
            )
            
            # Add menu items to corporate event
            chicken = MenuItem.objects.get(name='Chicken Marsala')
            mushrooms = MenuItem.objects.get(name='Stuffed Mushrooms')
            coffee = MenuItem.objects.get(name='Premium Coffee Service')
            berry_tart = MenuItem.objects.get(name='Fresh Berry Tart')
            
            BookingMenuItem.objects.create(booking=corporate_booking, menu_item=chicken, quantity=1)
            BookingMenuItem.objects.create(booking=corporate_booking, menu_item=mushrooms, quantity=1)
            BookingMenuItem.objects.create(booking=corporate_booking, menu_item=coffee, quantity=1)
            BookingMenuItem.objects.create(booking=corporate_booking, menu_item=berry_tart, quantity=1)
            
            # Add microphone service
            mic_service = SoundSystemService.objects.get(package_type='microphones')
            from savory_events.models import BookingSoundSystem
            BookingSoundSystem.objects.create(
                booking=corporate_booking,
                sound_system=mic_service
            )
            
            corporate_booking.update_total_amount()
            
            self.stdout.write(f'Created corporate booking for {mike.name}')
        
        self.stdout.write(self.style.SUCCESS('Successfully populated sample data!'))
        self.stdout.write(f'Created {MenuItem.objects.count()} menu items')
        self.stdout.write(f'Created {SoundSystemService.objects.count()} sound system services')
        self.stdout.write(f'Created {Customer.objects.count()} customers')
        self.stdout.write(f'Created {Booking.objects.count()} bookings')
