<!DOCTYPE html>
<html lang="en" class="h-full">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}BonAppetit Catering Services{% endblock %}</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'deep-olive': '#556B2F',
                        'burnt-orange': '#D35400',
                        'cream': '#FFF5E1',
                        'charcoal': '#2C2C2C',
                        'sage-green': '#A3B18A'
                    },
                    fontFamily: {
                        'sans': ['Inter', 'system-ui', 'sans-serif'],
                    },
                    spacing: {
                        '18': '4.5rem',
                        '88': '22rem',
                    },
                    animation: {
                        'fade-in': 'fadeIn 0.5s ease-in-out',
                        'slide-down': 'slideDown 0.3s ease-out',
                        'slide-up': 'slideUp 0.3s ease-out',
                    },
                    keyframes: {
                        fadeIn: {
                            '0%': { opacity: '0' },
                            '100%': { opacity: '1' },
                        },
                        slideDown: {
                            '0%': { transform: 'translateY(-10px)', opacity: '0' },
                            '100%': { transform: 'translateY(0)', opacity: '1' },
                        },
                        slideUp: {
                            '0%': { transform: 'translateY(10px)', opacity: '0' },
                            '100%': { transform: 'translateY(0)', opacity: '1' },
                        }
                    }
                }
            }
        }
    </script>
    
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- HTMX -->
    <script src="https://unpkg.com/htmx.org@1.9.10"></script>
    
    <!-- Unpoly -->
    <script src="https://unpkg.com/unpoly@3/unpoly.min.js"></script>
    <link rel="stylesheet" href="https://unpkg.com/unpoly@3/unpoly.min.css">
    
    <!-- Unpoly Configuration -->
    <script>
        up.configure({
            fragmentTransition: 'cross-fade',
            duration: 300,
            easing: 'ease'
        });
    </script>
    
    <!-- Alpine.js -->
    <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>
    
    {% block extra_head %}{% endblock %}
</head>
<body class="h-full bg-cream text-charcoal font-sans antialiased" x-data="{ mobileMenuOpen: false }">
    
    <!-- Mobile Navigation -->
    <nav class="bg-deep-olive text-white shadow-lg lg:hidden relative z-50">
        <div class="px-4 py-3">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <!-- Logo placeholder - can add actual logo here -->
                        <div class="w-8 h-8 bg-burnt-orange rounded-full flex items-center justify-center mr-3">
                            <span class="text-white font-bold text-sm">BA</span>
                        </div>
                    </div>
                    <h1 class="text-xl font-bold">BonAppetit</h1>
                </div>
                <button @click="mobileMenuOpen = !mobileMenuOpen" 
                        class="inline-flex items-center justify-center p-2 rounded-md hover:bg-sage-green focus:outline-none focus:ring-2 focus:ring-white transition-colors duration-200"
                        :aria-expanded="mobileMenuOpen"
                        aria-label="Toggle navigation menu">
                    <svg class="h-6 w-6 transition-transform duration-200" 
                         :class="{ 'rotate-90': mobileMenuOpen }"
                         stroke="currentColor" fill="none" viewBox="0 0 24 24">
                        <path :class="{'hidden': mobileMenuOpen, 'inline-flex': !mobileMenuOpen }" 
                              class="inline-flex" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                              d="M4 6h16M4 12h16M4 18h16" />
                        <path :class="{'hidden': !mobileMenuOpen, 'inline-flex': mobileMenuOpen }" 
                              class="hidden" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                              d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
        </div>
        
        <!-- Mobile Menu -->
        <div x-show="mobileMenuOpen" 
             x-transition:enter="transition ease-out duration-200"
             x-transition:enter-start="opacity-0 transform -translate-y-2"
             x-transition:enter-end="opacity-100 transform translate-y-0"
             x-transition:leave="transition ease-in duration-150"
             x-transition:leave-start="opacity-100 transform translate-y-0"
             x-transition:leave-end="opacity-0 transform -translate-y-2"
             class="px-2 pt-2 pb-3 space-y-1 bg-deep-olive border-t border-sage-green">
            <a href="/" up-nav @click="mobileMenuOpen = false" 
               class="block px-3 py-3 rounded-md text-base font-medium hover:bg-sage-green transition-colors duration-200 active:bg-burnt-orange">
               Home
            </a>
            <a href="/booking/" up-nav @click="mobileMenuOpen = false"
               class="block px-3 py-3 rounded-md text-base font-medium hover:bg-sage-green transition-colors duration-200 active:bg-burnt-orange">
               Book Now
            </a>
            <a href="/menu/" up-nav @click="mobileMenuOpen = false"
               class="block px-3 py-3 rounded-md text-base font-medium hover:bg-sage-green transition-colors duration-200 active:bg-burnt-orange">
               Menu
            </a>
            <a href="/services/" up-nav @click="mobileMenuOpen = false"
               class="block px-3 py-3 rounded-md text-base font-medium hover:bg-sage-green transition-colors duration-200 active:bg-burnt-orange">
               Services
            </a>
            <a href="/contact/" up-nav @click="mobileMenuOpen = false"
            class="block px-3 py-3 rounded-md text-base font-medium hover:bg-sage-green transition-colors duration-200 active:bg-burnt-orange">
            Contact
            </a>
            {% if user.is_authenticated %}
            {% if user.is_staff %}
            <a href="{% url 'savory_events:admin_dashboard' %}" up-nav @click="mobileMenuOpen = false"
            class="block px-3 py-3 rounded-md text-base font-medium bg-burnt-orange hover:bg-opacity-80 transition-colors duration-200">
            Admin Dashboard
            </a>
                        {% else %}
                            <a href="{% url 'savory_events:customer_dashboard' %}" up-nav @click="mobileMenuOpen = false"
                               class="block px-3 py-3 rounded-md text-base font-medium bg-sage-green hover:bg-opacity-80 transition-colors duration-200">
                                My Dashboard
                            </a>
                        {% endif %}
                        <a href="{% url 'savory_events:logout' %}" @click="mobileMenuOpen = false"
                           class="block px-3 py-3 rounded-md text-base font-medium text-red-600 hover:bg-red-50 transition-colors duration-200">
                            Sign Out
                        </a>
                    {% else %}
                        <a href="{% url 'savory_events:login' %}" up-nav @click="mobileMenuOpen = false"
                           class="block px-3 py-3 rounded-md text-base font-medium bg-burnt-orange hover:bg-opacity-80 transition-colors duration-200">
                            Sign In
                        </a>
                        <a href="{% url 'savory_events:register' %}" up-nav @click="mobileMenuOpen = false"
                           class="block px-3 py-3 rounded-md text-base font-medium border border-burnt-orange text-burnt-orange hover:bg-burnt-orange hover:text-white transition-colors duration-200">
                            Sign Up
                        </a>
                    {% endif %}
        </div>
    </nav>
    
    <!-- Desktop Navigation -->
    <nav class="hidden lg:block bg-deep-olive text-white shadow-lg relative z-40">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-18">
                <div class="flex items-center">
                    <div class="flex-shrink-0 mr-4">
                        <!-- Logo placeholder - can add actual logo here -->
                        <div class="w-10 h-10 bg-burnt-orange rounded-full flex items-center justify-center">
                            <span class="text-white font-bold">BA</span>
                        </div>
                    </div>
                    <h1 class="text-2xl font-bold">BonAppetit</h1>
                    <span class="ml-2 text-sm text-sage-green font-medium">Catering Services</span>
                </div>
                <div class="flex items-center space-x-6">
                    <a href="/" up-nav 
                       class="px-4 py-2 rounded-md text-sm font-medium hover:bg-sage-green focus:bg-sage-green focus:outline-none transition-colors duration-200 group">
                       <span class="group-hover:text-cream">Home</span>
                    </a>
                    <a href="/booking/" up-nav 
                       class="px-4 py-2 rounded-md text-sm font-medium hover:bg-sage-green focus:bg-sage-green focus:outline-none transition-colors duration-200 group">
                       <span class="group-hover:text-cream">Book Now</span>
                    </a>
                    <a href="/menu/" up-nav 
                       class="px-4 py-2 rounded-md text-sm font-medium hover:bg-sage-green focus:bg-sage-green focus:outline-none transition-colors duration-200 group">
                       <span class="group-hover:text-cream">Menu</span>
                    </a>
                    <a href="/services/" up-nav 
                       class="px-4 py-2 rounded-md text-sm font-medium hover:bg-sage-green focus:bg-sage-green focus:outline-none transition-colors duration-200 group">
                       <span class="group-hover:text-cream">Services</span>
                    </a>
                    <a href="/contact/" up-nav 
                    class="px-4 py-2 rounded-md text-sm font-medium hover:bg-sage-green focus:bg-sage-green focus:outline-none transition-colors duration-200 group">
                    <span class="group-hover:text-cream">Contact</span>
                    </a>
                    {% if user.is_authenticated %}
                    {% if user.is_staff %}
                    <a href="{% url 'savory_events:admin_dashboard' %}" up-nav 
                    class="bg-burnt-orange px-4 py-2 rounded-md text-sm font-medium hover:bg-opacity-80 focus:bg-opacity-80 focus:outline-none transition-all duration-200 shadow-md hover:shadow-lg">
                    Admin Dashboard
                    </a>
                        {% else %}
                            <a href="{% url 'savory_events:customer_dashboard' %}" up-nav 
                               class="bg-sage-green px-4 py-2 rounded-md text-sm font-medium hover:bg-opacity-80 focus:bg-opacity-80 focus:outline-none transition-all duration-200 shadow-md hover:shadow-lg">
                                My Dashboard
                            </a>
                        {% endif %}
                        <a href="{% url 'savory_events:logout' %}" 
                           class="px-4 py-2 rounded-md text-sm font-medium text-red-600 hover:bg-red-50 focus:bg-red-50 focus:outline-none transition-colors duration-200">
                            Sign Out
                        </a>
                    {% else %}
                        <a href="{% url 'savory_events:login' %}" up-nav 
                           class="px-4 py-2 rounded-md text-sm font-medium hover:bg-sage-green focus:bg-sage-green focus:outline-none transition-colors duration-200 group">
                            <span class="group-hover:text-cream">Sign In</span>
                        </a>
                        <a href="{% url 'savory_events:register' %}" up-nav 
                           class="bg-burnt-orange px-4 py-2 rounded-md text-sm font-medium hover:bg-opacity-80 focus:bg-opacity-80 focus:outline-none transition-all duration-200 shadow-md hover:shadow-lg">
                            Sign Up
                        </a>
                    {% endif %}
                </div>
            </div>
        </div>
    </nav>
    
    <!-- Main Content -->
    <main class="min-h-screen flex flex-col">
        <!-- Breadcrumb Navigation (optional) -->
        {% block breadcrumbs %}{% endblock %}
        
        <!-- Page Content -->
        <div class="flex">
            {% if is_customer %}
                <div class="hidden md:block md:w-64 md:flex-shrink-0">
                    {% include 'customer/sidebar.html' %}
                </div>
            {% endif %}
            <div class="flex-1">
                {% block content %}{% endblock %}
            </div>
        </div>
    </main>

    <!-- Chat Widget -->
    {% include 'chat/chat_widget.html' %}

    <!-- Footer -->
    <footer class="bg-charcoal text-cream py-12 mt-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8">
                <div class="col-span-1 lg:col-span-2">
                    <div class="flex items-center mb-4">
                        <div class="w-8 h-8 bg-burnt-orange rounded-full flex items-center justify-center mr-3">
                            <span class="text-white font-bold text-sm">BA</span>
                        </div>
                        <h3 class="text-xl font-bold">BonAppetit Catering</h3>
                    </div>
                    <p class="text-sm text-gray-300 mb-4 max-w-md">
                        Professional catering services for all your special occasions. We bring culinary excellence 
                        to weddings, corporate events, and celebrations of all sizes.
                    </p>
                    <div class="flex space-x-4">
                        <!-- Social media links placeholder -->
                        <a href="#" class="text-sage-green hover:text-burnt-orange transition-colors duration-200">
                            <span class="sr-only">Facebook</span>
                            <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z"/>
                            </svg>
                        </a>
                        <a href="#" class="text-sage-green hover:text-burnt-orange transition-colors duration-200">
                            <span class="sr-only">Instagram</span>
                            <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 6.62 5.367 11.987 11.988 11.987c6.62 0 11.987-5.367 11.987-11.987C24.014 5.367 18.637.001 12.017.001zM8.449 16.988c-1.297 0-2.448-.49-3.328-1.297L6.878 14.005c.525.613 1.297 1.006 2.164 1.006c1.503 0 2.448-1.094 2.448-2.597c0-1.503-.945-2.597-2.448-2.597c-.867 0-1.639.393-2.164 1.006L5.121 9.137c.88-.807 2.031-1.297 3.328-1.297c2.556 0 4.592 2.036 4.592 4.592S11.005 16.988 8.449 16.988z"/>
                            </svg>
                        </a>
                    </div>
                </div>
                
                <div>
                    <h3 class="text-lg font-semibold mb-4">Quick Links</h3>
                    <ul class="space-y-3 text-sm">
                        <li><a href="/booking/" class="hover:text-burnt-orange transition-colors duration-200">Book Now</a></li>
                        <li><a href="/menu/" class="hover:text-burnt-orange transition-colors duration-200">View Menu</a></li>
                        <li><a href="/services/" class="hover:text-burnt-orange transition-colors duration-200">Our Services</a></li>
                        <li><a href="/contact/" class="hover:text-burnt-orange transition-colors duration-200">Contact Us</a></li>
                    </ul>
                </div>
                
                <div>
                    <h3 class="text-lg font-semibold mb-4">Contact Info</h3>
                    <div class="space-y-3 text-sm">
                        <div class="flex items-center">
                            <svg class="h-4 w-4 mr-2 text-sage-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
                            </svg>
                            <span><EMAIL></span>
                        </div>
                        <div class="flex items-center">
                            <svg class="h-4 w-4 mr-2 text-sage-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"/>
                            </svg>
                            <span>(555) 123-4567</span>
                        </div>
                        <div class="flex items-start">
                            <svg class="h-4 w-4 mr-2 mt-0.5 text-sage-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                            </svg>
                            <div>
                                <div>Mon-Fri: 9AM-6PM</div>
                                <div>Sat: 10AM-4PM</div>
                                <div>Sun: Closed</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="border-t border-sage-green mt-8 pt-6">
                <div class="flex flex-col sm:flex-row justify-between items-center text-sm">
                    <p class="text-gray-300">&copy; 2025 BonAppetit Catering Services. All rights reserved.</p>
                    <div class="flex space-x-4 mt-4 sm:mt-0">
                        <a href="#" class="text-gray-300 hover:text-burnt-orange transition-colors duration-200">Privacy Policy</a>
                        <a href="#" class="text-gray-300 hover:text-burnt-orange transition-colors duration-200">Terms of Service</a>
                    </div>
                </div>
            </div>
        </div>
    </footer>
    
    <!-- HTMX Configuration -->
    <script>
        document.body.addEventListener('htmx:configRequest', (event) => {
            const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]');
            if (csrfToken) {
                event.detail.headers['X-CSRFToken'] = csrfToken.value;
            }
        });
        
        // Add loading indicators for HTMX requests
        document.body.addEventListener('htmx:beforeRequest', (event) => {
            const target = event.target;
            if (target.classList.contains('htmx-indicator')) {
                target.style.opacity = '0.7';
            }
        });
        
        document.body.addEventListener('htmx:afterRequest', (event) => {
            const target = event.target;
            if (target.classList.contains('htmx-indicator')) {
                target.style.opacity = '1';
            }
        });
        
        // Close mobile menu when clicking outside
        document.addEventListener('click', (event) => {
            const mobileMenu = document.querySelector('[x-data]');
            const menuButton = document.querySelector('[aria-expanded]');
            
            if (mobileMenu && menuButton && !mobileMenu.contains(event.target)) {
                // Alpine.js will handle this through x-data
            }
        });
        
        // Improve focus management for accessibility
        document.addEventListener('keydown', (event) => {
            if (event.key === 'Escape') {
                // Close mobile menu on escape
                const menuButton = document.querySelector('[aria-expanded="true"]');
                if (menuButton) {
                    menuButton.click();
                }
            }
        });
    </script>
    
    {% block extra_scripts %}{% endblock %}
</body>
</html>
