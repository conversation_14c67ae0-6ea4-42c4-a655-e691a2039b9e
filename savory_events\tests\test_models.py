from django.test import TestCase
from django.core.exceptions import ValidationError
from django.utils import timezone
from decimal import Decimal
from datetime import datetime, timedelta

from savory_events.models import (
    Customer, MenuItem, SoundSystemService, Booking, 
    Payment, BookingMenuItem, BookingSoundSystem
)


class CustomerModelTest(TestCase):
    """Test cases for Customer model."""
    
    def setUp(self):
        self.valid_customer_data = {
            'name': '<PERSON>',
            'email': '<EMAIL>',
            'phone': '(*************'
        }
    
    def test_customer_creation(self):
        """Test creating a customer with valid data."""
        customer = Customer.objects.create(**self.valid_customer_data)
        self.assertEqual(customer.name, '<PERSON>')
        self.assertEqual(customer.email, '<EMAIL>')
        self.assertEqual(customer.phone, '(*************')
        self.assertTrue(customer.created_at)
        self.assertTrue(customer.updated_at)
    
    def test_customer_str_representation(self):
        """Test customer string representation."""
        customer = Customer.objects.create(**self.valid_customer_data)
        expected = "<PERSON> (<EMAIL>)"
        self.assertEqual(str(customer), expected)
    
    def test_customer_name_validation(self):
        """Test customer name validation."""
        # Test short name
        customer = Customer(name='A', email='<EMAIL>', phone='123456789')
        with self.assertRaises(ValidationError):
            customer.full_clean()
    
    def test_customer_phone_validation(self):
        """Test customer phone validation."""
        # Test invalid phone format
        customer = Customer(name='John Doe', email='<EMAIL>', phone='abc')
        with self.assertRaises(ValidationError):
            customer.full_clean()
        
        # Test short phone number
        customer = Customer(name='John Doe', email='<EMAIL>', phone='123')
        with self.assertRaises(ValidationError):
            customer.full_clean()
    
    def test_customer_email_validation(self):
        """Test customer email validation."""
        customer = Customer(name='John Doe', email='invalid-email', phone='1234567890')
        with self.assertRaises(ValidationError):
            customer.full_clean()


class MenuItemModelTest(TestCase):
    """Test cases for MenuItem model."""
    
    def setUp(self):
        self.valid_menu_item_data = {
            'name': 'Grilled Salmon',
            'description': 'Fresh salmon grilled to perfection',
            'category': 'main',
            'price_per_person': Decimal('25.50')
        }
    
    def test_menu_item_creation(self):
        """Test creating a menu item with valid data."""
        item = MenuItem.objects.create(**self.valid_menu_item_data)
        self.assertEqual(item.name, 'Grilled Salmon')
        self.assertEqual(item.category, 'main')
        self.assertEqual(item.price_per_person, Decimal('25.50'))
        self.assertTrue(item.is_available)
    
    def test_menu_item_str_representation(self):
        """Test menu item string representation."""
        item = MenuItem.objects.create(**self.valid_menu_item_data)
        expected = "Grilled Salmon (Main Dishes) - $25.50"
        self.assertEqual(str(item), expected)
    
    def test_menu_item_price_validation(self):
        """Test menu item price validation."""
        item = MenuItem(
            name='Test Item',
            description='Test description',
            category='main',
            price_per_person=Decimal('0.00')
        )
        with self.assertRaises(ValidationError):
            item.full_clean()
    
    def test_calculate_total_price(self):
        """Test menu item total price calculation."""
        item = MenuItem.objects.create(**self.valid_menu_item_data)
        total = item.calculate_total_price(10)
        self.assertEqual(total, Decimal('255.00'))


class SoundSystemServiceModelTest(TestCase):
    """Test cases for SoundSystemService model."""
    
    def setUp(self):
        self.valid_service_data = {
            'name': 'Professional DJ Package',
            'package_type': 'dj',
            'description': 'Complete DJ service with sound system',
            'price': Decimal('400.00')
        }
    
    def test_sound_system_creation(self):
        """Test creating a sound system service."""
        service = SoundSystemService.objects.create(**self.valid_service_data)
        self.assertEqual(service.name, 'Professional DJ Package')
        self.assertEqual(service.package_type, 'dj')
        self.assertEqual(service.price, Decimal('400.00'))
        self.assertTrue(service.is_available)
    
    def test_sound_system_str_representation(self):
        """Test sound system string representation."""
        service = SoundSystemService.objects.create(**self.valid_service_data)
        expected = "Professional DJ Package (DJ Package) - $400.00"
        self.assertEqual(str(service), expected)


class BookingModelTest(TestCase):
    """Test cases for Booking model."""
    
    def setUp(self):
        self.customer = Customer.objects.create(
            name='Jane Smith',
            email='<EMAIL>',
            phone='(*************'
        )
        
        self.menu_item = MenuItem.objects.create(
            name='Chicken Marsala',
            description='Pan-seared chicken in marsala sauce',
            category='main',
            price_per_person=Decimal('24.00')
        )
        
        self.sound_system = SoundSystemService.objects.create(
            name='Basic Microphone Setup',
            package_type='microphones',
            description='Basic microphone setup',
            price=Decimal('150.00')
        )
        
        self.future_date = timezone.now() + timedelta(days=30)
        
        self.valid_booking_data = {
            'customer': self.customer,
            'event_date': self.future_date,
            'event_type': 'wedding',
            'guest_count': 50,
            'venue_address': '123 Wedding Venue Lane'
        }
    
    def test_booking_creation(self):
        """Test creating a booking with valid data."""
        booking = Booking.objects.create(**self.valid_booking_data)
        self.assertEqual(booking.customer, self.customer)
        self.assertEqual(booking.event_type, 'wedding')
        self.assertEqual(booking.guest_count, 50)
        self.assertEqual(booking.status, 'pending')
    
    def test_booking_str_representation(self):
        """Test booking string representation."""
        booking = Booking.objects.create(**self.valid_booking_data)
        expected = f"Jane Smith - Wedding on {self.future_date.strftime('%Y-%m-%d')}"
        self.assertEqual(str(booking), expected)
    
    def test_booking_event_date_validation(self):
        """Test booking event date validation."""
        past_date = timezone.now() - timedelta(days=1)
        booking = Booking(
            customer=self.customer,
            event_date=past_date,
            event_type='wedding',
            guest_count=50,
            venue_address='123 Test Address'
        )
        with self.assertRaises(ValidationError):
            booking.full_clean()
    
    def test_booking_guest_count_validation(self):
        """Test booking guest count validation."""
        booking = Booking(
            customer=self.customer,
            event_date=self.future_date,
            event_type='wedding',
            guest_count=0,
            venue_address='123 Test Address'
        )
        with self.assertRaises(ValidationError):
            booking.full_clean()
    
    def test_booking_total_calculation(self):
        """Test booking total amount calculation."""
        booking = Booking.objects.create(**self.valid_booking_data)
        
        # Add menu item
        BookingMenuItem.objects.create(
            booking=booking,
            menu_item=self.menu_item,
            quantity=1
        )
        
        # Add sound system
        BookingSoundSystem.objects.create(
            booking=booking,
            sound_system=self.sound_system
        )
        
        # Calculate expected total: (24.00 * 50) + 150.00 = 1350.00
        expected_total = Decimal('1350.00')
        calculated_total = booking.calculate_total_amount()
        self.assertEqual(calculated_total, expected_total)
    
    def test_deposit_calculation(self):
        """Test deposit amount calculation."""
        booking = Booking.objects.create(**self.valid_booking_data)
        booking.total_amount = Decimal('1000.00')
        
        deposit = booking.get_deposit_amount()
        self.assertEqual(deposit, Decimal('500.00'))
    
    def test_payment_status_methods(self):
        """Test payment status checking methods."""
        booking = Booking.objects.create(**self.valid_booking_data)
        booking.total_amount = Decimal('1000.00')
        booking.save()
        
        # Initially no payments
        self.assertEqual(booking.get_total_paid(), Decimal('0.00'))
        self.assertEqual(booking.get_remaining_balance(), Decimal('1000.00'))
        self.assertFalse(booking.is_deposit_paid())
        self.assertFalse(booking.is_fully_paid())
        
        # Add deposit payment
        Payment.objects.create(
            booking=booking,
            amount=Decimal('500.00'),
            payment_type='deposit'
        )
        
        self.assertEqual(booking.get_total_paid(), Decimal('500.00'))
        self.assertEqual(booking.get_remaining_balance(), Decimal('500.00'))
        self.assertTrue(booking.is_deposit_paid())
        self.assertFalse(booking.is_fully_paid())


class PaymentModelTest(TestCase):
    """Test cases for Payment model."""
    
    def setUp(self):
        self.customer = Customer.objects.create(
            name='Test Customer',
            email='<EMAIL>',
            phone='1234567890'
        )
        
        self.booking = Booking.objects.create(
            customer=self.customer,
            event_date=timezone.now() + timedelta(days=30),
            event_type='corporate',
            guest_count=25,
            venue_address='123 Corporate Plaza',
            total_amount=Decimal('500.00')
        )
    
    def test_payment_creation(self):
        """Test creating a payment."""
        payment = Payment.objects.create(
            booking=self.booking,
            amount=Decimal('250.00'),
            payment_type='deposit',
            notes='Initial deposit'
        )
        
        self.assertEqual(payment.booking, self.booking)
        self.assertEqual(payment.amount, Decimal('250.00'))
        self.assertEqual(payment.payment_type, 'deposit')
        self.assertTrue(payment.payment_date)
    
    def test_payment_str_representation(self):
        """Test payment string representation."""
        payment = Payment.objects.create(
            booking=self.booking,
            amount=Decimal('250.00'),
            payment_type='deposit'
        )
        
        expected = "Test Customer - $250.00 (Deposit (50%))"
        self.assertEqual(str(payment), expected)
    
    def test_payment_amount_validation(self):
        """Test payment amount validation."""
        # Test negative amount
        payment = Payment(
            booking=self.booking,
            amount=Decimal('-100.00'),
            payment_type='deposit'
        )
        with self.assertRaises(ValidationError):
            payment.full_clean()
        
        # Test amount exceeding booking total
        payment = Payment(
            booking=self.booking,
            amount=Decimal('600.00'),
            payment_type='deposit'
        )
        with self.assertRaises(ValidationError):
            payment.full_clean()
    
    def test_payment_status_update(self):
        """Test that payment updates booking status."""
        # Create deposit payment
        Payment.objects.create(
            booking=self.booking,
            amount=Decimal('250.00'),
            payment_type='deposit'
        )
        
        self.booking.refresh_from_db()
        self.assertEqual(self.booking.status, 'partial_paid')
        
        # Create balance payment
        Payment.objects.create(
            booking=self.booking,
            amount=Decimal('250.00'),
            payment_type='balance'
        )
        
        self.booking.refresh_from_db()
        self.assertEqual(self.booking.status, 'fully_paid')


class BookingMenuItemModelTest(TestCase):
    """Test cases for BookingMenuItem model."""
    
    def setUp(self):
        self.customer = Customer.objects.create(
            name='Test Customer',
            email='<EMAIL>',
            phone='1234567890'
        )
        
        self.booking = Booking.objects.create(
            customer=self.customer,
            event_date=timezone.now() + timedelta(days=30),
            event_type='wedding',
            guest_count=100,
            venue_address='123 Wedding Venue'
        )
        
        self.menu_item = MenuItem.objects.create(
            name='Beef Tenderloin',
            description='Premium beef tenderloin',
            category='main',
            price_per_person=Decimal('35.00')
        )
    
    def test_booking_menu_item_creation(self):
        """Test creating a booking menu item relationship."""
        booking_item = BookingMenuItem.objects.create(
            booking=self.booking,
            menu_item=self.menu_item,
            quantity=2
        )
        
        self.assertEqual(booking_item.booking, self.booking)
        self.assertEqual(booking_item.menu_item, self.menu_item)
        self.assertEqual(booking_item.quantity, 2)
    
    def test_booking_menu_item_str_representation(self):
        """Test booking menu item string representation."""
        booking_item = BookingMenuItem.objects.create(
            booking=self.booking,
            menu_item=self.menu_item,
            quantity=1
        )
        
        expected = "Test Customer - Beef Tenderloin (x1)"
        self.assertEqual(str(booking_item), expected)
    
    def test_total_price_calculation(self):
        """Test total price calculation for booking menu item."""
        booking_item = BookingMenuItem.objects.create(
            booking=self.booking,
            menu_item=self.menu_item,
            quantity=1
        )
        
        # Total = price_per_person * guest_count * quantity
        # Total = 35.00 * 100 * 1 = 3500.00
        expected_total = Decimal('3500.00')
        self.assertEqual(booking_item.get_total_price(), expected_total)
