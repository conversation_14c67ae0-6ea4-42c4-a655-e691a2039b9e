{% extends 'management/base.html' %}

{% block title %}Analytics & Reports{% endblock %}
{% block page_title %}Analytics & Reports{% endblock %}

{% block content %}
<!-- Analytics Overview -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
    
    <!-- Revenue Chart -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Monthly Revenue ({{ current_year }})</h3>
        </div>
        <div class="px-6 py-4">
            <canvas id="revenueChart" width="400" height="200"></canvas>
        </div>
    </div>

    <!-- Event Type Distribution -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Event Type Distribution</h3>
        </div>
        <div class="px-6 py-4">
            <canvas id="eventTypeChart" width="400" height="200"></canvas>
        </div>
    </div>
</div>

<!-- Monthly Data Table -->
<div class="bg-white shadow rounded-lg mb-8">
    <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900">Monthly Performance</h3>
    </div>
    <div class="overflow-hidden">
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Month</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Bookings</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Revenue</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Avg. Booking Value</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    {% for month_data in monthly_data %}
                    <tr class="hover:bg-gray-50">
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                            {{ month_data.month_name }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {{ month_data.bookings }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            ${{ month_data.revenue|floatformat:2 }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {% if month_data.bookings > 0 %}
                                ${% widthratio month_data.revenue month_data.bookings 1 %}
                            {% else %}
                                $0.00
                            {% endif %}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Event Analysis and Popular Items -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
    
    <!-- Event Analysis -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Event Type Analysis</h3>
        </div>
        <div class="overflow-hidden">
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Event Type</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Count</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Revenue</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Avg. Value</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {% for event in event_analysis %}
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                {{ event.event_type|title }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {{ event.count }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                ${{ event.total_revenue|default:0|floatformat:2 }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                ${{ event.avg_value|default:0|floatformat:2 }}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Popular Menu Items -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Popular Menu Items</h3>
        </div>
        <div class="overflow-hidden">
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Orders</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Quantity</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Revenue</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {% for item in popular_items %}
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">{{ item.menu_item__name }}</div>
                                <div class="text-sm text-gray-500">{{ item.menu_item__category|title }}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {{ item.total_orders }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {{ item.total_quantity }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                ${{ item.total_revenue|default:0|floatformat:2 }}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Customer Insights -->
<div class="bg-white shadow rounded-lg">
    <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900">Customer Insights</h3>
    </div>
    <div class="px-6 py-4">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            
            <!-- Customer Stats -->
            <div class="text-center">
                <div class="text-3xl font-bold text-deep-olive">{{ customer_insights.repeat_customers }}</div>
                <div class="text-sm text-gray-500">Repeat Customers</div>
            </div>
            
            <div class="text-center">
                <div class="text-3xl font-bold text-deep-olive">${{ customer_insights.avg_customer_value|floatformat:0 }}</div>
                <div class="text-sm text-gray-500">Avg. Customer Value</div>
            </div>
            
            <div class="text-center">
                <div class="text-3xl font-bold text-deep-olive">{{ customer_insights.top_customers|length }}</div>
                <div class="text-sm text-gray-500">VIP Customers</div>
            </div>
        </div>
        
        <!-- Top Customers -->
        {% if customer_insights.top_customers %}
        <div class="mt-8">
            <h4 class="text-lg font-medium text-gray-900 mb-4">Top Customers</h4>
            <div class="space-y-3">
                {% for customer in customer_insights.top_customers %}
                <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div class="flex items-center">
                        <div class="h-10 w-10 rounded-full bg-deep-olive flex items-center justify-center mr-3">
                            <span class="text-white font-medium text-sm">
                                {{ customer.name|first|upper }}
                            </span>
                        </div>
                        <div>
                            <div class="text-sm font-medium text-gray-900">{{ customer.name }}</div>
                            <div class="text-sm text-gray-500">{{ customer.email }}</div>
                        </div>
                    </div>
                    <div class="text-right">
                        <div class="text-lg font-medium text-gray-900">${{ customer.total_spent|floatformat:2 }}</div>
                        <a href="{% url 'savory_events:management_customer_detail' customer.id %}" 
                           class="text-sm text-deep-olive hover:text-burnt-orange">
                            View Profile
                        </a>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
        {% endif %}
    </div>
</div>

{% csrf_token %}
{% endblock %}

{% block extra_scripts %}
<script>
// Revenue Chart
const revenueCtx = document.getElementById('revenueChart').getContext('2d');
const revenueChart = new Chart(revenueCtx, {
    type: 'line',
    data: {
        labels: [
            {% for month_data in monthly_data %}
            '{{ month_data.month_name }}'{% if not forloop.last %},{% endif %}
            {% endfor %}
        ],
        datasets: [{
            label: 'Revenue',
            data: [
                {% for month_data in monthly_data %}
                {{ month_data.revenue|floatformat:2 }}{% if not forloop.last %},{% endif %}
                {% endfor %}
            ],
            borderColor: '#556B2F',
            backgroundColor: 'rgba(85, 107, 47, 0.1)',
            tension: 0.4,
            fill: true
        }]
    },
    options: {
        responsive: true,
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    callback: function(value) {
                        return '$' + value.toLocaleString();
                    }
                }
            }
        },
        plugins: {
            legend: {
                display: false
            }
        }
    }
});

// Event Type Chart
const eventTypeCtx = document.getElementById('eventTypeChart').getContext('2d');
const eventTypeChart = new Chart(eventTypeCtx, {
    type: 'doughnut',
    data: {
        labels: [
            {% for event in event_analysis %}
            '{{ event.event_type|title }}'{% if not forloop.last %},{% endif %}
            {% endfor %}
        ],
        datasets: [{
            data: [
                {% for event in event_analysis %}
                {{ event.count }}{% if not forloop.last %},{% endif %}
                {% endfor %}
            ],
            backgroundColor: [
                '#556B2F',
                '#D35400',
                '#3498DB',
                '#E74C3C',
                '#9B59B6',
                '#F39C12'
            ]
        }]
    },
    options: {
        responsive: true,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});
</script>
{% endblock %}
