Design the catering services web application with a mobile-first approach using Tailwind CSS. Ensure all components are optimized for small screen sizes and scale responsively to tablets and desktops. Focus on interactive UI/UX, fast response times, and seamless navigation for all users.

🎯 Core Features Specification
1. Customer Booking & Reservation System
Customers can select an event date and time

Provide basic information (Name, Contact, Event Type)

Choose from available packages or create custom bookings

2. Menu Selection System
Display categories: Appetizers, Main Dishes, Desserts, Drinks

Each menu item has image, description, and selection checkbox

Summary of selected menu with total estimation

3. Partial Payment Handling (50%)
Upon booking, the system notifies the customer to hand in a 50% downpayment

Admin interface to log the received 50% payment

Once entered, the system automatically tracks the remaining balance and updates booking status

4. Sound System Reservation
Optional sound system services selectable during booking

Show available packages (e.g., DJ, Microphones, Full Audio Set)

Integrated in the same booking form with conditional logic

5. Admin Dashboard with Collapsible Sidebar
Mobile-responsive sidebar dashboard

Sidebar is collapsible and toggleable on small screens using Alpine.js

HTMX and Unpoly power partial updates and modal views

Admin sees list of bookings, payments, upcoming events, and customer selections

🎨 Color Palette & Theme (Inspired by Gourmet Catering)
Element	Color Name	HEX
Primary	Deep Olive	#556B2F
Accent	Burnt Orange	#D35400
Background	Cream	#FFF5E1
Text	Charcoal	#2C2C2C
Secondary Accent	Sage Green	#A3B18A

Use this warm and elegant color palette across the app for a classy and appetizing visual experience.

🧩 Stack Guidelines
Frontend:
Tailwind CSS: For utility-first styling and responsive layouts.

HTMX: For dynamic content swapping (e.g., modals, inline updates for booking status).

Unpoly: For progressive enhancement and smooth page transitions without full reloads.

Alpine.js CDN: For collapsible sidebar, toggles, interactivity in mobile view.

Backend:
Django Views/Models:

Booking, MenuItem, Payment, SoundSystemService, Customer

Implement Django forms with HTMX support for AJAX-based actions.

Django admin or custom dashboard for staff to manage payments and bookings.