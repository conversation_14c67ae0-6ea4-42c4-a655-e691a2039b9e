{% extends 'customer/base.html' %}

{% block title %}Booking Wizard - BonAppetit Catering{% endblock %}

{% block page_title %}Booking Wizard{% endblock %}

{% block page_subtitle %}Step-by-step guided booking process for your catering event{% endblock %}

{% block content %}
<div class="py-8">
    {% csrf_token %}
    <div class="bg-white rounded-lg shadow-lg p-6 md:p-8" x-data="bookingWizard({{ menu_items|safe }})">
        
        <!-- Progress Bar -->
        <div class="mb-8">
            <div class="flex items-center justify-between mb-2">
                <span class="text-sm font-medium text-charcoal">Step <span x-text="currentStep"></span> of 4</span>
                <span class="text-sm text-gray-500" x-text="Math.round((currentStep / 4) * 100) + '%'"></span>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-2">
                <div class="bg-burnt-orange h-2 rounded-full transition-all duration-300" 
                     :style="'width: ' + (currentStep / 4) * 100 + '%'"></div>
            </div>
        </div>
        
        <!-- Step 1: Event Details -->
        <div x-show="currentStep === 1" x-transition class="space-y-6">
            <h2 class="text-2xl font-bold text-charcoal mb-6">Event Details</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label class="block text-sm font-medium text-charcoal mb-2">Event Type *</label>
                    <select x-model="eventData.event_type" required
                            class="w-full px-3 py-2 border border-sage-green rounded-md focus:outline-none focus:ring-2 focus:ring-burnt-orange">
                        <option value="">Select Event Type</option>
                        <option value="wedding">Wedding</option>
                        <option value="corporate">Corporate Event</option>
                        <option value="birthday">Birthday Party</option>
                        <option value="anniversary">Anniversary</option>
                        <option value="other">Other</option>
                    </select>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-charcoal mb-2">Number of Guests *</label>
                    <input type="number" x-model="eventData.guest_count" required min="1" max="1000"
                           class="w-full px-3 py-2 border border-sage-green rounded-md focus:outline-none focus:ring-2 focus:ring-burnt-orange">
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-charcoal mb-2">Event Date *</label>
                    <input type="date" x-model="eventData.event_date" required
                           class="w-full px-3 py-2 border border-sage-green rounded-md focus:outline-none focus:ring-2 focus:ring-burnt-orange">
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-charcoal mb-2">Event Time *</label>
                    <input type="time" x-model="eventData.event_time" required
                           class="w-full px-3 py-2 border border-sage-green rounded-md focus:outline-none focus:ring-2 focus:ring-burnt-orange">
                </div>
                
                <div class="md:col-span-2">
                    <label class="block text-sm font-medium text-charcoal mb-2">Venue Address *</label>
                    <textarea x-model="eventData.venue_address" required rows="3"
                              class="w-full px-3 py-2 border border-sage-green rounded-md focus:outline-none focus:ring-2 focus:ring-burnt-orange"
                              placeholder="Enter the complete venue address..."></textarea>
                </div>
            </div>
        </div>
        
        <!-- Step 2: Menu Selection -->
        <div x-show="currentStep === 2" x-transition class="space-y-6">
            <h2 class="text-2xl font-bold text-charcoal mb-6">Menu Selection</h2>
            
            <!-- Category Filter -->
            <div class="flex flex-wrap gap-2 mb-6">
                <button @click="activeCategory = 'all'" 
                        :class="activeCategory === 'all' ? 'bg-burnt-orange text-white' : 'bg-gray-200 text-charcoal hover:bg-sage-green hover:text-white'"
                        class="px-4 py-2 rounded-md text-sm font-medium transition-colors">
                    All Items
                </button>
                <button @click="activeCategory = 'appetizer'" 
                        :class="activeCategory === 'appetizer' ? 'bg-burnt-orange text-white' : 'bg-gray-200 text-charcoal hover:bg-sage-green hover:text-white'"
                        class="px-4 py-2 rounded-md text-sm font-medium transition-colors">
                    Appetizers
                </button>
                <button @click="activeCategory = 'main'" 
                        :class="activeCategory === 'main' ? 'bg-burnt-orange text-white' : 'bg-gray-200 text-charcoal hover:bg-sage-green hover:text-white'"
                        class="px-4 py-2 rounded-md text-sm font-medium transition-colors">
                    Main Dishes
                </button>
                <button @click="activeCategory = 'dessert'" 
                        :class="activeCategory === 'dessert' ? 'bg-burnt-orange text-white' : 'bg-gray-200 text-charcoal hover:bg-sage-green hover:text-white'"
                        class="px-4 py-2 rounded-md text-sm font-medium transition-colors">
                    Desserts
                </button>
            </div>
            
            <!-- Menu Items -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <template x-for="item in filteredMenuItems" :key="item.id">
                    <div class="bg-cream rounded-lg p-4 border-2 transition-colors"
                         :class="selectedMenuItems.includes(item.id) ? 'border-burnt-orange bg-orange-50' : 'border-sage-green'">
                        <div class="flex items-center justify-between mb-2">
                            <h4 class="font-semibold text-charcoal" x-text="item.name"></h4>
                            <input type="checkbox" :value="item.id" 
                                   @change="toggleMenuItem(item.id)"
                                   :checked="selectedMenuItems.includes(item.id)"
                                   class="text-burnt-orange focus:ring-burnt-orange">
                        </div>
                        <p class="text-sm text-gray-600 mb-2" x-text="item.description"></p>
                        <p class="text-sm font-medium text-burnt-orange">$<span x-text="item.price.toFixed(2)"></span> per person</p>
                    </div>
                </template>
            </div>
            
            <!-- Selected Items Summary -->
            <div x-show="selectedMenuItems.length > 0" class="bg-white border border-sage-green rounded-lg p-4">
                <h4 class="font-semibold text-charcoal mb-2">Selected Items (<span x-text="selectedMenuItems.length"></span>)</h4>
                <div class="text-sm text-gray-600">
                    Estimated total: $<span x-text="calculateMenuTotal().toFixed(2)"></span> for <span x-text="eventData.guest_count || 0"></span> guests
                </div>
            </div>
        </div>
        
        <!-- Step 3: Additional Services -->
        <div x-show="currentStep === 3" x-transition class="space-y-6">
            <h2 class="text-2xl font-bold text-charcoal mb-6">Additional Services</h2>
            
            <div class="bg-cream rounded-lg p-6">
                <h3 class="text-lg font-semibold text-charcoal mb-4">Sound System (Optional)</h3>
                <div class="space-y-3">
                    <label class="flex items-center">
                        <input type="radio" x-model="eventData.sound_system" value="" class="mr-3">
                        <span>No sound system needed</span>
                    </label>
                    <label class="flex items-center">
                        <input type="radio" x-model="eventData.sound_system" value="microphones" class="mr-3">
                        <span>Basic microphone setup (+$150)</span>
                    </label>
                    <label class="flex items-center">
                        <input type="radio" x-model="eventData.sound_system" value="dj" class="mr-3">
                        <span>Full DJ setup (+$300)</span>
                    </label>
                </div>
            </div>
            
            <div class="bg-cream rounded-lg p-6">
                <h3 class="text-lg font-semibold text-charcoal mb-4">Special Requests</h3>
                <textarea x-model="eventData.special_requests" rows="4"
                          class="w-full px-3 py-2 border border-sage-green rounded-md focus:outline-none focus:ring-2 focus:ring-burnt-orange"
                          placeholder="Any dietary restrictions, special arrangements, or additional requests..."></textarea>
            </div>
        </div>
        
        <!-- Step 4: Review & Submit -->
        <div x-show="currentStep === 4" x-transition class="space-y-6">
            <h2 class="text-2xl font-bold text-charcoal mb-6">Review Your Booking</h2>
            
            <!-- Event Summary -->
            <div class="bg-cream rounded-lg p-6">
                <h3 class="text-lg font-semibold text-charcoal mb-4">Event Summary</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                    <div><strong>Event Type:</strong> <span x-text="eventData.event_type"></span></div>
                    <div><strong>Guests:</strong> <span x-text="eventData.guest_count"></span></div>
                    <div><strong>Date:</strong> <span x-text="eventData.event_date"></span></div>
                    <div><strong>Time:</strong> <span x-text="eventData.event_time"></span></div>
                    <div class="md:col-span-2"><strong>Venue:</strong> <span x-text="eventData.venue_address"></span></div>
                </div>
            </div>
            
            <!-- Final Total -->
            <div class="bg-white border border-sage-green rounded-lg p-6">
                <h3 class="text-lg font-semibold text-charcoal mb-4">Total Estimate</h3>
                <div class="text-xl font-bold text-burnt-orange">
                    $<span x-text="calculateTotal().toFixed(2)"></span>
                </div>
                <p class="text-sm text-gray-600 mt-2">This is an estimate. Final pricing will be confirmed after review.</p>
            </div>
        </div>
        
        <!-- Navigation Buttons -->
        <div class="flex justify-between mt-8">
            <button @click="previousStep()" 
                    x-show="currentStep > 1"
                    class="px-6 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors">
                <i class="fas fa-arrow-left mr-2"></i>Previous
            </button>
            
            <div class="flex-1"></div>
            
            <button @click="nextStep()" 
                    x-show="currentStep < 4"
                    :disabled="!canProceed()"
                    :class="canProceed() ? 'bg-burnt-orange hover:bg-opacity-80 text-white' : 'bg-gray-300 text-gray-500 cursor-not-allowed'"
                    class="px-6 py-2 rounded-md transition-colors">
                Next<i class="fas fa-arrow-right ml-2"></i>
            </button>
            
            <button @click="submitBooking()" 
                    x-show="currentStep === 4"
                    class="bg-burnt-orange hover:bg-opacity-80 text-white px-6 py-2 rounded-md transition-colors">
                <i class="fas fa-check mr-2"></i>Submit Booking
            </button>
        </div>
    </div>
</div>

<script>
function bookingWizard(menuItems) {
    return {
        currentStep: 1,
        activeCategory: 'all',
        menuItems: menuItems,
        selectedMenuItems: [],
        eventData: {
            customer_name: '{% if user.is_authenticated %}{{ user.get_full_name }}{% endif %}',
            customer_email: '{% if user.is_authenticated %}{{ user.email }}{% endif %}',
            customer_phone: '{% if customer.phone %}{{ customer.phone }}{% endif %}',
            event_type: '',
            guest_count: '',
            event_date: '',
            event_time: '',
            venue_address: '',
            sound_system: '',
            special_requests: ''
        },
        
        get filteredMenuItems() {
            if (this.activeCategory === 'all') {
                return this.menuItems;
            }
            return this.menuItems.filter(item => item.category === this.activeCategory);
        },
        
        toggleMenuItem(itemId) {
            const index = this.selectedMenuItems.indexOf(itemId);
            if (index > -1) {
                this.selectedMenuItems.splice(index, 1);
            } else {
                this.selectedMenuItems.push(itemId);
            }
        },
        
        calculateMenuTotal() {
            const guestCount = parseInt(this.eventData.guest_count) || 0;
            return this.selectedMenuItems.reduce((total, itemId) => {
                const item = this.menuItems.find(i => i.id === itemId);
                return total + (item ? item.price * guestCount : 0);
            }, 0);
        },
        
        calculateTotal() {
            let total = this.calculateMenuTotal();
            
            // Add sound system cost
            if (this.eventData.sound_system === 'microphones') {
                total += 150;
            } else if (this.eventData.sound_system === 'dj') {
                total += 300;
            }
            
            return total;
        },
        
        canProceed() {
            switch (this.currentStep) {
                case 1:
                    return this.eventData.event_type && this.eventData.guest_count && 
                           this.eventData.event_date && this.eventData.event_time && 
                           this.eventData.venue_address;
                case 2:
                    return this.selectedMenuItems.length > 0;
                case 3:
                    return true; // Optional step
                default:
                    return true;
            }
        },
        
        nextStep() {
            if (this.canProceed() && this.currentStep < 4) {
                this.currentStep++;
            }
        },
        
        previousStep() {
            if (this.currentStep > 1) {
                this.currentStep--;
            }
        },
        
        submitBooking() {
            // Prepare form data
            const formData = new FormData();
            formData.append('customer_name', this.eventData.customer_name || '{{ user.get_full_name }}');
            formData.append('customer_email', this.eventData.customer_email || '{{ user.email }}');
            formData.append('customer_phone', this.eventData.customer_phone || '');
            formData.append('event_type', this.eventData.event_type);
            formData.append('event_date', this.eventData.event_date);
            formData.append('event_time', this.eventData.event_time);
            formData.append('guest_count', this.eventData.guest_count);
            formData.append('venue_address', this.eventData.venue_address);
            formData.append('sound_system', this.eventData.sound_system);
            formData.append('special_requests', this.eventData.special_requests);

            // Add CSRF token
            formData.append('csrfmiddlewaretoken', document.querySelector('[name=csrfmiddlewaretoken]').value);

            this.selectedMenuItems.forEach(itemId => {
                formData.append('selected_menu_items', itemId);
            });

            // Submit via fetch to the booking endpoint
            fetch('{% url "savory_events:htmx_booking_submit" %}', {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'HX-Request': 'true'
                }
            })
            .then(response => response.text())
            .then(html => {
                // Replace the entire wizard with the response
                document.querySelector('.py-8').innerHTML = html;
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while submitting your booking. Please try again.');
            });
        }
    }
}
</script>
{% endblock %}
