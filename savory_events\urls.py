from django.urls import path
from . import views
from .management_views import (
    management_dashboard, management_bookings, management_booking_detail,
    management_customers, management_customer_detail, management_analytics,
    management_payments, management_add_payment, management_update_booking_status,
    management_menu, management_menu_create, management_menu_update,
    management_menu_delete, management_menu_toggle_availability, management_menu_get_item,
    # Enhanced admin booking management
    management_booking_edit, management_booking_add_note, management_booking_add_reminder,
    management_booking_assign_staff, management_booking_bulk_action, management_booking_calendar,
    management_booking_conflicts, management_booking_reports
)

app_name = 'savory_events'

urlpatterns = [
    # Public pages
    path('', views.home, name='home'),
    path('booking/', views.booking, name='booking'),
    path('booking/wizard/', views.booking_wizard, name='booking_wizard'),
    path('menu/', views.menu, name='menu'),
    path('services/', views.services, name='services'),
    path('contact/', views.contact, name='contact'),
    
    # Authentication
    path('auth/login/', views.login_view, name='login'),
    path('auth/register/', views.register_view, name='register'),
    path('auth/logout/', views.logout_view, name='logout'),
    
    # Customer dashboard
    path('dashboard/', views.customer_dashboard, name='customer_dashboard'),
    path('dashboard/bookings/', views.customer_bookings, name='customer_bookings'),
    path('dashboard/bookings/<int:booking_id>/', views.customer_booking_detail, name='customer_booking_detail'),
    path('dashboard/profile/', views.customer_profile, name='customer_profile'),
    
    # Management dashboard (NEW - using management_views)
    path('management/', management_dashboard, name='management_dashboard'),
    path('management/bookings/', management_bookings, name='management_bookings'),
    path('management/bookings/<int:booking_id>/', management_booking_detail, name='management_booking_detail'),
    path('management/customers/', management_customers, name='management_customers'),
    path('management/customers/<int:customer_id>/', management_customer_detail, name='management_customer_detail'),
    path('management/analytics/', management_analytics, name='management_analytics'),
    path('management/payments/', management_payments, name='management_payments'),
    path('management/payments/add/<int:booking_id>/', management_add_payment, name='management_add_payment'),
    path('management/bookings/<int:booking_id>/status/', management_update_booking_status, name='management_update_booking_status'),

    # Enhanced Admin Booking Management
    path('management/bookings/<int:booking_id>/edit/', management_booking_edit, name='management_booking_edit'),
    path('management/bookings/<int:booking_id>/add-note/', management_booking_add_note, name='management_booking_add_note'),
    path('management/bookings/<int:booking_id>/add-reminder/', management_booking_add_reminder, name='management_booking_add_reminder'),
    path('management/bookings/<int:booking_id>/assign-staff/', management_booking_assign_staff, name='management_booking_assign_staff'),
    path('management/bookings/bulk-action/', management_booking_bulk_action, name='management_booking_bulk_action'),
    path('management/bookings/calendar/', management_booking_calendar, name='management_booking_calendar'),
    path('management/bookings/conflicts/', management_booking_conflicts, name='management_booking_conflicts'),
    path('management/bookings/reports/', management_booking_reports, name='management_booking_reports'),

    # Menu Management
    path('management/menu/', management_menu, name='management_menu'),
    path('management/menu/create/', management_menu_create, name='management_menu_create'),
    path('management/menu/update/<int:item_id>/', management_menu_update, name='management_menu_update'),
    path('management/menu/delete/<int:item_id>/', management_menu_delete, name='management_menu_delete'),
    path('management/menu/toggle/<int:item_id>/', management_menu_toggle_availability, name='management_menu_toggle_availability'),
    path('management/menu/get/<int:item_id>/', management_menu_get_item, name='management_menu_get_item'),
    
    # Legacy admin dashboard (redirect to new management)
    path('admin-dashboard/', views.admin_dashboard_redirect, name='admin_dashboard_redirect'),
    
    # Legacy admin URLs (keep for backward compatibility but redirect)
    path('admin_dashboard/', management_dashboard, name='admin_dashboard'),
    path('admin_customers/', management_customers, name='admin_customers'),
    path('admin_bookings/', management_bookings, name='admin_bookings'),
    path('admin_booking_detail/<int:booking_id>/', management_booking_detail, name='admin_booking_detail'),
    path('admin_customer_detail/<int:customer_id>/', management_customer_detail, name='admin_customer_detail'),
    
    # HTMX endpoints
    path('htmx/booking/submit/', views.htmx_booking_submit, name='htmx_booking_submit'),
    path('htmx/menu/update/', views.htmx_menu_update, name='htmx_menu_update'),
    path('htmx/admin/modal/', views.htmx_admin_modal, name='htmx_admin_modal'),
]
