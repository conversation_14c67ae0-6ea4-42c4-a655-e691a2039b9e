{% extends 'base.html' %}

{% block title %}Home - BonAppetit Catering Services{% endblock %}

{% block content %}
<!-- Hero Section -->
<section class="relative bg-gradient-to-r from-deep-olive to-sage-green text-white py-20">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center">
            <h1 class="text-4xl md:text-6xl font-bold mb-6">Exceptional Catering for Every Occasion</h1>
            <p class="text-xl md:text-2xl mb-8 max-w-3xl mx-auto">
                From intimate gatherings to grand celebrations, we bring culinary excellence to your special events.
            </p>
            <div class="space-y-4 sm:space-y-0 sm:space-x-4 sm:flex sm:justify-center">
                <a href="/booking/" 
                   class="inline-block bg-burnt-orange hover:bg-opacity-80 text-white font-bold py-3 px-8 rounded-lg transition-colors">
                    Book Your Event
                </a>
                <a href="/menu/" 
                   class="inline-block border-2 border-white hover:bg-white hover:text-deep-olive text-white font-bold py-3 px-8 rounded-lg transition-colors">
                    View Our Menu
                </a>
            </div>
        </div>
    </div>
</section>

<!-- Features Section -->
<section class="py-16 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl md:text-4xl font-bold text-charcoal mb-4">Why Choose BonAppetit?</h2>
            <p class="text-lg text-gray-600 max-w-2xl mx-auto">
                We provide comprehensive catering solutions with attention to every detail.
            </p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <!-- Feature 1 -->
            <div class="text-center p-6 rounded-lg border border-sage-green hover:shadow-lg transition-shadow">
                <div class="w-16 h-16 bg-burnt-orange rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-charcoal mb-2">Custom Menu Planning</h3>
                <p class="text-gray-600">Personalized menus tailored to your taste preferences and dietary requirements.</p>
            </div>
            
            <!-- Feature 2 -->
            <div class="text-center p-6 rounded-lg border border-sage-green hover:shadow-lg transition-shadow">
                <div class="w-16 h-16 bg-burnt-orange rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-charcoal mb-2">Professional Service</h3>
                <p class="text-gray-600">Experienced staff ensuring seamless service from setup to cleanup.</p>
            </div>
            
            <!-- Feature 3 -->
            <div class="text-center p-6 rounded-lg border border-sage-green hover:shadow-lg transition-shadow">
                <div class="w-16 h-16 bg-burnt-orange rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a5 5 0 1110 0v6a3 3 0 01-3 3z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-charcoal mb-2">Sound System Services</h3>
                <p class="text-gray-600">Optional audio equipment packages to enhance your event experience.</p>
            </div>
        </div>
    </div>
</section>

<!-- Featured Menu Section -->
<section class="py-16 bg-sage-green bg-opacity-10">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl md:text-4xl font-bold text-charcoal mb-4">Featured Menu Highlights</h2>
            <p class="text-lg text-gray-600 max-w-2xl mx-auto">
                Discover some of our most popular dishes, crafted with fresh ingredients and culinary expertise.
            </p>
        </div>

        <!-- Appetizers -->
        {% if featured_appetizers %}
        <div class="mb-12">
            <h3 class="text-2xl font-semibold text-charcoal mb-6 text-center">Appetizers</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                {% for item in featured_appetizers %}
                <div class="bg-white rounded-xl shadow-lg hover:shadow-xl transition-shadow duration-300 overflow-hidden">
                    <div class="p-6">
                        <div class="flex justify-between items-start mb-3">
                            <h4 class="text-xl font-semibold text-charcoal">{{ item.name }}</h4>
                            <span class="text-lg font-bold text-burnt-orange">${{ item.price_per_person }}</span>
                        </div>
                        <p class="text-gray-600 text-sm leading-relaxed">{{ item.description }}</p>
                        {% if item.image %}
                        <div class="mt-4 h-32 bg-gray-200 rounded-lg overflow-hidden">
                            <img src="{{ item.image.url }}" alt="{{ item.name }}" class="w-full h-full object-cover">
                        </div>
                        {% endif %}
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
        {% endif %}

        <!-- Main Dishes -->
        {% if featured_mains %}
        <div class="mb-12">
            <h3 class="text-2xl font-semibold text-charcoal mb-6 text-center">Main Dishes</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                {% for item in featured_mains %}
                <div class="bg-white rounded-xl shadow-lg hover:shadow-xl transition-shadow duration-300 overflow-hidden">
                    <div class="p-6">
                        <div class="flex justify-between items-start mb-3">
                            <h4 class="text-xl font-semibold text-charcoal">{{ item.name }}</h4>
                            <span class="text-lg font-bold text-burnt-orange">${{ item.price_per_person }}</span>
                        </div>
                        <p class="text-gray-600 text-sm leading-relaxed">{{ item.description }}</p>
                        {% if item.image %}
                        <div class="mt-4 h-32 bg-gray-200 rounded-lg overflow-hidden">
                            <img src="{{ item.image.url }}" alt="{{ item.name }}" class="w-full h-full object-cover">
                        </div>
                        {% endif %}
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
        {% endif %}

        <!-- Desserts -->
        {% if featured_desserts %}
        <div class="mb-8">
            <h3 class="text-2xl font-semibold text-charcoal mb-6 text-center">Desserts</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                {% for item in featured_desserts %}
                <div class="bg-white rounded-xl shadow-lg hover:shadow-xl transition-shadow duration-300 overflow-hidden">
                    <div class="p-6">
                        <div class="flex justify-between items-start mb-3">
                            <h4 class="text-xl font-semibold text-charcoal">{{ item.name }}</h4>
                            <span class="text-lg font-bold text-burnt-orange">${{ item.price_per_person }}</span>
                        </div>
                        <p class="text-gray-600 text-sm leading-relaxed">{{ item.description }}</p>
                        {% if item.image %}
                        <div class="mt-4 h-32 bg-gray-200 rounded-lg overflow-hidden">
                            <img src="{{ item.image.url }}" alt="{{ item.name }}" class="w-full h-full object-cover">
                        </div>
                        {% endif %}
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
        {% endif %}

        <!-- View Full Menu CTA -->
        <div class="text-center">
            <a href="/menu/" 
               class="inline-block bg-burnt-orange hover:bg-opacity-80 text-white font-bold py-4 px-8 rounded-lg transition-colors text-lg shadow-lg hover:shadow-xl">
                View Our Complete Menu
            </a>
        </div>
    </div>
</section>

<!-- Quick Booking Section -->
<section class="py-16 bg-cream">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h2 class="text-3xl md:text-4xl font-bold text-charcoal mb-4">Ready to Plan Your Event?</h2>
        <p class="text-lg text-gray-600 mb-8">
            Get started with our simple booking process and let us handle the rest.
        </p>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <div class="bg-white p-6 rounded-lg shadow-md">
                <div class="text-burnt-orange text-2xl font-bold mb-2">1</div>
                <h3 class="font-semibold mb-2">Choose Your Date</h3>
                <p class="text-sm text-gray-600">Select your event date and provide basic details.</p>
            </div>
            <div class="bg-white p-6 rounded-lg shadow-md">
                <div class="text-burnt-orange text-2xl font-bold mb-2">2</div>
                <h3 class="font-semibold mb-2">Customize Your Menu</h3>
                <p class="text-sm text-gray-600">Pick from our delicious menu options or create a custom selection.</p>
            </div>
            <div class="bg-white p-6 rounded-lg shadow-md">
                <div class="text-burnt-orange text-2xl font-bold mb-2">3</div>
                <h3 class="font-semibold mb-2">Secure Your Booking</h3>
                <p class="text-sm text-gray-600">Confirm with a 50% deposit and we'll handle the rest.</p>
            </div>
        </div>
        <a href="/booking/" 
           class="inline-block bg-burnt-orange hover:bg-opacity-80 text-white font-bold py-4 px-8 rounded-lg transition-colors text-lg">
            Start Your Booking
        </a>
    </div>
</section>

<!-- Contact Section -->
<section class="py-16 bg-white">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h2 class="text-3xl md:text-4xl font-bold text-charcoal mb-4">Get in Touch</h2>
        <p class="text-lg text-gray-600 mb-8">
            Have questions? We're here to help you plan the perfect event.
        </p>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div class="bg-cream p-6 rounded-lg">
                <h3 class="font-semibold text-lg mb-4">Contact Information</h3>
                <div class="space-y-2 text-left">
                    <p><strong>Email:</strong> <EMAIL></p>
                    <p><strong>Phone:</strong> (*************</p>
                    <p><strong>Hours:</strong> Mon-Fri 9AM-6PM</p>
                </div>
            </div>
            <div class="bg-cream p-6 rounded-lg">
                <h3 class="font-semibold text-lg mb-4">Quick Contact</h3>
                <form class="space-y-4"
                      x-data="{ loading: false, errors: {} }"
                      up-submit up-target="#quick-contact-response"
                      @submit.prevent="loading = true; $el.submit();">
                    {% csrf_token %}
                    <div id="quick-contact-response" class="mb-2"></div>
                    
                    <input type="text" name="name" placeholder="Your Name" required
                           x-data="{ value: '' }" x-model="value"
                           :class="{ 'border-red-500': errors.name, 'border-sage-green': !errors.name }"
                           class="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-burnt-orange transition-colors">
                    
                    <input type="email" name="email" placeholder="Your Email" required
                           x-data="{ value: '' }" x-model="value"
                           :class="{ 'border-red-500': errors.email, 'border-sage-green': !errors.email }"
                           class="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-burnt-orange transition-colors">
                    
                    <textarea name="message" placeholder="Your Message" rows="3" required
                              x-data="{ value: '' }" x-model="value"
                              :class="{ 'border-red-500': errors.message, 'border-sage-green': !errors.message }"
                              class="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-burnt-orange transition-colors"></textarea>
                    
                    <button type="submit"
                            :disabled="loading"
                            :class="{ 'opacity-50 cursor-not-allowed': loading }"
                            class="w-full bg-burnt-orange hover:bg-opacity-80 text-white font-semibold py-2 px-4 rounded-md transition-all duration-200 flex items-center justify-center">
                        <span x-show="!loading">Send Message</span>
                        <span x-show="loading" class="flex items-center">
                            <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            Sending...
                        </span>
                    </button>
                </form>
            </div>
        </div>
    </div>
</section>
{% endblock %}
