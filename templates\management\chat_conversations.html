{% extends 'management/base.html' %}

{% block title %}Chat Conversations{% endblock %}

{% block content %}
<div class="p-6">
    <!-- Header -->
    <div class="flex justify-between items-center mb-6">
        <div>
            <h1 class="text-2xl font-bold text-charcoal">Chat Conversations</h1>
            <p class="text-gray-600 mt-1">Monitor and manage customer chat conversations</p>
        </div>
        <div class="flex space-x-3">
            <button onclick="location.reload()" 
                    class="bg-sage-green text-white px-4 py-2 rounded-lg hover:bg-deep-olive transition-colors">
                <i class="fas fa-sync-alt mr-2"></i>Refresh
            </button>
        </div>
    </div>

    <!-- Filters -->
    <div class="bg-white rounded-lg shadow-sm p-4 mb-6">
        <form method="GET" class="flex flex-wrap gap-4 items-end">
            <div class="flex-1 min-w-64">
                <label class="block text-sm font-medium text-gray-700 mb-1">Search</label>
                <input type="text" name="search" value="{{ search_query }}" 
                       placeholder="Search by user, customer, or session ID..."
                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-burnt-orange">
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Status</label>
                <select name="status" class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-burnt-orange">
                    <option value="all" {% if status_filter == 'all' %}selected{% endif %}>All Conversations</option>
                    <option value="active" {% if status_filter == 'active' %}selected{% endif %}>Active</option>
                    <option value="escalated" {% if status_filter == 'escalated' %}selected{% endif %}>Escalated</option>
                </select>
            </div>
            <button type="submit" class="bg-burnt-orange text-white px-4 py-2 rounded-md hover:bg-opacity-80 transition-colors">
                <i class="fas fa-search mr-2"></i>Filter
            </button>
        </form>
    </div>

    <!-- Conversations List -->
    <div class="bg-white rounded-lg shadow-sm overflow-hidden">
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Conversation
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            User/Customer
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Messages
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Status
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Last Activity
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Actions
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    {% for conversation in conversations %}
                    <tr class="hover:bg-gray-50">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm font-medium text-gray-900">
                                Session: {{ conversation.session_id|slice:":8" }}...
                            </div>
                            <div class="text-sm text-gray-500">
                                Started: {{ conversation.created_at|date:"M d, Y H:i" }}
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            {% if conversation.user %}
                                <div class="text-sm font-medium text-gray-900">
                                    <i class="fas fa-user text-sage-green mr-1"></i>
                                    {{ conversation.user.username }}
                                </div>
                                {% if conversation.user.email %}
                                <div class="text-sm text-gray-500">{{ conversation.user.email }}</div>
                                {% endif %}
                            {% elif conversation.customer %}
                                <div class="text-sm font-medium text-gray-900">
                                    <i class="fas fa-user-circle text-burnt-orange mr-1"></i>
                                    {{ conversation.customer.name }}
                                </div>
                                <div class="text-sm text-gray-500">{{ conversation.customer.email }}</div>
                            {% else %}
                                <div class="text-sm text-gray-500">
                                    <i class="fas fa-user-secret text-gray-400 mr-1"></i>
                                    Anonymous User
                                </div>
                            {% endif %}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-gray-900">
                                {{ conversation.messages.count }} messages
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center space-x-2">
                                {% if conversation.is_active %}
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                        <i class="fas fa-circle text-green-400 mr-1" style="font-size: 6px;"></i>
                                        Active
                                    </span>
                                {% else %}
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                        <i class="fas fa-circle text-gray-400 mr-1" style="font-size: 6px;"></i>
                                        Inactive
                                    </span>
                                {% endif %}
                                
                                {% if conversation.escalation %}
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                        <i class="fas fa-exclamation-triangle mr-1"></i>
                                        Escalated
                                    </span>
                                {% endif %}
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {{ conversation.updated_at|timesince }} ago
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <a href="{% url 'savory_events:admin_chat_detail' conversation.id %}" 
                               class="text-burnt-orange hover:text-deep-olive mr-3">
                                <i class="fas fa-eye mr-1"></i>View
                            </a>
                            {% if conversation.escalation %}
                                <span class="text-red-600">
                                    <i class="fas fa-exclamation-circle mr-1"></i>
                                    {{ conversation.escalation.get_status_display }}
                                </span>
                            {% endif %}
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="6" class="px-6 py-12 text-center text-gray-500">
                            <i class="fas fa-comments text-4xl text-gray-300 mb-4"></i>
                            <p class="text-lg">No conversations found</p>
                            <p class="text-sm">Chat conversations will appear here once customers start using the chatbot.</p>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        {% if conversations.has_other_pages %}
        <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
            <div class="flex-1 flex justify-between sm:hidden">
                {% if conversations.has_previous %}
                    <a href="?page={{ conversations.previous_page_number }}&search={{ search_query }}&status={{ status_filter }}" 
                       class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                        Previous
                    </a>
                {% endif %}
                {% if conversations.has_next %}
                    <a href="?page={{ conversations.next_page_number }}&search={{ search_query }}&status={{ status_filter }}" 
                       class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                        Next
                    </a>
                {% endif %}
            </div>
            <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                <div>
                    <p class="text-sm text-gray-700">
                        Showing page {{ conversations.number }} of {{ conversations.paginator.num_pages }}
                        ({{ conversations.paginator.count }} total conversations)
                    </p>
                </div>
                <div>
                    <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                        {% if conversations.has_previous %}
                            <a href="?page={{ conversations.previous_page_number }}&search={{ search_query }}&status={{ status_filter }}" 
                               class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                <i class="fas fa-chevron-left"></i>
                            </a>
                        {% endif %}
                        
                        <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700">
                            Page {{ conversations.number }}
                        </span>
                        
                        {% if conversations.has_next %}
                            <a href="?page={{ conversations.next_page_number }}&search={{ search_query }}&status={{ status_filter }}" 
                               class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                <i class="fas fa-chevron-right"></i>
                            </a>
                        {% endif %}
                    </nav>
                </div>
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
