<div class="bg-red-50 border border-red-200 rounded-lg p-6 mt-6">
    <div class="flex items-start">
        <div class="flex-shrink-0">
            <svg class="h-6 w-6 text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
            </svg>
        </div>
        <div class="ml-3">
            <h3 class="text-lg font-medium text-red-800">
                Please correct the following errors:
            </h3>
            <div class="mt-2 text-sm text-red-700">
                {% if errors %}
                    <ul class="list-disc list-inside space-y-1">
                        {% for field, field_errors in errors.items %}
                            {% for error in field_errors %}
                                <li>{{ field|title }}: {{ error }}</li>
                            {% endfor %}
                        {% endfor %}
                    </ul>
                {% endif %}
            </div>
            <div class="mt-4">
                <button onclick="this.parentElement.parentElement.parentElement.parentElement.remove()"
                        class="bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-lg transition-colors">
                    Dismiss
                </button>
            </div>
        </div>
    </div>
</div>
