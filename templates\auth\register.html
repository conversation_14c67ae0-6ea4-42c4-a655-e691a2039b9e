{% extends 'base.html' %}

{% block title %}Create Account - BonAppetit Catering{% endblock %}

{% block content %}
<div class="min-h-screen bg-gradient-to-br from-cream via-white to-sage-green/10 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8" x-data="registerForm()">
    <!-- Background Pattern -->
    <div class="absolute inset-0 opacity-5">
        <div class="absolute inset-0" style="background-image: url('data:image/svg+xml,<svg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"><g fill="none" fill-rule="evenodd"><g fill="%23556B2F" fill-opacity="0.1"><circle cx="30" cy="30" r="2"/></g></svg>'); background-size: 60px 60px;"></div>
    </div>

    <div class="relative max-w-lg w-full">
        <!-- Main Card -->
        <div class="bg-white/80 backdrop-blur-sm shadow-2xl rounded-2xl border border-sage-green/20 overflow-hidden transform transition-all duration-300 hover:shadow-3xl">
            <!-- Header Section -->
            <div class="px-8 pt-8 pb-6 bg-gradient-to-r from-deep-olive to-sage-green text-white relative overflow-hidden">
                <!-- Decorative Elements -->
                <div class="absolute top-0 right-0 w-32 h-32 bg-white/10 rounded-full -translate-y-16 translate-x-16"></div>
                <div class="absolute bottom-0 left-0 w-24 h-24 bg-white/5 rounded-full translate-y-12 -translate-x-12"></div>

                <div class="relative z-10 text-center">
                    <!-- Logo -->
                    <div class="mx-auto h-20 w-20 bg-white/20 backdrop-blur-sm rounded-2xl flex items-center justify-center mb-4 transform transition-transform duration-300 hover:scale-105 shadow-lg">
                        <span class="text-white font-bold text-2xl">BA</span>
                    </div>

                    <!-- Title -->
                    <h1 class="text-2xl font-bold mb-2">Join BonAppetit</h1>
                    <p class="text-white/90 text-sm">Create your account to start planning amazing events</p>
                </div>
            </div>

            <!-- Form Section -->
            <div class="px-8 py-6">
                <form class="space-y-6" method="post" @submit="handleSubmit">
                    {% csrf_token %}

                    <!-- Progress Indicator -->
                    <div class="mb-6">
                        <div class="flex items-center justify-between text-sm text-gray-600 mb-2">
                            <span>Account Information</span>
                            <span x-text="Math.round(completionPercentage) + '%'" class="font-semibold"></span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2">
                            <div class="bg-gradient-to-r from-sage-green to-burnt-orange h-2 rounded-full transition-all duration-500"
                                 :style="`width: ${completionPercentage}%`"></div>
                        </div>
                    </div>

                    <!-- Username Field -->
                    <div class="space-y-2">
                        <label for="{{ form.username.id_for_label }}" class="block text-sm font-semibold text-charcoal">
                            <i class="fas fa-user mr-2 text-sage-green"></i>Username *
                        </label>
                        <div class="relative">
                            <input type="text"
                                   name="{{ form.username.name }}"
                                   id="{{ form.username.id_for_label }}"
                                   class="w-full px-4 py-3 border-2 border-sage-green/30 rounded-xl focus:outline-none focus:border-burnt-orange focus:ring-4 focus:ring-burnt-orange/20 transition-all duration-300 bg-white/70 backdrop-blur-sm placeholder-gray-400"
                                   placeholder="Choose a unique username"
                                   x-model="formData.username"
                                   @input="validateField('username')"
                                   :class="getFieldClass('username')"
                                   required>
                            <div class="absolute right-3 top-1/2 transform -translate-y-1/2">
                                <i x-show="validFields.username" class="fas fa-check text-green-500 transition-opacity duration-300"></i>
                                <i x-show="errors.username" class="fas fa-exclamation-circle text-red-500 transition-opacity duration-300"></i>
                            </div>
                        </div>
                        <p x-show="errors.username" x-text="errors.username" class="text-red-500 text-sm mt-1 transition-opacity duration-300"></p>
                        {% if form.username.errors %}
                            {% for error in form.username.errors %}
                                <p class="text-red-500 text-sm">{{ error }}</p>
                            {% endfor %}
                        {% endif %}
                    </div>

                    <!-- Name Fields -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <!-- First Name -->
                        <div class="space-y-2">
                            <label for="{{ form.first_name.id_for_label }}" class="block text-sm font-semibold text-charcoal">
                                <i class="fas fa-id-card mr-2 text-sage-green"></i>First Name *
                            </label>
                            <div class="relative">
                                <input type="text"
                                       name="{{ form.first_name.name }}"
                                       id="{{ form.first_name.id_for_label }}"
                                       class="w-full px-4 py-3 border-2 border-sage-green/30 rounded-xl focus:outline-none focus:border-burnt-orange focus:ring-4 focus:ring-burnt-orange/20 transition-all duration-300 bg-white/70 backdrop-blur-sm placeholder-gray-400"
                                       placeholder="Your first name"
                                       x-model="formData.first_name"
                                       @input="validateField('first_name')"
                                       :class="getFieldClass('first_name')"
                                       required>
                                <div class="absolute right-3 top-1/2 transform -translate-y-1/2">
                                    <i x-show="validFields.first_name" class="fas fa-check text-green-500 transition-opacity duration-300"></i>
                                    <i x-show="errors.first_name" class="fas fa-exclamation-circle text-red-500 transition-opacity duration-300"></i>
                                </div>
                            </div>
                            <p x-show="errors.first_name" x-text="errors.first_name" class="text-red-500 text-sm mt-1 transition-opacity duration-300"></p>
                            {% if form.first_name.errors %}
                                {% for error in form.first_name.errors %}
                                    <p class="text-red-500 text-sm">{{ error }}</p>
                                {% endfor %}
                            {% endif %}
                        </div>

                        <!-- Last Name -->
                        <div class="space-y-2">
                            <label for="{{ form.last_name.id_for_label }}" class="block text-sm font-semibold text-charcoal">
                                <i class="fas fa-id-card mr-2 text-sage-green"></i>Last Name *
                            </label>
                            <div class="relative">
                                <input type="text"
                                       name="{{ form.last_name.name }}"
                                       id="{{ form.last_name.id_for_label }}"
                                       class="w-full px-4 py-3 border-2 border-sage-green/30 rounded-xl focus:outline-none focus:border-burnt-orange focus:ring-4 focus:ring-burnt-orange/20 transition-all duration-300 bg-white/70 backdrop-blur-sm placeholder-gray-400"
                                       placeholder="Your last name"
                                       x-model="formData.last_name"
                                       @input="validateField('last_name')"
                                       :class="getFieldClass('last_name')"
                                       required>
                                <div class="absolute right-3 top-1/2 transform -translate-y-1/2">
                                    <i x-show="validFields.last_name" class="fas fa-check text-green-500 transition-opacity duration-300"></i>
                                    <i x-show="errors.last_name" class="fas fa-exclamation-circle text-red-500 transition-opacity duration-300"></i>
                                </div>
                            </div>
                            <p x-show="errors.last_name" x-text="errors.last_name" class="text-red-500 text-sm mt-1 transition-opacity duration-300"></p>
                            {% if form.last_name.errors %}
                                {% for error in form.last_name.errors %}
                                    <p class="text-red-500 text-sm">{{ error }}</p>
                                {% endfor %}
                            {% endif %}
                        </div>
                    </div>

                    <!-- Email Field -->
                    <div class="space-y-2">
                        <label for="{{ form.email.id_for_label }}" class="block text-sm font-semibold text-charcoal">
                            <i class="fas fa-envelope mr-2 text-sage-green"></i>Email Address *
                        </label>
                        <div class="relative">
                            <input type="email"
                                   name="{{ form.email.name }}"
                                   id="{{ form.email.id_for_label }}"
                                   class="w-full px-4 py-3 border-2 border-sage-green/30 rounded-xl focus:outline-none focus:border-burnt-orange focus:ring-4 focus:ring-burnt-orange/20 transition-all duration-300 bg-white/70 backdrop-blur-sm placeholder-gray-400"
                                   placeholder="<EMAIL>"
                                   x-model="formData.email"
                                   @input="validateField('email')"
                                   :class="getFieldClass('email')"
                                   required>
                            <div class="absolute right-3 top-1/2 transform -translate-y-1/2">
                                <i x-show="validFields.email" class="fas fa-check text-green-500 transition-opacity duration-300"></i>
                                <i x-show="errors.email" class="fas fa-exclamation-circle text-red-500 transition-opacity duration-300"></i>
                            </div>
                        </div>
                        <p x-show="errors.email" x-text="errors.email" class="text-red-500 text-sm mt-1 transition-opacity duration-300"></p>
                        {% if form.email.errors %}
                            {% for error in form.email.errors %}
                                <p class="text-red-500 text-sm">{{ error }}</p>
                            {% endfor %}
                        {% endif %}
                    </div>

                    <!-- Phone Field -->
                    <div class="space-y-2">
                        <label for="{{ form.phone.id_for_label }}" class="block text-sm font-semibold text-charcoal">
                            <i class="fas fa-phone mr-2 text-sage-green"></i>Phone Number <span class="text-gray-400">(Optional)</span>
                        </label>
                        <div class="relative">
                            <input type="tel"
                                   name="{{ form.phone.name }}"
                                   id="{{ form.phone.id_for_label }}"
                                   class="w-full px-4 py-3 border-2 border-sage-green/30 rounded-xl focus:outline-none focus:border-burnt-orange focus:ring-4 focus:ring-burnt-orange/20 transition-all duration-300 bg-white/70 backdrop-blur-sm placeholder-gray-400"
                                   placeholder="(*************"
                                   x-model="formData.phone"
                                   @input="validateField('phone')"
                                   :class="getFieldClass('phone')">
                            <div class="absolute right-3 top-1/2 transform -translate-y-1/2">
                                <i x-show="validFields.phone" class="fas fa-check text-green-500 transition-opacity duration-300"></i>
                                <i x-show="errors.phone" class="fas fa-exclamation-circle text-red-500 transition-opacity duration-300"></i>
                            </div>
                        </div>
                        <p x-show="errors.phone" x-text="errors.phone" class="text-red-500 text-sm mt-1 transition-opacity duration-300"></p>
                        {% if form.phone.errors %}
                            {% for error in form.phone.errors %}
                                <p class="text-red-500 text-sm">{{ error }}</p>
                            {% endfor %}
                        {% endif %}
                    </div>

                    <!-- Password Fields -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <!-- Password -->
                        <div class="space-y-2">
                            <label for="{{ form.password1.id_for_label }}" class="block text-sm font-semibold text-charcoal">
                                <i class="fas fa-lock mr-2 text-sage-green"></i>Password *
                            </label>
                            <div class="relative">
                                <input :type="showPassword ? 'text' : 'password'"
                                       name="{{ form.password1.name }}"
                                       id="{{ form.password1.id_for_label }}"
                                       class="w-full px-4 py-3 pr-12 border-2 border-sage-green/30 rounded-xl focus:outline-none focus:border-burnt-orange focus:ring-4 focus:ring-burnt-orange/20 transition-all duration-300 bg-white/70 backdrop-blur-sm placeholder-gray-400"
                                       placeholder="Create a strong password"
                                       x-model="formData.password1"
                                       @input="validateField('password1')"
                                       :class="getFieldClass('password1')"
                                       required>
                                <button type="button"
                                        @click="showPassword = !showPassword"
                                        class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-sage-green transition-colors duration-200">
                                    <i :class="showPassword ? 'fas fa-eye-slash' : 'fas fa-eye'"></i>
                                </button>
                            </div>
                            <p x-show="errors.password1" x-text="errors.password1" class="text-red-500 text-sm mt-1 transition-opacity duration-300"></p>
                            {% if form.password1.errors %}
                                {% for error in form.password1.errors %}
                                    <p class="text-red-500 text-sm">{{ error }}</p>
                                {% endfor %}
                            {% endif %}

                            <!-- Password Strength Indicator -->
                            <div x-show="formData.password1" class="mt-2">
                                <div class="flex items-center space-x-2 text-xs">
                                    <span class="text-gray-600">Strength:</span>
                                    <div class="flex space-x-1">
                                        <div class="w-6 h-1 rounded" :class="passwordStrength >= 1 ? 'bg-red-400' : 'bg-gray-200'"></div>
                                        <div class="w-6 h-1 rounded" :class="passwordStrength >= 2 ? 'bg-yellow-400' : 'bg-gray-200'"></div>
                                        <div class="w-6 h-1 rounded" :class="passwordStrength >= 3 ? 'bg-green-400' : 'bg-gray-200'"></div>
                                    </div>
                                    <span x-text="getPasswordStrengthText()" class="font-medium" :class="getPasswordStrengthColor()"></span>
                                </div>
                            </div>
                        </div>

                        <!-- Confirm Password -->
                        <div class="space-y-2">
                            <label for="{{ form.password2.id_for_label }}" class="block text-sm font-semibold text-charcoal">
                                <i class="fas fa-lock mr-2 text-sage-green"></i>Confirm Password *
                            </label>
                            <div class="relative">
                                <input :type="showPassword ? 'text' : 'password'"
                                       name="{{ form.password2.name }}"
                                       id="{{ form.password2.id_for_label }}"
                                       class="w-full px-4 py-3 pr-12 border-2 border-sage-green/30 rounded-xl focus:outline-none focus:border-burnt-orange focus:ring-4 focus:ring-burnt-orange/20 transition-all duration-300 bg-white/70 backdrop-blur-sm placeholder-gray-400"
                                       placeholder="Confirm your password"
                                       x-model="formData.password2"
                                       @input="validateField('password2')"
                                       :class="getFieldClass('password2')"
                                       required>
                                <div class="absolute right-3 top-1/2 transform -translate-y-1/2">
                                    <i x-show="validFields.password2" class="fas fa-check text-green-500 transition-opacity duration-300"></i>
                                    <i x-show="errors.password2" class="fas fa-exclamation-circle text-red-500 transition-opacity duration-300"></i>
                                </div>
                            </div>
                            <p x-show="errors.password2" x-text="errors.password2" class="text-red-500 text-sm mt-1 transition-opacity duration-300"></p>
                            {% if form.password2.errors %}
                                {% for error in form.password2.errors %}
                                    <p class="text-red-500 text-sm">{{ error }}</p>
                                {% endfor %}
                            {% endif %}
                        </div>
                    </div>

                    <!-- Django Form Errors -->
                    {% if form.non_field_errors %}
                    <div class="bg-red-50 border-l-4 border-red-400 p-4 rounded-lg" x-data="{ show: true }" x-show="show" x-transition>
                        <div class="flex items-start">
                            <div class="flex-shrink-0">
                                <i class="fas fa-exclamation-triangle text-red-400"></i>
                            </div>
                            <div class="ml-3 flex-1">
                                <h3 class="text-sm font-medium text-red-800 mb-2">
                                    Please correct the following errors:
                                </h3>
                                <div class="text-sm text-red-700 space-y-1">
                                    {% for error in form.non_field_errors %}
                                        <div class="flex items-center">
                                            <i class="fas fa-times-circle mr-2"></i>
                                            <span>{{ error }}</span>
                                        </div>
                                    {% endfor %}
                                </div>
                            </div>
                            <button @click="show = false" class="flex-shrink-0 ml-3 text-red-400 hover:text-red-600 transition-colors">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                    {% endif %}

                    <!-- Submit Button -->
                    <div class="space-y-4">
                        <button type="submit"
                                :disabled="isSubmitting || !isFormValid"
                                class="group relative w-full flex justify-center py-4 px-6 border border-transparent text-base font-semibold rounded-xl text-white bg-gradient-to-r from-burnt-orange to-deep-olive hover:from-deep-olive hover:to-burnt-orange focus:outline-none focus:ring-4 focus:ring-burnt-orange/30 disabled:opacity-50 disabled:cursor-not-allowed transform transition-all duration-300 hover:scale-[1.02] hover:shadow-lg"
                                :class="{'opacity-50 cursor-not-allowed': !isFormValid}">

                            <!-- Loading Spinner -->
                            <div x-show="isSubmitting" class="absolute left-4 top-1/2 transform -translate-y-1/2">
                                <div class="animate-spin rounded-full h-5 w-5 border-2 border-white border-t-transparent"></div>
                            </div>

                            <!-- Create Account Icon -->
                            <span x-show="!isSubmitting" class="absolute left-4 top-1/2 transform -translate-y-1/2">
                                <i class="fas fa-user-plus text-white/80 group-hover:text-white transition-colors duration-200"></i>
                            </span>

                            <!-- Button Text -->
                            <span x-text="isSubmitting ? 'Creating Account...' : 'Create Account'" class="transition-all duration-200"></span>
                        </button>

                        <!-- Terms and Privacy -->
                        <p class="text-xs text-gray-600 text-center">
                            By creating an account, you agree to our
                            <a href="#" class="text-burnt-orange hover:text-deep-olive transition-colors duration-200 font-medium">Terms of Service</a>
                            and
                            <a href="#" class="text-burnt-orange hover:text-deep-olive transition-colors duration-200 font-medium">Privacy Policy</a>
                        </p>
                    </div>

                </form>
            </div>

            <!-- Footer Section -->
            <div class="px-8 py-6 bg-gray-50/80 border-t border-sage-green/20">
                <div class="text-center space-y-4">
                    <p class="text-sm text-gray-600">
                        Already have an account?
                        <a href="{% url 'savory_events:login' %}" class="font-semibold text-burnt-orange hover:text-deep-olive transition-colors duration-200 hover:underline">
                            Sign in here
                        </a>
                    </p>

                    <!-- Security Badge -->
                    <div class="flex items-center justify-center space-x-4 pt-4 border-t border-gray-200">
                        <div class="flex items-center space-x-2">
                            <i class="fas fa-shield-alt text-sage-green"></i>
                            <span class="text-xs text-gray-500">Secure Registration</span>
                        </div>
                        <div class="flex space-x-2">
                            <div class="w-2 h-2 bg-sage-green rounded-full animate-pulse"></div>
                            <div class="w-2 h-2 bg-burnt-orange rounded-full animate-pulse" style="animation-delay: 0.2s;"></div>
                            <div class="w-2 h-2 bg-deep-olive rounded-full animate-pulse" style="animation-delay: 0.4s;"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Additional Help Text -->
        <div class="mt-6 text-center">
            <p class="text-sm text-gray-600">
                Need help? <a href="#" class="text-burnt-orange hover:text-deep-olive transition-colors duration-200 font-medium">Contact Support</a>
            </p>
        </div>
    </div>
</div>

<!-- Alpine.js Component -->
<script>
function registerForm() {
    return {
        formData: {
            username: '',
            first_name: '',
            last_name: '',
            email: '',
            phone: '',
            password1: '',
            password2: ''
        },
        errors: {},
        validFields: {},
        showPassword: false,
        isSubmitting: false,

        get completionPercentage() {
            const requiredFields = ['username', 'first_name', 'last_name', 'email', 'password1', 'password2'];
            const completedFields = requiredFields.filter(field =>
                this.formData[field] && this.formData[field].length > 0 && this.validFields[field]
            );
            return (completedFields.length / requiredFields.length) * 100;
        },

        get isFormValid() {
            const requiredFields = ['username', 'first_name', 'last_name', 'email', 'password1', 'password2'];
            return requiredFields.every(field => this.validFields[field]) &&
                   Object.keys(this.errors).length === 0;
        },

        get passwordStrength() {
            const password = this.formData.password1;
            if (!password) return 0;

            let strength = 0;
            if (password.length >= 8) strength++;
            if (/[A-Z]/.test(password) && /[a-z]/.test(password)) strength++;
            if (/\d/.test(password) && /[!@#$%^&*(),.?":{}|<>]/.test(password)) strength++;

            return strength;
        },

        getPasswordStrengthText() {
            switch (this.passwordStrength) {
                case 0: return 'Too weak';
                case 1: return 'Weak';
                case 2: return 'Good';
                case 3: return 'Strong';
                default: return '';
            }
        },

        getPasswordStrengthColor() {
            switch (this.passwordStrength) {
                case 0: return 'text-red-500';
                case 1: return 'text-red-500';
                case 2: return 'text-yellow-500';
                case 3: return 'text-green-500';
                default: return 'text-gray-500';
            }
        },

        getFieldClass(field) {
            if (this.errors[field]) {
                return 'border-red-400 focus:border-red-400 focus:ring-red-400/20';
            } else if (this.validFields[field]) {
                return 'border-green-400 focus:border-green-400 focus:ring-green-400/20';
            }
            return '';
        },

        validateField(field) {
            // Clear previous errors
            delete this.errors[field];
            delete this.validFields[field];

            const value = this.formData[field];

            switch (field) {
                case 'username':
                    if (!value || value.length < 3) {
                        this.errors.username = 'Username must be at least 3 characters';
                    } else if (!/^[a-zA-Z0-9_]+$/.test(value)) {
                        this.errors.username = 'Username can only contain letters, numbers, and underscores';
                    } else {
                        this.validFields.username = true;
                    }
                    break;

                case 'first_name':
                case 'last_name':
                    if (!value || value.length < 2) {
                        this.errors[field] = 'Name must be at least 2 characters';
                    } else if (!/^[a-zA-Z\s]+$/.test(value)) {
                        this.errors[field] = 'Name can only contain letters and spaces';
                    } else {
                        this.validFields[field] = true;
                    }
                    break;

                case 'email':
                    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                    if (!value) {
                        this.errors.email = 'Email is required';
                    } else if (!emailRegex.test(value)) {
                        this.errors.email = 'Please enter a valid email address';
                    } else {
                        this.validFields.email = true;
                    }
                    break;

                case 'phone':
                    if (value && !/^[\d\s\-\(\)\+]+$/.test(value)) {
                        this.errors.phone = 'Please enter a valid phone number';
                    } else if (value) {
                        this.validFields.phone = true;
                    }
                    break;

                case 'password1':
                    if (!value || value.length < 8) {
                        this.errors.password1 = 'Password must be at least 8 characters';
                    } else if (this.passwordStrength < 2) {
                        this.errors.password1 = 'Password is too weak. Use uppercase, lowercase, numbers, and symbols';
                    } else {
                        this.validFields.password1 = true;
                        // Re-validate password2 if it exists
                        if (this.formData.password2) {
                            this.validateField('password2');
                        }
                    }
                    break;

                case 'password2':
                    if (!value) {
                        this.errors.password2 = 'Please confirm your password';
                    } else if (value !== this.formData.password1) {
                        this.errors.password2 = 'Passwords do not match';
                    } else {
                        this.validFields.password2 = true;
                    }
                    break;
            }
        },

        handleSubmit(event) {
            // Validate all fields before submission
            Object.keys(this.formData).forEach(field => {
                this.validateField(field);
            });

            // If there are errors, prevent submission
            if (Object.keys(this.errors).length > 0 || !this.isFormValid) {
                event.preventDefault();
                return;
            }

            // Show loading state
            this.isSubmitting = true;

            // Let the form submit naturally
            // The loading state will be reset when the page reloads or redirects
        }
    }
}
</script>

{% endblock %}
