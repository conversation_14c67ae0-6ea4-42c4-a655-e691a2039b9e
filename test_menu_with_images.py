#!/usr/bin/env python
"""
Test script for the enhanced menu management system with image upload functionality.
"""

import os
import sys
import django
from django.core.files.uploadedfile import SimpleUploadedFile
from PIL import Image
import io

# Setup Django first
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'BonAppetit.settings')
django.setup()

# Now import Django modules
from django.test import TestCase, Client
from django.contrib.auth.models import User
from django.urls import reverse
from savory_events.models import MenuItem
from decimal import Decimal


def create_test_image():
    """Create a test image file for upload testing."""
    # Create a simple test image
    image = Image.new('RGB', (100, 100), color='red')
    image_io = io.BytesIO()
    image.save(image_io, format='JPEG')
    image_io.seek(0)
    
    return SimpleUploadedFile(
        name='test_image.jpg',
        content=image_io.getvalue(),
        content_type='image/jpeg'
    )


def test_menu_page_with_images():
    """Test that the menu management page displays images correctly."""
    print("Testing Menu Management Page with Images...")
    
    # Create test client and staff user
    client = Client()
    staff_user = User.objects.create_user(
        username='admin',
        email='<EMAIL>',
        password='testpass',
        is_staff=True
    )
    client.login(username='admin', password='testpass')
    
    # Create a menu item with image
    test_image = create_test_image()
    menu_item = MenuItem.objects.create(
        name='Test Item with Image',
        description='Test item for image display',
        category='main',
        price_per_person=Decimal('15.00'),
        is_available=True,
        image=test_image
    )
    
    # Test menu management page
    response = client.get('/management/menu/')
    print(f"   Status: {response.status_code}")
    assert response.status_code == 200
    
    content = response.content.decode()
    assert 'Test Item with Image' in content
    assert 'test_image' in content  # Image filename should appear in src
    
    print("   [PASS] Menu page displays images correctly")


def test_menu_item_creation_with_image():
    """Test creating a menu item with image upload."""
    print("Testing Menu Item Creation with Image...")
    
    client = Client()
    staff_user = User.objects.create_user(
        username='admin2',
        email='<EMAIL>',
        password='testpass',
        is_staff=True
    )
    client.login(username='admin2', password='testpass')
    
    # Create test image
    test_image = create_test_image()
    
    # Test creating a new menu item with image
    response = client.post('/management/menu/create/', {
        'name': 'Pizza with Image',
        'description': 'Delicious pizza with uploaded image',
        'category': 'main',
        'price_per_person': '18.99',
        'is_available': 'on',
        'image': test_image
    })
    
    print(f"   Status: {response.status_code}")
    assert response.status_code == 200
    
    # Check JSON response
    data = response.json()
    assert data['success'] == True
    assert 'Pizza with Image' in data['message']
    assert 'image_url' in data['menu_item']
    assert data['menu_item']['image_url'] is not None
    
    # Verify item was created in database with image
    menu_item = MenuItem.objects.get(name='Pizza with Image')
    assert menu_item.image is not None
    assert 'test_image' in menu_item.image.name
    
    print("   [PASS] Menu item created successfully with image")


def test_menu_item_update_with_image():
    """Test updating a menu item with new image."""
    print("Testing Menu Item Update with Image...")
    
    client = Client()
    staff_user = User.objects.create_user(
        username='admin3',
        email='<EMAIL>',
        password='testpass',
        is_staff=True
    )
    client.login(username='admin3', password='testpass')
    
    # Create a menu item without image first
    menu_item = MenuItem.objects.create(
        name='Item to Update',
        description='Original description',
        category='appetizer',
        price_per_person=Decimal('10.00'),
        is_available=True
    )
    
    # Create test image
    test_image = create_test_image()
    
    # Test updating the menu item with image
    response = client.post(f'/management/menu/update/{menu_item.id}/', {
        'name': 'Updated Item with Image',
        'description': 'Updated description with image',
        'category': 'main',
        'price_per_person': '12.50',
        'is_available': 'on',
        'image': test_image
    })
    
    print(f"   Status: {response.status_code}")
    assert response.status_code == 200
    
    # Check JSON response
    data = response.json()
    assert data['success'] == True
    assert 'Updated Item with Image' in data['message']
    assert data['menu_item']['image_url'] is not None
    
    # Verify item was updated in database
    menu_item.refresh_from_db()
    assert menu_item.name == 'Updated Item with Image'
    assert menu_item.image is not None
    
    print("   [PASS] Menu item updated successfully with image")


def test_image_validation():
    """Test image upload validation."""
    print("Testing Image Upload Validation...")
    
    client = Client()
    staff_user = User.objects.create_user(
        username='admin4',
        email='<EMAIL>',
        password='testpass',
        is_staff=True
    )
    client.login(username='admin4', password='testpass')
    
    # Test with oversized image (simulate large file)
    large_image_content = b'x' * (6 * 1024 * 1024)  # 6MB
    large_image = SimpleUploadedFile(
        name='large_image.jpg',
        content=large_image_content,
        content_type='image/jpeg'
    )
    
    response = client.post('/management/menu/create/', {
        'name': 'Test Item',
        'description': 'Test description',
        'category': 'main',
        'price_per_person': '15.00',
        'is_available': 'on',
        'image': large_image
    })
    
    assert response.status_code == 400
    data = response.json()
    assert 'too large' in data['error']
    print("   [PASS] Large image validation works")
    
    # Test with invalid file type
    text_file = SimpleUploadedFile(
        name='test.txt',
        content=b'This is not an image',
        content_type='text/plain'
    )
    
    response = client.post('/management/menu/create/', {
        'name': 'Test Item',
        'description': 'Test description',
        'category': 'main',
        'price_per_person': '15.00',
        'is_available': 'on',
        'image': text_file
    })
    
    assert response.status_code == 400
    data = response.json()
    assert 'Invalid image format' in data['error']
    print("   [PASS] File type validation works")


def test_image_removal():
    """Test removing image from menu item."""
    print("Testing Image Removal...")
    
    client = Client()
    staff_user = User.objects.create_user(
        username='admin5',
        email='<EMAIL>',
        password='testpass',
        is_staff=True
    )
    client.login(username='admin5', password='testpass')
    
    # Create a menu item with image
    test_image = create_test_image()
    menu_item = MenuItem.objects.create(
        name='Item with Image to Remove',
        description='Test item',
        category='dessert',
        price_per_person=Decimal('8.00'),
        is_available=True,
        image=test_image
    )
    
    # Verify image exists
    assert menu_item.image is not None
    
    # Test updating to remove image
    response = client.post(f'/management/menu/update/{menu_item.id}/', {
        'name': 'Item without Image',
        'description': 'Updated description',
        'category': 'dessert',
        'price_per_person': '8.00',
        'is_available': 'on',
        'remove_image': 'true'
    })
    
    print(f"   Status: {response.status_code}")
    assert response.status_code == 200
    
    data = response.json()
    assert data['success'] == True
    assert data['menu_item']['image_url'] is None
    
    # Verify image was removed from database
    menu_item.refresh_from_db()
    assert not menu_item.image
    
    print("   [PASS] Image removal works correctly")


def test_visual_display():
    """Test that images are properly displayed in the management interface."""
    print("Testing Visual Display of Images...")
    
    client = Client()
    staff_user = User.objects.create_user(
        username='admin6',
        email='<EMAIL>',
        password='testpass',
        is_staff=True
    )
    client.login(username='admin6', password='testpass')
    
    # Create menu items with and without images
    test_image = create_test_image()
    
    # Item with image
    MenuItem.objects.create(
        name='Item with Image',
        description='Has image',
        category='main',
        price_per_person=Decimal('20.00'),
        is_available=True,
        image=test_image
    )
    
    # Item without image
    MenuItem.objects.create(
        name='Item without Image',
        description='No image',
        category='appetizer',
        price_per_person=Decimal('10.00'),
        is_available=True
    )
    
    # Test menu page display
    response = client.get('/management/menu/')
    content = response.content.decode()
    
    # Check for image-related HTML elements
    assert 'object-cover' in content  # Image styling class
    assert 'No Image' in content  # Placeholder text for items without images
    assert 'h-48' in content  # Image container height class
    
    print("   [PASS] Visual display elements are present")


if __name__ == '__main__':
    print("Starting Enhanced Menu Management Tests with Images...")
    print("=" * 60)
    
    try:
        # Clean up any existing test data
        User.objects.filter(username__startswith='admin').delete()
        MenuItem.objects.all().delete()
        
        # Run tests
        test_menu_page_with_images()
        test_menu_item_creation_with_image()
        test_menu_item_update_with_image()
        test_image_validation()
        test_image_removal()
        test_visual_display()
        
        print("\n" + "=" * 60)
        print("ALL TESTS PASSED! Enhanced Menu Management with Images is working correctly.")
        print("Image upload, validation, display, and management features are fully functional!")
        
    except Exception as e:
        print(f"\n[FAIL] Test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
    
    finally:
        # Clean up test data
        User.objects.filter(username__startswith='admin').delete()
        MenuItem.objects.all().delete()
        
        # Clean up test images
        import shutil
        media_path = os.path.join(os.path.dirname(__file__), 'media', 'menu_items')
        if os.path.exists(media_path):
            try:
                shutil.rmtree(media_path)
            except:
                pass
