{% extends 'base.html' %}

{% block title %}Book Your Event - BonAppetit Catering{% endblock %}

{% block content %}
<div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <div class="bg-white rounded-lg shadow-lg p-6 md:p-8">
        <h1 class="text-3xl font-bold text-charcoal mb-6 text-center">Book Your Catering Event</h1>
        
        <!-- Booking Form with HTMX and Unpoly -->
        <form hx-post="{% url 'savory_events:htmx_booking_submit' %}"
              hx-target="#booking-response"
              hx-swap="innerHTML"
              hx-indicator="#loading-indicator"
              up-submit
              up-target="#booking-response"
              x-data="bookingForm()"
              class="space-y-6">
            {% csrf_token %}
            
            <!-- Customer Information -->
            <div class="border-b border-sage-green pb-6">
                <h2 class="text-xl font-semibold text-charcoal mb-4">Contact Information</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label for="customer_name" class="block text-sm font-medium text-charcoal mb-2">Full Name *</label>
                        <input type="text" 
                               id="customer_name" 
                               name="customer_name" 
                               x-model="formData.name"
                               @input="validateField('name')"
                               class="w-full px-3 py-2 border border-sage-green rounded-md focus:outline-none focus:ring-2 focus:ring-burnt-orange"
                               :class="{'border-red-500': errors.name}"
                               required>
                        <div x-show="errors.name" class="text-red-500 text-sm mt-1" x-text="errors.name"></div>
                    </div>
                    <div>
                        <label for="customer_email" class="block text-sm font-medium text-charcoal mb-2">Email Address *</label>
                        <input type="email" 
                               id="customer_email" 
                               name="customer_email" 
                               x-model="formData.email"
                               @input="validateField('email')"
                               class="w-full px-3 py-2 border border-sage-green rounded-md focus:outline-none focus:ring-2 focus:ring-burnt-orange"
                               :class="{'border-red-500': errors.email}"
                               required>
                        <div x-show="errors.email" class="text-red-500 text-sm mt-1" x-text="errors.email"></div>
                    </div>
                    <div>
                        <label for="customer_phone" class="block text-sm font-medium text-charcoal mb-2">Phone Number *</label>
                        <input type="tel" 
                               id="customer_phone" 
                               name="customer_phone" 
                               x-model="formData.phone"
                               @input="validateField('phone')"
                               class="w-full px-3 py-2 border border-sage-green rounded-md focus:outline-none focus:ring-2 focus:ring-burnt-orange"
                               :class="{'border-red-500': errors.phone}"
                               required>
                        <div x-show="errors.phone" class="text-red-500 text-sm mt-1" x-text="errors.phone"></div>
                    </div>
                </div>
            </div>
            
            <!-- Event Details -->
            <div class="border-b border-sage-green pb-6">
                <h2 class="text-xl font-semibold text-charcoal mb-4">Event Details</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label for="event_date" class="block text-sm font-medium text-charcoal mb-2">Event Date *</label>
                        <input type="date" 
                               id="event_date" 
                               name="event_date" 
                               x-model="formData.eventDate"
                               @input="validateField('eventDate')"
                               class="w-full px-3 py-2 border border-sage-green rounded-md focus:outline-none focus:ring-2 focus:ring-burnt-orange"
                               :class="{'border-red-500': errors.eventDate}"
                               required>
                        <div x-show="errors.eventDate" class="text-red-500 text-sm mt-1" x-text="errors.eventDate"></div>
                    </div>
                    <div>
                        <label for="event_time" class="block text-sm font-medium text-charcoal mb-2">Event Time *</label>
                        <input type="time" 
                               id="event_time" 
                               name="event_time" 
                               x-model="formData.eventTime"
                               class="w-full px-3 py-2 border border-sage-green rounded-md focus:outline-none focus:ring-2 focus:ring-burnt-orange"
                               required>
                    </div>
                    <div>
                        <label for="event_type" class="block text-sm font-medium text-charcoal mb-2">Event Type *</label>
                        <select id="event_type" 
                                name="event_type" 
                                x-model="formData.eventType"
                                class="w-full px-3 py-2 border border-sage-green rounded-md focus:outline-none focus:ring-2 focus:ring-burnt-orange"
                                required>
                            <option value="">Select Event Type</option>
                            <option value="wedding">Wedding</option>
                            <option value="corporate">Corporate Event</option>
                            <option value="birthday">Birthday Party</option>
                            <option value="anniversary">Anniversary</option>
                            <option value="other">Other</option>
                        </select>
                    </div>
                    <div>
                        <label for="guest_count" class="block text-sm font-medium text-charcoal mb-2">Number of Guests *</label>
                        <input type="number" 
                               id="guest_count" 
                               name="guest_count" 
                               x-model="formData.guestCount"
                               @input="validateField('guestCount')"
                               min="1" 
                               class="w-full px-3 py-2 border border-sage-green rounded-md focus:outline-none focus:ring-2 focus:ring-burnt-orange"
                               :class="{'border-red-500': errors.guestCount}"
                               required>
                        <div x-show="errors.guestCount" class="text-red-500 text-sm mt-1" x-text="errors.guestCount"></div>
                    </div>
                </div>
                <div class="mt-4">
                    <label for="venue_address" class="block text-sm font-medium text-charcoal mb-2">Venue Address *</label>
                    <textarea id="venue_address" 
                              name="venue_address" 
                              x-model="formData.venueAddress"
                              rows="3" 
                              class="w-full px-3 py-2 border border-sage-green rounded-md focus:outline-none focus:ring-2 focus:ring-burnt-orange"
                              required></textarea>
                </div>
            </div>

            <!-- Menu Selection -->
            <div class="border-b border-sage-green pb-6">
                <h2 class="text-xl font-semibold text-charcoal mb-4">Menu Selection</h2>
                <p class="text-sm text-gray-600 mb-4">Select menu items for your event. You can also use our <a href="{% url 'savory_events:booking_wizard' %}" class="text-burnt-orange hover:underline">Booking Wizard</a> for a guided experience.</p>

                {% if menu_items %}
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 max-h-64 overflow-y-auto">
                        {% for item in menu_items %}
                        <div class="flex items-start space-x-3 p-3 border border-sage-green rounded-md hover:bg-cream transition-colors">
                            <input type="checkbox" id="menu_item_{{ item.id }}" name="selected_menu_items" value="{{ item.id }}"
                                   class="mt-1 text-burnt-orange focus:ring-burnt-orange">
                            <div class="flex-1">
                                <label for="menu_item_{{ item.id }}" class="block text-sm font-medium text-charcoal cursor-pointer">
                                    {{ item.name }}
                                </label>
                                <p class="text-xs text-gray-600 mt-1">{{ item.description|truncatewords:8 }}</p>
                                <p class="text-xs text-burnt-orange font-medium mt-1">
                                    ${{ item.price_per_person }} per person
                                </p>
                                <span class="inline-block text-xs bg-sage-green text-white px-2 py-1 rounded-full mt-1">
                                    {{ item.get_category_display }}
                                </span>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <p class="text-gray-600">No menu items available at the moment. Please contact us for custom menu options.</p>
                {% endif %}
            </div>

            <!-- Sound System Selection -->
            <div class="border-b border-sage-green pb-6">
                <h2 class="text-xl font-semibold text-charcoal mb-4">Sound System Services (Optional)</h2>
                <div class="space-y-3">
                    <div class="flex items-center">
                        <input type="radio" id="sound_none" name="sound_system" value="" checked class="mr-3">
                        <label for="sound_none" class="text-sm">No sound system needed</label>
                    </div>

                    {% if sound_systems %}
                        {% for system in sound_systems %}
                        <div class="flex items-start space-x-3 p-3 border border-sage-green rounded-md hover:bg-cream transition-colors">
                            <input type="radio" id="sound_{{ system.id }}" name="sound_system" value="{{ system.package_type }}" class="mt-1">
                            <div class="flex-1">
                                <label for="sound_{{ system.id }}" class="block text-sm font-medium text-charcoal cursor-pointer">
                                    {{ system.name }}
                                </label>
                                <p class="text-xs text-gray-600 mt-1">{{ system.description|truncatewords:10 }}</p>
                                <p class="text-xs text-burnt-orange font-medium mt-1">
                                    ${{ system.price }}
                                </p>
                                <span class="inline-block text-xs bg-burnt-orange text-white px-2 py-1 rounded-full mt-1">
                                    {{ system.get_package_type_display }}
                                </span>
                            </div>
                        </div>
                        {% endfor %}
                    {% endif %}
                </div>
            </div>
            
            <!-- Special Requests -->
            <div class="border-b border-sage-green pb-6">
                <h2 class="text-xl font-semibold text-charcoal mb-4">Special Requests</h2>
                <textarea id="special_requests" 
                          name="special_requests" 
                          x-model="formData.specialRequests"
                          rows="4" 
                          placeholder="Any dietary restrictions, special accommodations, or additional requests..."
                          class="w-full px-3 py-2 border border-sage-green rounded-md focus:outline-none focus:ring-2 focus:ring-burnt-orange"></textarea>
            </div>
            
            <!-- Cost Summary -->
            <div class="bg-cream p-4 rounded-lg">
                <h3 class="text-lg font-semibold text-charcoal mb-2">Estimated Cost</h3>
                <div class="space-y-1 text-sm">
                    <div class="flex justify-between">
                        <span>Base catering (per person):</span>
                        <span>$25.00</span>
                    </div>
                    <div class="flex justify-between">
                        <span>Sound system:</span>
                        <span x-text="'$' + soundSystemCost + '.00'"></span>
                    </div>
                    <div class="border-t border-sage-green pt-1 mt-2">
                        <div class="flex justify-between font-semibold">
                            <span>Estimated Total:</span>
                            <span x-text="'$' + calculateTotal() + '.00'"></span>
                        </div>
                        <div class="text-xs text-gray-600 mt-1">
                            50% deposit required to secure booking
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Submit Button -->
            <div class="text-center">
                <button type="submit" 
                        :disabled="!isFormValid()"
                        class="bg-burnt-orange hover:bg-opacity-80 disabled:bg-gray-400 disabled:cursor-not-allowed text-white font-bold py-3 px-8 rounded-lg transition-colors">
                    <span id="loading-indicator" class="htmx-indicator">
                        <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white inline" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Processing...
                    </span>
                    <span class="htmx-indicator:not(.htmx-request)">Submit Booking Request</span>
                </button>
            </div>
        </form>
        
        <!-- HTMX Response Container -->
        <div id="booking-response" class="mt-6"></div>
    </div>
</div>

<script>
function bookingForm() {
    return {
        formData: {
            name: '',
            email: '',
            phone: '',
            eventDate: '',
            eventTime: '',
            eventType: '',
            guestCount: '',
            venueAddress: '',
            soundSystem: [],
            specialRequests: ''
        },
        errors: {},
        soundSystemCost: 0,
        
        validateField(field) {
            switch(field) {
                case 'name':
                    this.errors.name = this.formData.name.length < 2 ? 'Name must be at least 2 characters' : '';
                    break;
                case 'email':
                    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                    this.errors.email = !emailRegex.test(this.formData.email) ? 'Please enter a valid email address' : '';
                    break;
                case 'phone':
                    const phoneRegex = /^[\d\s\-\+\(\)]+$/;
                    this.errors.phone = !phoneRegex.test(this.formData.phone) ? 'Please enter a valid phone number' : '';
                    break;
                case 'eventDate':
                    const selectedDate = new Date(this.formData.eventDate);
                    const today = new Date();
                    this.errors.eventDate = selectedDate <= today ? 'Event date must be in the future' : '';
                    break;
                case 'guestCount':
                    this.errors.guestCount = this.formData.guestCount < 1 ? 'Must have at least 1 guest' : '';
                    break;
            }
        },
        
        updateSoundSystemCost() {
            const costs = {
                'none': 0,
                'microphones': 150,
                'dj': 400,
                'full_audio': 600
            };
            
            this.soundSystemCost = 0;
            if (Array.isArray(this.formData.soundSystem)) {
                this.formData.soundSystem.forEach(system => {
                    if (costs[system]) {
                        this.soundSystemCost += costs[system];
                    }
                });
            }
        },
        
        calculateTotal() {
            const basePerPerson = 25;
            const guestCount = parseInt(this.formData.guestCount) || 0;
            return (basePerPerson * guestCount) + this.soundSystemCost;
        },
        
        isFormValid() {
            return this.formData.name && 
                   this.formData.email && 
                   this.formData.phone && 
                   this.formData.eventDate && 
                   this.formData.eventTime && 
                   this.formData.eventType && 
                   this.formData.guestCount &&
                   this.formData.venueAddress &&
                   !Object.values(this.errors).some(error => error);
        }
    }
}
</script>
{% endblock %}
