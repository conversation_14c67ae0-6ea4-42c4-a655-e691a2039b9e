"""
Services for BonAppetit catering application.
"""

import os
import json
import logging
from typing import Dict, List, Optional, Tuple
from decimal import Decimal
from datetime import datetime, timedelta

import google.generativeai as genai
from django.conf import settings
from django.core.cache import cache
from django.utils import timezone

from .models import MenuItem, SoundSystemService, Booking, Customer, ChatConversation, ChatMessage

logger = logging.getLogger(__name__)


class ChatbotService:
    """Service for handling chatbot interactions using Gemini AI."""

    def __init__(self):
        """Initialize the chatbot service."""
        self.api_key = os.getenv('GEMINI_API_KEY')
        if not self.api_key:
            raise ValueError("GEMINI_API_KEY environment variable is required")

        genai.configure(api_key=self.api_key)
        # For older version of google-generativeai
        try:
            self.model = genai.GenerativeModel('gemini-pro')
        except AttributeError:
            # Fallback for older API
            self.model = None
        
        # Cache knowledge base for 1 hour
        self.knowledge_base_cache_key = 'chatbot_knowledge_base'
        self.knowledge_base_cache_timeout = 3600
    
    def get_knowledge_base(self) -> str:
        """Get comprehensive knowledge base about BonAppetit services."""
        cached_knowledge = cache.get(self.knowledge_base_cache_key)
        if cached_knowledge:
            return cached_knowledge
        
        # Build knowledge base from current data
        knowledge_base = self._build_knowledge_base()
        cache.set(self.knowledge_base_cache_key, knowledge_base, self.knowledge_base_cache_timeout)
        return knowledge_base
    
    def _build_knowledge_base(self) -> str:
        """Build comprehensive knowledge base from database."""
        knowledge_parts = []
        
        # Company information
        knowledge_parts.append("""
        COMPANY INFORMATION:
        - Company Name: BonAppetit Catering Services
        - Specialization: Professional catering for weddings, corporate events, birthday parties, anniversaries, and other special occasions
        - Service Area: Local area (specific locations can be discussed during booking)
        - Business Hours: Monday-Friday 9 AM - 6 PM, Saturday 10 AM - 4 PM, Closed Sundays
        - Contact: Available through website contact form or phone during business hours
        - Booking Process: Online booking system with menu selection, guest count specification, and partial payment options
        """)
        
        # Menu items
        menu_items = MenuItem.objects.filter(is_available=True).order_by('category', 'name')
        if menu_items.exists():
            knowledge_parts.append("\nMENU ITEMS:")
            current_category = None
            for item in menu_items:
                if item.category != current_category:
                    current_category = item.category
                    knowledge_parts.append(f"\n{item.get_category_display().upper()}:")
                
                knowledge_parts.append(
                    f"- {item.name}: {item.description} (${item.price_per_person:.2f} per person)"
                )
        
        # Sound system services
        sound_services = SoundSystemService.objects.filter(is_available=True).order_by('package_type', 'name')
        if sound_services.exists():
            knowledge_parts.append("\n\nSOUND SYSTEM SERVICES:")
            for service in sound_services:
                knowledge_parts.append(
                    f"- {service.name} ({service.get_package_type_display()}): {service.description} (${service.price:.2f})"
                )
        
        # Event types and policies
        knowledge_parts.append("""
        
        EVENT TYPES SUPPORTED:
        - Weddings: Full-service catering with customizable menus
        - Corporate Events: Professional catering for business meetings, conferences, and company parties
        - Birthday Parties: Fun and festive catering for all ages
        - Anniversaries: Elegant catering for milestone celebrations
        - Other Events: Custom catering solutions for various occasions
        
        BOOKING POLICIES:
        - Minimum guest count: 10 people
        - Advance booking required: At least 7 days for small events, 14+ days for large events (50+ guests)
        - Payment: Partial payment required to secure booking, full payment due before event
        - Cancellation: 48-hour notice required for cancellations
        - Dietary restrictions: We accommodate vegetarian, vegan, gluten-free, and other dietary needs
        - Venue requirements: We can cater at your venue or recommend partner venues
        
        PRICING STRUCTURE:
        - Menu items priced per person
        - Sound system services have flat rates
        - Final pricing depends on guest count, menu selection, and additional services
        - Discounts may be available for large events or repeat customers
        """)
        
        return "".join(knowledge_parts)
    
    def generate_response(self, user_message: str, conversation_context: List[Dict] = None) -> Tuple[str, bool]:
        """
        Generate AI response to user message.
        
        Args:
            user_message: The user's message
            conversation_context: Previous conversation messages for context
            
        Returns:
            Tuple of (response_text, should_escalate)
        """
        try:
            # Build conversation prompt
            prompt = self._build_conversation_prompt(user_message, conversation_context)

            # Generate response
            if self.model:
                response = self.model.generate_content(prompt)
                response_text = response.text.strip()
            else:
                # Fallback for older API or when model is not available
                response_text = self._get_rule_based_response(user_message)

            # Check if escalation is needed
            should_escalate = self._should_escalate(user_message, response_text)

            return response_text, should_escalate

        except Exception as e:
            logger.error(f"Error generating chatbot response: {str(e)}")
            return self._get_fallback_response(), False
    
    def _build_conversation_prompt(self, user_message: str, conversation_context: List[Dict] = None) -> str:
        """Build the complete prompt for the AI model."""
        knowledge_base = self.get_knowledge_base()
        
        prompt_parts = [
            "You are a helpful customer service assistant for BonAppetit Catering Services.",
            "You should be friendly, professional, and knowledgeable about our catering services.",
            "Always provide accurate information based on the knowledge base provided.",
            "If you don't know something specific, politely say so and offer to connect them with a human representative.",
            "Keep responses concise but informative.",
            "Use a warm, welcoming tone that reflects our premium catering brand.",
            "",
            "KNOWLEDGE BASE:",
            knowledge_base,
            "",
            "CONVERSATION GUIDELINES:",
            "- Answer questions about menu items, pricing, services, and booking process",
            "- Help customers understand our catering options and make informed decisions",
            "- Provide specific pricing when asked (use the per-person rates from the knowledge base)",
            "- Suggest appropriate menu combinations for different event types",
            "- Explain our booking process and requirements clearly",
            "- If asked about availability for specific dates, explain that they need to use our booking system",
            "- For complex requests or complaints, offer to escalate to a human representative",
            "",
        ]
        
        # Add conversation context if available
        if conversation_context:
            prompt_parts.append("PREVIOUS CONVERSATION:")
            for msg in conversation_context[-5:]:  # Last 5 messages for context
                role = "Customer" if msg['type'] == 'user' else "Assistant"
                prompt_parts.append(f"{role}: {msg['content']}")
            prompt_parts.append("")
        
        prompt_parts.extend([
            f"Customer: {user_message}",
            "Assistant:"
        ])
        
        return "\n".join(prompt_parts)
    
    def _should_escalate(self, user_message: str, response_text: str) -> bool:
        """Determine if the conversation should be escalated to human support."""
        escalation_keywords = [
            'complaint', 'angry', 'upset', 'disappointed', 'refund', 'cancel booking',
            'speak to manager', 'human representative', 'not satisfied', 'poor service',
            'food poisoning', 'allergic reaction', 'emergency', 'urgent'
        ]
        
        user_lower = user_message.lower()
        response_lower = response_text.lower()
        
        # Check for escalation keywords in user message
        if any(keyword in user_lower for keyword in escalation_keywords):
            return True
        
        # Check if AI response indicates uncertainty or inability to help
        uncertainty_phrases = [
            "i don't know", "i'm not sure", "i can't help", "contact our team",
            "speak with a representative", "human assistance"
        ]
        
        if any(phrase in response_lower for phrase in uncertainty_phrases):
            return True
        
        return False
    
    def _get_rule_based_response(self, user_message: str) -> str:
        """Generate rule-based response when AI is not available."""
        message_lower = user_message.lower()

        # Menu-related questions
        if any(word in message_lower for word in ['menu', 'food', 'dish', 'appetizer', 'main', 'dessert', 'drink']):
            menu_items = MenuItem.objects.filter(is_available=True)[:5]
            if menu_items:
                items_text = ", ".join([f"{item.name} (${item.price_per_person})" for item in menu_items])
                return f"Here are some of our popular menu items: {items_text}. We have many more options available! Would you like to see our full menu or learn about a specific category?"
            else:
                return "We offer a variety of delicious menu options for your event. Please visit our menu page or contact us for detailed information about our current offerings."

        # Pricing questions
        elif any(word in message_lower for word in ['price', 'cost', 'pricing', 'expensive', 'cheap', 'budget']):
            return "Our pricing is based on per-person rates for menu items, plus flat rates for additional services. Menu items typically range from $6-$35 per person. For an accurate quote, I'd be happy to help you with our booking process where you can select your preferences and get detailed pricing."

        # Booking questions
        elif any(word in message_lower for word in ['book', 'booking', 'reserve', 'event', 'wedding', 'party']):
            return "I'd be happy to help you book your event! Our booking process is simple: choose your event date, guest count, select menu items, and add any additional services like sound systems. You can start the booking process on our website, and we'll guide you through each step."

        # Sound system questions
        elif any(word in message_lower for word in ['sound', 'audio', 'microphone', 'dj', 'music']):
            return "We offer several sound system packages: DJ Package, Microphones Only, and Full Audio Set. These range from basic microphone setups to complete DJ services. Would you like more details about our audio services?"

        # Contact/hours questions
        elif any(word in message_lower for word in ['contact', 'phone', 'email', 'hours', 'open', 'closed']):
            return "You can reach us Monday-Friday 9 AM - 6 PM, Saturday 10 AM - 4 PM. We're closed Sundays. You can also use our contact form on the website for non-urgent inquiries. How can I help you with your catering needs today?"

        # Greeting
        elif any(word in message_lower for word in ['hello', 'hi', 'hey', 'good morning', 'good afternoon']):
            return "Hello! Welcome to BonAppetit Catering Services. I'm here to help you with information about our catering options, menu items, pricing, and booking process. What would you like to know about our services?"

        # Default response
        else:
            return "Thank you for your question! I'm here to help with information about our catering services, menu options, pricing, and booking process. Could you please let me know what specific aspect of our catering services you'd like to learn about?"

    def _get_fallback_response(self) -> str:
        """Get fallback response when AI generation fails."""
        return (
            "I apologize, but I'm experiencing some technical difficulties right now. "
            "Please try again in a moment, or feel free to contact our team directly "
            "through our contact form for immediate assistance with your catering needs."
        )
    
    def get_quick_actions(self) -> List[Dict[str, str]]:
        """Get list of quick action buttons for the chat interface."""
        return [
            {"text": "View Menu", "action": "show_menu"},
            {"text": "Pricing Info", "action": "pricing_info"},
            {"text": "Book Event", "action": "book_event"},
            {"text": "Sound Systems", "action": "sound_systems"},
            {"text": "Contact Info", "action": "contact_info"},
            {"text": "Speak to Human", "action": "escalate"},
        ]
    
    def handle_quick_action(self, action: str) -> str:
        """Handle quick action button clicks."""
        action_responses = {
            "show_menu": "I'd be happy to help you explore our menu! We offer appetizers, main dishes, desserts, and beverages. What type of food are you interested in for your event?",
            "pricing_info": "Our pricing is based on per-person rates for menu items, plus flat rates for additional services like sound systems. Could you tell me about your event so I can provide specific pricing?",
            "book_event": "Great! I can help you get started with booking. You'll need to provide your event date, guest count, and venue. Would you like me to guide you through our booking process?",
            "sound_systems": "We offer three types of sound system packages: DJ Package, Microphones Only, and Full Audio Set. What kind of audio setup are you looking for?",
            "contact_info": "You can reach us Monday-Friday 9 AM - 6 PM, Saturday 10 AM - 4 PM. We're closed Sundays. You can also use our contact form on the website for non-urgent inquiries.",
            "escalate": "I'll connect you with one of our human representatives who can provide more detailed assistance. Please hold on while I escalate your conversation."
        }
        
        return action_responses.get(action, "I'm not sure how to help with that. Could you please rephrase your question?")


class ChatSessionService:
    """Service for managing chat sessions and conversations."""
    
    @staticmethod
    def get_or_create_conversation(session_id: str, user=None) -> ChatConversation:
        """Get existing conversation or create new one."""
        conversation, created = ChatConversation.objects.get_or_create(
            session_id=session_id,
            defaults={
                'user': user,
                'is_active': True
            }
        )
        
        # Update user if provided and conversation exists
        if not created and user and not conversation.user:
            conversation.user = user
            conversation.save()
        
        return conversation
    
    @staticmethod
    def add_message(conversation: ChatConversation, message_type: str, content: str, metadata: Dict = None) -> ChatMessage:
        """Add message to conversation."""
        metadata_json = json.dumps(metadata or {})
        message = ChatMessage.objects.create(
            conversation=conversation,
            message_type=message_type,
            content=content,
            metadata=metadata_json
        )

        # Update conversation timestamp
        conversation.save()  # This will update the updated_at field

        return message
    
    @staticmethod
    def get_conversation_context(conversation: ChatConversation, limit: int = 10) -> List[Dict]:
        """Get recent messages for conversation context."""
        messages = conversation.messages.filter(is_visible=True).order_by('-created_at')[:limit]
        
        context = []
        for message in reversed(messages):
            context.append({
                'type': message.message_type,
                'content': message.content,
                'timestamp': message.created_at.isoformat()
            })
        
        return context
