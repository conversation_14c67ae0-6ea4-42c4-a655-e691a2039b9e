<!DOCTYPE html>
<html lang="en" class="h-full bg-gray-50">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Management Dashboard{% endblock %} | BonAppetit</title>
    
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Alpine.js CDN -->
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
    
    <!-- HTMX CDN -->
    <script src="https://unpkg.com/htmx.org@1.9.8"></script>
    
    <!-- Chart.js CDN -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <style>
        /* Custom color palette */
        :root {
            --deep-olive: #556B2F;
            --burnt-orange: #D35400;
            --cream: #FFF5E1;
            --charcoal: #2C2C2C;
        }
        
        .bg-deep-olive { background-color: var(--deep-olive); }
        .bg-burnt-orange { background-color: var(--burnt-orange); }
        .bg-cream { background-color: var(--cream); }
        .bg-charcoal { background-color: var(--charcoal); }
        
        .text-deep-olive { color: var(--deep-olive); }
        .text-burnt-orange { color: var(--burnt-orange); }
        .text-cream { color: var(--cream); }
        .text-charcoal { color: var(--charcoal); }
        
        .border-deep-olive { border-color: var(--deep-olive); }
        .border-burnt-orange { border-color: var(--burnt-orange); }
        
        .hover\:bg-deep-olive:hover { background-color: var(--deep-olive); }
        .hover\:bg-burnt-orange:hover { background-color: var(--burnt-orange); }
        
        /* Custom animations */
        .fade-in {
            animation: fadeIn 0.3s ease-in;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .stat-card {
            transition: all 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body class="h-full bg-gray-50" x-data="{ sidebarOpen: false }">
    
    <!-- Mobile sidebar -->
    <div x-show="sidebarOpen" 
         x-transition:enter="transition-opacity ease-linear duration-300"
         x-transition:enter-start="opacity-0"
         x-transition:enter-end="opacity-100"
         x-transition:leave="transition-opacity ease-linear duration-300"
         x-transition:leave-start="opacity-100"
         x-transition:leave-end="opacity-0"
         class="fixed inset-0 flex z-40 md:hidden">
        
        <div x-show="sidebarOpen"
             x-transition:enter="transition ease-in-out duration-300 transform"
             x-transition:enter-start="-translate-x-full"
             x-transition:enter-end="translate-x-0"
             x-transition:leave="transition ease-in-out duration-300 transform"
             x-transition:leave-start="translate-x-0"
             x-transition:leave-end="-translate-x-full"
             class="relative flex-1 flex flex-col max-w-xs w-full bg-charcoal">
            
            <div class="absolute top-0 right-0 -mr-12 pt-2">
                <button @click="sidebarOpen = false" 
                        class="ml-1 flex items-center justify-center h-10 w-10 rounded-full focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white">
                    <i class="fas fa-times text-white"></i>
                </button>
            </div>
            
            {% if user.is_authenticated and user.is_staff %}
                {% include 'management/sidebar.html' %}
            {% else %}
                {% include 'customer/sidebar.html' %}
            {% endif %}
        </div>
        
        <div class="flex-shrink-0 w-14"></div>
    </div>

    <!-- Static sidebar for desktop -->
    <div class="hidden md:flex md:w-64 md:flex-col md:fixed md:inset-y-0">
        <div class="flex-1 flex flex-col min-h-0 bg-charcoal">
            {% if user.is_authenticated and user.is_staff %}
                {% include 'management/sidebar.html' %}
            {% else %}
                {% include 'customer/sidebar.html' %}
            {% endif %}
        </div>
    </div>

    <!-- Main content -->
    <div class="md:pl-64 flex flex-col flex-1">
        
        <!-- Top navigation -->
        <div class="sticky top-0 z-10 flex-shrink-0 flex h-16 bg-white shadow">
            <button @click="sidebarOpen = true" 
                    class="px-4 border-r border-gray-200 text-gray-500 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-deep-olive md:hidden">
                <i class="fas fa-bars"></i>
            </button>
            
            <div class="flex-1 px-4 flex justify-between items-center">
                <div class="flex-1">
                    <h1 class="text-2xl font-semibold text-charcoal">
                        {% block page_title %}Management Dashboard{% endblock %}
                    </h1>
                </div>
                
                <div class="ml-4 flex items-center md:ml-6 space-x-4">
                    <!-- Notifications -->
                    <button class="p-2 rounded-md text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-deep-olive">
                        <i class="fas fa-bell"></i>
                    </button>
                    
                    <!-- User menu -->
                    <div class="relative" x-data="{ open: false }">
                        <button @click="open = !open" 
                                class="max-w-xs bg-white flex items-center text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-deep-olive">
                            <div class="h-8 w-8 rounded-full bg-deep-olive flex items-center justify-center">
                                <i class="fas fa-user text-white text-sm"></i>
                            </div>
                            <span class="ml-2 text-sm font-medium text-gray-700">{{ user.username }}</span>
                            <i class="ml-2 fas fa-chevron-down text-gray-400"></i>
                        </button>
                        
                        <div x-show="open" 
                             x-transition:enter="transition ease-out duration-100"
                             x-transition:enter-start="transform opacity-0 scale-95"
                             x-transition:enter-end="transform opacity-100 scale-100"
                             x-transition:leave="transition ease-in duration-75"
                             x-transition:leave-start="transform opacity-100 scale-100"
                             x-transition:leave-end="transform opacity-0 scale-95"
                             @click.away="open = false"
                             class="origin-top-right absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5">
                            <div class="py-1">
                                <a href="{% url 'savory_events:home' %}" 
                                   class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                    <i class="fas fa-home mr-2"></i>View Site
                                </a>
                                <a href="{% url 'savory_events:customer_profile' %}" 
                                   class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                    <i class="fas fa-user-cog mr-2"></i>Profile
                                </a>
                                <a href="{% url 'savory_events:logout' %}" 
                                   class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                    <i class="fas fa-sign-out-alt mr-2"></i>Logout
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Page content -->
        <main class="flex-1 overflow-y-auto">
            <div class="py-6">
                {% if messages %}
                    <div class="max-w-7xl mx-auto px-4 sm:px-6 md:px-8 mb-6">
                        {% for message in messages %}
                            <div class="rounded-md {% if message.tags == 'error' %}bg-red-50{% else %}bg-green-50{% endif %} p-4 mb-4 fade-in">
                                <div class="flex">
                                    <div class="flex-shrink-0">
                                        <i class="fas {% if message.tags == 'error' %}fa-exclamation-circle text-red-400{% else %}fa-check-circle text-green-400{% endif %}"></i>
                                    </div>
                                    <div class="ml-3">
                                        <p class="text-sm font-medium {% if message.tags == 'error' %}text-red-800{% else %}text-green-800{% endif %}">{{ message }}</p>
                                    </div>
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                {% endif %}
                
                <div class="max-w-7xl mx-auto px-4 sm:px-6 md:px-8">
                    {% block content %}{% endblock %}
                </div>
            </div>
        </main>
    </div>

    <!-- Global scripts -->
    <script>
        // Auto-dismiss messages
        setTimeout(() => {
            document.querySelectorAll('.fade-in').forEach(el => {
                el.style.transition = 'opacity 0.5s';
                el.style.opacity = '0';
                setTimeout(() => el.remove(), 500);
            });
        }, 5000);
        
        // HTMX configuration
        document.body.addEventListener('htmx:configRequest', (evt) => {
            evt.detail.headers['X-CSRFToken'] = document.querySelector('[name=csrfmiddlewaretoken]').value;
        });
    </script>
    
    {% block extra_scripts %}{% endblock %}
</body>
</html>
