{% extends 'customer/base.html' %}

{% block title %}Book Your Event - BonAppetit Catering{% endblock %}

{% block page_title %}Book Your Event{% endblock %}

{% block page_subtitle %}Create a new catering booking for your special event{% endblock %}

{% block content %}
<div class="py-8">
    <div class="bg-white rounded-lg shadow-lg p-6 md:p-8">
        <h2 class="text-2xl font-bold text-charcoal mb-6 text-center">Book Your Catering Event</h2>
        
        <!-- Booking Form with HTMX and Unpoly -->
        <form hx-post="{% url 'savory_events:htmx_booking_submit' %}"
              hx-target="#booking-response"
              hx-swap="innerHTML"
              hx-indicator="#loading-indicator"
              up-submit
              up-target="#booking-response"
              x-data="bookingForm()"
              class="space-y-6">
            {% csrf_token %}
            
            <!-- Response container -->
            <div id="booking-response" class="mb-4"></div>
            
            <!-- Loading indicator -->
            <div id="loading-indicator" class="htmx-indicator">
                <div class="flex items-center justify-center py-4">
                    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-burnt-orange"></div>
                    <span class="ml-2 text-gray-600">Processing your booking...</span>
                </div>
            </div>
            
            <!-- Customer Information -->
            <div class="bg-cream rounded-lg p-6">
                <h3 class="text-lg font-semibold text-charcoal mb-4">Customer Information</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label for="customer_name" class="block text-sm font-medium text-charcoal mb-2">Full Name *</label>
                        <input type="text" id="customer_name" name="customer_name" required
                               value="{% if user.is_authenticated %}{{ user.get_full_name }}{% endif %}"
                               class="w-full px-3 py-2 border border-sage-green rounded-md focus:outline-none focus:ring-2 focus:ring-burnt-orange transition-colors">
                    </div>
                    <div>
                        <label for="customer_email" class="block text-sm font-medium text-charcoal mb-2">Email *</label>
                        <input type="email" id="customer_email" name="customer_email" required
                               value="{% if user.is_authenticated %}{{ user.email }}{% endif %}"
                               class="w-full px-3 py-2 border border-sage-green rounded-md focus:outline-none focus:ring-2 focus:ring-burnt-orange transition-colors">
                    </div>
                    <div class="md:col-span-2">
                        <label for="customer_phone" class="block text-sm font-medium text-charcoal mb-2">Phone Number *</label>
                        <input type="tel" id="customer_phone" name="customer_phone" required
                               value="{% if customer.phone %}{{ customer.phone }}{% endif %}"
                               class="w-full px-3 py-2 border border-sage-green rounded-md focus:outline-none focus:ring-2 focus:ring-burnt-orange transition-colors">
                    </div>
                </div>
            </div>
            
            <!-- Event Details -->
            <div class="bg-cream rounded-lg p-6">
                <h3 class="text-lg font-semibold text-charcoal mb-4">Event Details</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label for="event_type" class="block text-sm font-medium text-charcoal mb-2">Event Type *</label>
                        <select id="event_type" name="event_type" required
                                class="w-full px-3 py-2 border border-sage-green rounded-md focus:outline-none focus:ring-2 focus:ring-burnt-orange transition-colors">
                            <option value="">Select Event Type</option>
                            <option value="wedding">Wedding</option>
                            <option value="corporate">Corporate Event</option>
                            <option value="birthday">Birthday Party</option>
                            <option value="anniversary">Anniversary</option>
                            <option value="other">Other</option>
                        </select>
                    </div>
                    <div>
                        <label for="guest_count" class="block text-sm font-medium text-charcoal mb-2">Number of Guests *</label>
                        <input type="number" id="guest_count" name="guest_count" required min="1" max="1000"
                               class="w-full px-3 py-2 border border-sage-green rounded-md focus:outline-none focus:ring-2 focus:ring-burnt-orange transition-colors">
                    </div>
                    <div>
                        <label for="event_date" class="block text-sm font-medium text-charcoal mb-2">Event Date *</label>
                        <input type="date" id="event_date" name="event_date" required
                               class="w-full px-3 py-2 border border-sage-green rounded-md focus:outline-none focus:ring-2 focus:ring-burnt-orange transition-colors">
                    </div>
                    <div>
                        <label for="event_time" class="block text-sm font-medium text-charcoal mb-2">Event Time *</label>
                        <input type="time" id="event_time" name="event_time" required
                               class="w-full px-3 py-2 border border-sage-green rounded-md focus:outline-none focus:ring-2 focus:ring-burnt-orange transition-colors">
                    </div>
                    <div class="md:col-span-2">
                        <label for="venue_address" class="block text-sm font-medium text-charcoal mb-2">Venue Address *</label>
                        <textarea id="venue_address" name="venue_address" required rows="3"
                                  class="w-full px-3 py-2 border border-sage-green rounded-md focus:outline-none focus:ring-2 focus:ring-burnt-orange transition-colors"
                                  placeholder="Enter the complete venue address..."></textarea>
                    </div>
                </div>
            </div>
            
            <!-- Menu Selection -->
            <div class="bg-cream rounded-lg p-6">
                <h3 class="text-lg font-semibold text-charcoal mb-4">Menu Selection</h3>
                <p class="text-sm text-gray-600 mb-4">Select menu items for your event. You can also use our <a href="{% url 'savory_events:booking_wizard' %}" class="text-burnt-orange hover:underline">Booking Wizard</a> for a guided experience.</p>

                {% if menu_items %}
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 max-h-64 overflow-y-auto">
                        {% for item in menu_items %}
                        <div class="flex items-start space-x-3 p-3 border border-sage-green rounded-md hover:bg-white transition-colors">
                            <input type="checkbox" id="menu_item_{{ item.id }}" name="selected_menu_items" value="{{ item.id }}"
                                   class="mt-1 text-burnt-orange focus:ring-burnt-orange">
                            <div class="flex-1">
                                <label for="menu_item_{{ item.id }}" class="block text-sm font-medium text-charcoal cursor-pointer">
                                    {{ item.name }}
                                </label>
                                <p class="text-xs text-gray-600 mt-1">{{ item.description|truncatewords:8 }}</p>
                                <p class="text-xs text-burnt-orange font-medium mt-1">
                                    ${{ item.price_per_person }} per person
                                </p>
                                <span class="inline-block text-xs bg-sage-green text-white px-2 py-1 rounded-full mt-1">
                                    {{ item.get_category_display }}
                                </span>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <p class="text-gray-600">No menu items available at the moment. Please contact us for custom menu options.</p>
                {% endif %}
            </div>
            
            <!-- Sound System -->
            <div class="bg-cream rounded-lg p-6">
                <h3 class="text-lg font-semibold text-charcoal mb-4">Sound System (Optional)</h3>
                <div class="space-y-3">
                    <div class="flex items-center">
                        <input type="radio" id="sound_none" name="sound_system" value="" checked class="mr-3">
                        <label for="sound_none" class="text-sm">No sound system needed</label>
                    </div>

                    {% if sound_systems %}
                        {% for system in sound_systems %}
                        <div class="flex items-start space-x-3 p-3 border border-sage-green rounded-md hover:bg-white transition-colors">
                            <input type="radio" id="sound_{{ system.id }}" name="sound_system" value="{{ system.package_type }}" class="mt-1">
                            <div class="flex-1">
                                <label for="sound_{{ system.id }}" class="block text-sm font-medium text-charcoal cursor-pointer">
                                    {{ system.name }}
                                </label>
                                <p class="text-xs text-gray-600 mt-1">{{ system.description|truncatewords:10 }}</p>
                                <p class="text-xs text-burnt-orange font-medium mt-1">
                                    ${{ system.price }}
                                </p>
                                <span class="inline-block text-xs bg-burnt-orange text-white px-2 py-1 rounded-full mt-1">
                                    {{ system.get_package_type_display }}
                                </span>
                            </div>
                        </div>
                        {% endfor %}
                    {% endif %}
                </div>
            </div>
            
            <!-- Special Requests -->
            <div class="bg-cream rounded-lg p-6">
                <h3 class="text-lg font-semibold text-charcoal mb-4">Special Requests</h3>
                <textarea id="special_requests" name="special_requests" rows="4"
                          class="w-full px-3 py-2 border border-sage-green rounded-md focus:outline-none focus:ring-2 focus:ring-burnt-orange transition-colors"
                          placeholder="Any dietary restrictions, special arrangements, or additional requests..."></textarea>
            </div>
            
            <!-- Submit Button -->
            <div class="text-center">
                <button type="submit" 
                        class="bg-burnt-orange hover:bg-opacity-80 text-white font-bold py-3 px-8 rounded-lg transition-colors duration-200 shadow-md hover:shadow-lg">
                    <i class="fas fa-calendar-plus mr-2"></i>
                    Submit Booking Request
                </button>
            </div>
        </form>
    </div>
</div>

<script>
function bookingForm() {
    return {
        formData: {
            customer_name: '{% if user.is_authenticated %}{{ user.get_full_name }}{% endif %}',
            customer_email: '{% if user.is_authenticated %}{{ user.email }}{% endif %}',
            customer_phone: '{% if customer.phone %}{{ customer.phone }}{% endif %}',
            event_date: '',
            event_time: '',
            event_type: '',
            guest_count: '',
            venue_address: '',
            sound_system: '',
            special_requests: ''
        },
        errors: {},

        validateField(field) {
            switch(field) {
                case 'customer_name':
                    this.errors.customer_name = this.formData.customer_name.length < 2 ? 'Name must be at least 2 characters' : '';
                    break;
                case 'customer_email':
                    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                    this.errors.customer_email = !emailRegex.test(this.formData.customer_email) ? 'Please enter a valid email address' : '';
                    break;
                case 'customer_phone':
                    const phoneRegex = /^[\d\s\-\+\(\)]+$/;
                    this.errors.customer_phone = !phoneRegex.test(this.formData.customer_phone) ? 'Please enter a valid phone number' : '';
                    break;
                case 'event_date':
                    const selectedDate = new Date(this.formData.event_date);
                    const today = new Date();
                    this.errors.event_date = selectedDate <= today ? 'Event date must be in the future' : '';
                    break;
                case 'guest_count':
                    this.errors.guest_count = this.formData.guest_count < 1 ? 'Must have at least 1 guest' : '';
                    break;
            }
        },

        isFormValid() {
            return this.formData.customer_name &&
                   this.formData.customer_email &&
                   this.formData.customer_phone &&
                   this.formData.event_date &&
                   this.formData.event_time &&
                   this.formData.event_type &&
                   this.formData.guest_count &&
                   this.formData.venue_address &&
                   !Object.values(this.errors).some(error => error);
        }
    }
}
</script>
{% endblock %}
