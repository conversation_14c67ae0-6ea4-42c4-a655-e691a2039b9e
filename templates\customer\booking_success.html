{% extends 'customer/base.html' %}

{% block title %}Booking Confirmed - BonAppetit Catering{% endblock %}

{% block page_title %}Booking Confirmed{% endblock %}

{% block page_subtitle %}Your catering booking has been successfully submitted{% endblock %}

{% block content %}
<div class="py-8">
    <div class="max-w-4xl mx-auto">
        
        <!-- Success Message -->
        <div class="bg-green-50 border border-green-200 rounded-lg p-6 mb-8">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <i class="fas fa-check-circle text-green-400 text-3xl"></i>
                </div>
                <div class="ml-4">
                    <h2 class="text-xl font-semibold text-green-800">Booking Successfully Submitted!</h2>
                    <p class="text-green-700 mt-1">
                        Thank you, {{ customer.name }}! Your catering booking request has been received and is being processed.
                    </p>
                </div>
            </div>
        </div>
        
        <!-- Booking Details -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <h3 class="text-xl font-semibold text-charcoal mb-6">Booking Details</h3>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Event Information -->
                <div>
                    <h4 class="text-lg font-medium text-charcoal mb-4">Event Information</h4>
                    <div class="space-y-3">
                        <div class="flex justify-between">
                            <span class="text-gray-600">Booking ID:</span>
                            <span class="font-medium text-charcoal">#{{ booking.id }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Event Type:</span>
                            <span class="font-medium text-charcoal">{{ booking.get_event_type_display }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Date & Time:</span>
                            <span class="font-medium text-charcoal">{{ booking.event_date|date:"M d, Y g:i A" }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Guest Count:</span>
                            <span class="font-medium text-charcoal">{{ booking.guest_count }} guests</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Status:</span>
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">
                                {{ booking.get_status_display }}
                            </span>
                        </div>
                    </div>
                </div>
                
                <!-- Customer Information -->
                <div>
                    <h4 class="text-lg font-medium text-charcoal mb-4">Customer Information</h4>
                    <div class="space-y-3">
                        <div class="flex justify-between">
                            <span class="text-gray-600">Name:</span>
                            <span class="font-medium text-charcoal">{{ customer.name }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Email:</span>
                            <span class="font-medium text-charcoal">{{ customer.email }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Phone:</span>
                            <span class="font-medium text-charcoal">{{ customer.phone }}</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Venue Address -->
            <div class="mt-6">
                <h4 class="text-lg font-medium text-charcoal mb-2">Venue Address</h4>
                <p class="text-gray-600 bg-gray-50 p-3 rounded-md">{{ booking.venue_address }}</p>
            </div>
            
            <!-- Special Requests -->
            {% if booking.special_requests %}
            <div class="mt-6">
                <h4 class="text-lg font-medium text-charcoal mb-2">Special Requests</h4>
                <p class="text-gray-600 bg-gray-50 p-3 rounded-md">{{ booking.special_requests }}</p>
            </div>
            {% endif %}
        </div>
        
        <!-- Menu Items -->
        {% if booking.menu_items.exists %}
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <h3 class="text-xl font-semibold text-charcoal mb-6">Selected Menu Items</h3>
            
            <div class="space-y-4">
                {% for booking_item in booking.menu_items.all %}
                <div class="flex items-center justify-between border-b border-gray-200 pb-4">
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-sage-green rounded-lg flex items-center justify-center mr-4">
                            <i class="fas fa-utensils text-white"></i>
                        </div>
                        <div>
                            <h4 class="font-medium text-charcoal">{{ booking_item.menu_item.name }}</h4>
                            <p class="text-sm text-gray-600">{{ booking_item.menu_item.get_category_display }}</p>
                            <p class="text-sm text-gray-500">{{ booking_item.menu_item.description|truncatewords:10 }}</p>
                        </div>
                    </div>
                    <div class="text-right">
                        <p class="font-medium text-charcoal">
                            ${{ booking_item.menu_item.price_per_person }} × {{ booking.guest_count }} guests
                        </p>
                        <p class="text-sm text-gray-600">
                            Subtotal: ${{ booking_item.get_total_price }}
                        </p>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
        {% endif %}
        
        <!-- Sound System -->
        {% if booking.sound_system_booking %}
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <h3 class="text-xl font-semibold text-charcoal mb-6">Sound System Service</h3>
            
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-burnt-orange rounded-lg flex items-center justify-center mr-4">
                        <i class="fas fa-music text-white"></i>
                    </div>
                    <div>
                        <h4 class="font-medium text-charcoal">{{ booking.sound_system_booking.sound_system.name }}</h4>
                        <p class="text-sm text-gray-600">{{ booking.sound_system_booking.sound_system.get_package_type_display }}</p>
                        <p class="text-sm text-gray-500">{{ booking.sound_system_booking.sound_system.description|truncatewords:15 }}</p>
                    </div>
                </div>
                <div class="text-right">
                    <p class="font-medium text-charcoal">${{ booking.sound_system_booking.sound_system.price }}</p>
                </div>
            </div>
        </div>
        {% endif %}
        
        <!-- Total Cost -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <h3 class="text-xl font-semibold text-charcoal mb-4">Total Cost</h3>
            
            <div class="space-y-2">
                {% for booking_item in booking.menu_items.all %}
                <div class="flex justify-between text-gray-600">
                    <span>{{ booking_item.menu_item.name }} ({{ booking.guest_count }} guests)</span>
                    <span>${{ booking_item.get_total_price }}</span>
                </div>
                {% endfor %}
                
                {% if booking.sound_system_booking %}
                <div class="flex justify-between text-gray-600">
                    <span>{{ booking.sound_system_booking.sound_system.name }}</span>
                    <span>${{ booking.sound_system_booking.sound_system.price }}</span>
                </div>
                {% endif %}
                
                <div class="border-t border-gray-200 pt-2 mt-4">
                    <div class="flex justify-between text-xl font-bold text-charcoal">
                        <span>Total Amount:</span>
                        <span>${{ booking.total_amount }}</span>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Next Steps -->
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8">
            <h3 class="text-lg font-semibold text-blue-800 mb-4">What Happens Next?</h3>
            <div class="space-y-3 text-blue-700">
                <div class="flex items-start">
                    <i class="fas fa-clock text-blue-500 mt-1 mr-3"></i>
                    <p>Our team will review your booking request within 24 hours.</p>
                </div>
                <div class="flex items-start">
                    <i class="fas fa-phone text-blue-500 mt-1 mr-3"></i>
                    <p>We'll contact you to confirm details and finalize your booking.</p>
                </div>
                <div class="flex items-start">
                    <i class="fas fa-envelope text-blue-500 mt-1 mr-3"></i>
                    <p>You'll receive email updates about your booking status.</p>
                </div>
                <div class="flex items-start">
                    <i class="fas fa-credit-card text-blue-500 mt-1 mr-3"></i>
                    <p>Payment arrangements will be discussed during confirmation.</p>
                </div>
            </div>
        </div>
        
        <!-- Action Buttons -->
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <a href="{% url 'savory_events:customer_dashboard' %}" 
               class="bg-burnt-orange hover:bg-opacity-80 text-white px-6 py-3 rounded-md font-semibold text-center transition-colors">
                <i class="fas fa-tachometer-alt mr-2"></i>Go to Dashboard
            </a>
            <a href="{% url 'savory_events:customer_bookings' %}" 
               class="border border-burnt-orange text-burnt-orange hover:bg-burnt-orange hover:text-white px-6 py-3 rounded-md font-semibold text-center transition-colors">
                <i class="fas fa-list mr-2"></i>View All Bookings
            </a>
            <a href="{% url 'savory_events:booking_wizard' %}" 
               class="border border-gray-300 text-gray-700 hover:bg-gray-50 px-6 py-3 rounded-md font-semibold text-center transition-colors">
                <i class="fas fa-plus mr-2"></i>Book Another Event
            </a>
        </div>
    </div>
</div>
{% endblock %}
