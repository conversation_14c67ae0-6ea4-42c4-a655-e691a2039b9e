<!DOCTYPE html>
<html lang="en" class="h-full bg-gray-50">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Customer Dashboard{% endblock %} | BonAppetit</title>
    
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- HTMX CDN -->
    <script src="https://unpkg.com/htmx.org@1.9.10"></script>

    <!-- Alpine.js CDN -->
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>

    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <style>
        /* Custom color palette */
        :root {
            --deep-olive: #556B2F;
            --burnt-orange: #D35400;
            --cream: #FFF5E1;
            --charcoal: #2C2C2C;
            --sage-green: #A3B18A;
        }
        
        .bg-deep-olive { background-color: var(--deep-olive); }
        .bg-burnt-orange { background-color: var(--burnt-orange); }
        .bg-cream { background-color: var(--cream); }
        .bg-charcoal { background-color: var(--charcoal); }
        .bg-sage-green { background-color: var(--sage-green); }
        
        .text-deep-olive { color: var(--deep-olive); }
        .text-burnt-orange { color: var(--burnt-orange); }
        .text-cream { color: var(--cream); }
        .text-charcoal { color: var(--charcoal); }
        .text-sage-green { color: var(--sage-green); }
        
        .border-deep-olive { border-color: var(--deep-olive); }
        .border-burnt-orange { border-color: var(--burnt-orange); }
        .border-sage-green { border-color: var(--sage-green); }
        
        .hover\:bg-deep-olive:hover { background-color: var(--deep-olive); }
        .hover\:bg-burnt-orange:hover { background-color: var(--burnt-orange); }
        .hover\:bg-sage-green:hover { background-color: var(--sage-green); }
        
        /* Custom animations */
        .fade-in {
            animation: fadeIn 0.3s ease-in;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        /* Sidebar styles */
        .sidebar-transition {
            transition: all 0.3s ease-in-out;
        }
        
        /* Custom scrollbar */
        .custom-scrollbar::-webkit-scrollbar {
            width: 6px;
        }
        
        .custom-scrollbar::-webkit-scrollbar-track {
            background: #f1f1f1;
        }
        
        .custom-scrollbar::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 3px;
        }
        
        .custom-scrollbar::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }
    </style>
    
    {% block extra_head %}{% endblock %}
</head>
<body class="h-full bg-gray-50" x-data="{ sidebarOpen: false }">
    
    <!-- Mobile sidebar overlay -->
    <div x-show="sidebarOpen" 
         x-transition:enter="transition-opacity ease-linear duration-300"
         x-transition:enter-start="opacity-0"
         x-transition:enter-end="opacity-100"
         x-transition:leave="transition-opacity ease-linear duration-300"
         x-transition:leave-start="opacity-100"
         x-transition:leave-end="opacity-0"
         class="fixed inset-0 flex z-40 md:hidden">
        
        <!-- Overlay -->
        <div x-show="sidebarOpen" 
             @click="sidebarOpen = false"
             x-transition:enter="transition-opacity ease-linear duration-300"
             x-transition:enter-start="opacity-0"
             x-transition:enter-end="opacity-100"
             x-transition:leave="transition-opacity ease-linear duration-300"
             x-transition:leave-start="opacity-100"
             x-transition:leave-end="opacity-0"
             class="fixed inset-0 bg-gray-600 bg-opacity-75"></div>
        
        <!-- Mobile sidebar -->
        <div x-show="sidebarOpen"
             x-transition:enter="transition ease-in-out duration-300 transform"
             x-transition:enter-start="-translate-x-full"
             x-transition:enter-end="translate-x-0"
             x-transition:leave="transition ease-in-out duration-300 transform"
             x-transition:leave-start="translate-x-0"
             x-transition:leave-end="-translate-x-full"
             class="relative flex-1 flex flex-col max-w-xs w-full bg-white">
            
            <!-- Close button -->
            <div class="absolute top-0 right-0 -mr-12 pt-2">
                <button @click="sidebarOpen = false"
                        class="ml-1 flex items-center justify-center h-10 w-10 rounded-full focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white">
                    <i class="fas fa-times text-white"></i>
                </button>
            </div>
            
            {% include 'customer/sidebar.html' %}
        </div>
    </div>
    
    <!-- Desktop sidebar -->
    <div class="hidden md:flex md:w-64 md:flex-col md:fixed md:inset-y-0">
        <div class="flex-1 flex flex-col min-h-0 bg-white shadow-lg">
            {% include 'customer/sidebar.html' %}
        </div>
    </div>
    
    <!-- Main content -->
    <div class="md:pl-64 flex flex-col flex-1">
        <!-- Top bar for mobile -->
        <div class="sticky top-0 z-10 md:hidden pl-1 pt-1 sm:pl-3 sm:pt-3 bg-gray-50">
            <button @click="sidebarOpen = true"
                    class="-ml-0.5 -mt-0.5 h-12 w-12 inline-flex items-center justify-center rounded-md text-gray-500 hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-deep-olive">
                <i class="fas fa-bars text-xl"></i>
            </button>
        </div>
        
        <!-- Page header -->
        <div class="bg-white shadow-sm border-b border-gray-200">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 md:px-8">
                <div class="py-4">
                    <div class="flex items-center justify-between">
                        <div>
                            <h1 class="text-2xl font-bold text-gray-900">{% block page_title %}Dashboard{% endblock %}</h1>
                            <p class="mt-1 text-sm text-gray-500">{% block page_subtitle %}Welcome to your customer portal{% endblock %}</p>
                        </div>
                        <div class="flex items-center space-x-4">
                            <!-- User menu -->
                            <div class="relative" x-data="{ userMenuOpen: false }">
                                <button @click="userMenuOpen = !userMenuOpen"
                                        class="flex items-center text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-deep-olive">
                                    <div class="h-8 w-8 rounded-full bg-burnt-orange flex items-center justify-center">
                                        <span class="text-white font-medium text-sm">
                                            {{ user.first_name.0|default:user.username.0 }}{{ user.last_name.0|default:"" }}
                                        </span>
                                    </div>
                                    <span class="ml-2 text-gray-700 font-medium">{{ user.get_full_name|default:user.username }}</span>
                                    <i class="ml-1 fas fa-chevron-down text-gray-400"></i>
                                </button>
                                
                                <!-- User dropdown -->
                                <div x-show="userMenuOpen" 
                                     @click.away="userMenuOpen = false"
                                     x-transition:enter="transition ease-out duration-100"
                                     x-transition:enter-start="transform opacity-0 scale-95"
                                     x-transition:enter-end="transform opacity-100 scale-100"
                                     x-transition:leave="transition ease-in duration-75"
                                     x-transition:leave-start="transform opacity-100 scale-100"
                                     x-transition:leave-end="transform opacity-0 scale-95"
                                     class="origin-top-right absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 z-50">
                                    <div class="py-1">
                                        <a href="{% url 'savory_events:customer_profile' %}" 
                                           class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                            <i class="fas fa-user mr-2"></i>My Profile
                                        </a>
                                        <a href="{% url 'savory_events:logout' %}" 
                                           class="block px-4 py-2 text-sm text-red-600 hover:bg-red-50">
                                            <i class="fas fa-sign-out-alt mr-2"></i>Sign Out
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Main content area -->
        <main class="flex-1 relative overflow-y-auto focus:outline-none custom-scrollbar">
            <!-- Flash messages -->
            {% if messages %}
                <div class="max-w-7xl mx-auto px-4 sm:px-6 md:px-8 pt-4">
                    {% for message in messages %}
                        <div class="mb-4 fade-in">
                            <div class="rounded-md p-4 {% if message.tags == 'error' %}bg-red-50 border border-red-200{% elif message.tags == 'warning' %}bg-yellow-50 border border-yellow-200{% elif message.tags == 'success' %}bg-green-50 border border-green-200{% else %}bg-blue-50 border border-blue-200{% endif %}">
                                <div class="flex">
                                    <div class="flex-shrink-0">
                                        {% if message.tags == 'error' %}
                                            <i class="fas fa-exclamation-circle text-red-400"></i>
                                        {% elif message.tags == 'warning' %}
                                            <i class="fas fa-exclamation-triangle text-yellow-400"></i>
                                        {% elif message.tags == 'success' %}
                                            <i class="fas fa-check-circle text-green-400"></i>
                                        {% else %}
                                            <i class="fas fa-info-circle text-blue-400"></i>
                                        {% endif %}
                                    </div>
                                    <div class="ml-3">
                                        <p class="text-sm {% if message.tags == 'error' %}text-red-800{% elif message.tags == 'warning' %}text-yellow-800{% elif message.tags == 'success' %}text-green-800{% else %}text-blue-800{% endif %}">
                                            {{ message }}
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    {% endfor %}
                </div>
            {% endif %}
            
            <div class="max-w-7xl mx-auto px-4 sm:px-6 md:px-8">
                {% block content %}{% endblock %}
            </div>
        </main>
    </div>

    <!-- Global scripts -->
    <script>
        // Auto-dismiss messages
        setTimeout(() => {
            document.querySelectorAll('.fade-in').forEach(el => {
                el.style.transition = 'opacity 0.5s';
                el.style.opacity = '0';
                setTimeout(() => el.remove(), 500);
            });
        }, 5000);
    </script>
    
    {% block extra_scripts %}{% endblock %}
</body>
</html>
