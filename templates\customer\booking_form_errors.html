<!-- Booking Form Error Response Template -->
<div class="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
    <div class="flex items-center mb-3">
        <i class="fas fa-exclamation-triangle text-red-500 mr-2"></i>
        <h3 class="text-lg font-semibold text-red-800">Please Fix the Following Errors</h3>
    </div>
    
    {% if errors %}
        <div class="space-y-2">
            {% for field, field_errors in errors.items %}
                {% if field == 'general' %}
                    {% for error in field_errors %}
                        <div class="text-red-700">
                            <i class="fas fa-times-circle mr-1"></i>
                            {{ error }}
                        </div>
                    {% endfor %}
                {% else %}
                    {% for error in field_errors %}
                        <div class="text-red-700">
                            <i class="fas fa-times-circle mr-1"></i>
                            <strong>{{ field|title }}:</strong> {{ error }}
                        </div>
                    {% endfor %}
                {% endif %}
            {% endfor %}
        </div>
    {% endif %}
    
    {% if form.non_field_errors %}
        <div class="mt-3 space-y-2">
            {% for error in form.non_field_errors %}
                <div class="text-red-700">
                    <i class="fas fa-times-circle mr-1"></i>
                    {{ error }}
                </div>
            {% endfor %}
        </div>
    {% endif %}
    
    <div class="mt-4 text-sm text-red-600">
        <p>Please correct the errors above and try submitting again.</p>
    </div>
</div>
