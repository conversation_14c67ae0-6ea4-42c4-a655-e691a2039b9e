from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.db.models import Sum, Count, Avg, Q, F, DecimalField, Max
from django.db.models.functions import Coalesce
from django.core.paginator import Paginator
from django.core.exceptions import ValidationError
from django.utils import timezone
from django.http import JsonResponse
from django.views.decorators.http import require_http_methods
from django.views.decorators.csrf import csrf_exempt
from decimal import Decimal
from .models import (
    Booking, Customer, Payment, MenuItem, BookingMenuItem, SoundSystemService,
    BookingNote, BookingStatusHistory, BookingReminder, BookingStaffAssignment
)
import json


@login_required
def management_dashboard(request):
    """Modern management dashboard with key metrics and insights."""
    if not request.user.is_staff:
        return redirect('savory_events:login')
    
    # Core statistics
    stats = {
        'total_bookings': Booking.objects.count(),
        'active_bookings': Booking.objects.filter(status__in=['confirmed', 'partial_paid', 'fully_paid']).count(),
        'pending_bookings': Booking.objects.filter(status='pending').count(),
        'completed_bookings': Booking.objects.filter(status='completed').count(),
        'total_customers': Customer.objects.count(),
    }
    
    # Revenue metrics
    revenue = {
        'total': Booking.objects.filter(status__in=['fully_paid', 'completed']).aggregate(
            Sum('total_amount'))['total_amount__sum'] or Decimal('0.00'),
        'this_month': Booking.objects.filter(
            created_at__month=timezone.now().month,
            created_at__year=timezone.now().year,
            status__in=['fully_paid', 'completed']
        ).aggregate(Sum('total_amount'))['total_amount__sum'] or Decimal('0.00'),
        'pending': Booking.objects.filter(status__in=['confirmed', 'partial_paid']).aggregate(
            Sum('total_amount'))['total_amount__sum'] or Decimal('0.00'),
    }
    
    # Recent activity
    recent_bookings = Booking.objects.select_related('customer').order_by('-created_at')[:5]
    upcoming_events = Booking.objects.filter(
        event_date__gte=timezone.now(),
        status__in=['confirmed', 'partial_paid', 'fully_paid']
    ).select_related('customer').order_by('event_date')[:5]
    
    # Quick insights
    insights = {
        'avg_booking_value': Booking.objects.filter(total_amount__gt=0).aggregate(
            avg=Avg('total_amount'))['avg'] or Decimal('0.00'),
        'popular_event_type': Booking.objects.values('event_type').annotate(
            count=Count('id')).order_by('-count').first(),
        'busiest_month': Booking.objects.filter(event_date__year=timezone.now().year).extra(
            select={'month': "strftime('%%m', event_date)"}
        ).values('month').annotate(count=Count('id')).order_by('-count').first(),
    }
    
    context = {
        'stats': stats,
        'revenue': revenue,
        'recent_bookings': recent_bookings,
        'upcoming_events': upcoming_events,
        'insights': insights,
    }
    
    return render(request, 'management/dashboard.html', context)


@login_required
def management_bookings(request):
    """Comprehensive bookings management."""
    if not request.user.is_staff:
        return redirect('savory_events:login')
        
    bookings = Booking.objects.select_related('customer').prefetch_related('payments').order_by('-event_date')
    
    # Advanced filtering
    filters = {
        'status': request.GET.get('status', ''),
        'event_type': request.GET.get('event_type', ''),
        'date_from': request.GET.get('date_from', ''),
        'date_to': request.GET.get('date_to', ''),
        'search': request.GET.get('search', ''),
    }
    
    # Apply filters
    if filters['status']:
        bookings = bookings.filter(status=filters['status'])
    
    if filters['event_type']:
        bookings = bookings.filter(event_type=filters['event_type'])
    
    if filters['date_from']:
        try:
            date_from = timezone.datetime.strptime(filters['date_from'], '%Y-%m-%d').date()
            bookings = bookings.filter(event_date__date__gte=date_from)
        except ValueError:
            pass
    
    if filters['date_to']:
        try:
            date_to = timezone.datetime.strptime(filters['date_to'], '%Y-%m-%d').date()
            bookings = bookings.filter(event_date__date__lte=date_to)
        except ValueError:
            pass
    
    if filters['search']:
        bookings = bookings.filter(
            Q(customer__name__icontains=filters['search']) | 
            Q(customer__email__icontains=filters['search']) | 
            Q(venue_address__icontains=filters['search'])
        )
    
    # Pagination
    paginator = Paginator(bookings, 15)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    context = {
        'bookings': page_obj,
        'filters': filters,
        'status_choices': Booking.BOOKING_STATUS_CHOICES,
        'event_type_choices': Booking.EVENT_TYPE_CHOICES,
    }
    
    return render(request, 'management/bookings.html', context)


@login_required
def management_booking_detail(request, booking_id):
    """Detailed booking view with management actions."""
    if not request.user.is_staff:
        return redirect('savory_events:login')
        
    booking = get_object_or_404(
        Booking.objects.select_related('customer')
                      .prefetch_related('menu_items__menu_item', 'payments', 'sound_system_booking__sound_system'),
        id=booking_id
    )
    
    # Calculate financial details
    financial_summary = {
        'total_amount': booking.total_amount,
        'total_paid': booking.get_total_paid(),
        'remaining_balance': booking.get_remaining_balance(),
        'deposit_required': booking.get_deposit_amount(),
        'is_deposit_paid': booking.is_deposit_paid(),
        'is_fully_paid': booking.is_fully_paid(),
    }
    
    context = {
        'booking': booking,
        'financial_summary': financial_summary,
        'status_choices': Booking.BOOKING_STATUS_CHOICES,
        'payment_types': Payment.PAYMENT_TYPE_CHOICES,
    }

    # Check if enhanced view is requested
    if request.GET.get('enhanced') == 'true':
        return render(request, 'management/enhanced_booking_detail.html', context)

    return render(request, 'management/booking_detail.html', context)


@login_required
def management_customers(request):
    """Customer management with analytics."""
    if not request.user.is_staff:
        return redirect('savory_events:login')
        
    customers = Customer.objects.annotate(
        total_bookings=Count('bookings'),
        total_spent=Sum('bookings__total_amount', filter=Q(bookings__status__in=['fully_paid', 'completed'])),
        last_booking=Max('bookings__event_date')
    ).order_by('-created_at')
    
    # Search and filter
    search_query = request.GET.get('search', '')
    if search_query:
        customers = customers.filter(
            Q(name__icontains=search_query) | 
            Q(email__icontains=search_query) | 
            Q(phone__icontains=search_query)
        )
    
    # Sorting
    sort_by = request.GET.get('sort', 'created_at')
    valid_sorts = ['name', 'email', 'created_at', 'total_bookings', 'total_spent', 'last_booking']
    if sort_by in valid_sorts:
        if sort_by in ['total_bookings', 'total_spent']:
            customers = customers.order_by(f'-{sort_by}')
        else:
            customers = customers.order_by(f'-{sort_by}')
    
    # Pagination
    paginator = Paginator(customers, 12)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    context = {
        'customers': page_obj,
        'search_query': search_query,
        'sort_by': sort_by,
    }
    
    return render(request, 'management/customers.html', context)


@login_required
def management_analytics(request):
    """Advanced analytics and reporting."""
    if not request.user.is_staff:
        return redirect('savory_events:login')
        
    # Time-based analysis
    current_year = timezone.now().year
    monthly_data = []
    
    for month in range(1, 13):
        month_bookings = Booking.objects.filter(
            created_at__year=current_year,
            created_at__month=month
        )
        revenue = month_bookings.filter(
            status__in=['fully_paid', 'completed']
        ).aggregate(Sum('total_amount'))['total_amount__sum'] or Decimal('0.00')
        
        monthly_data.append({
            'month': month,
            'month_name': timezone.datetime(current_year, month, 1).strftime('%B'),
            'bookings': month_bookings.count(),
            'revenue': revenue,
        })
    
    # Event type analysis
    event_analysis = Booking.objects.values('event_type').annotate(
        count=Count('id'),
        total_revenue=Sum('total_amount', filter=Q(status__in=['fully_paid', 'completed'])),
        avg_value=Avg('total_amount')
    ).order_by('-count')
    
    # Menu item popularity
    popular_items = BookingMenuItem.objects.values('menu_item__name', 'menu_item__category').annotate(
        total_orders=Count('booking', distinct=True),
        total_quantity=Sum('quantity'),
        total_revenue=Sum(
            F('quantity') * F('menu_item__price_per_person') * F('booking__guest_count'),
            output_field=DecimalField()
        )
    ).order_by('-total_orders')[:10]
    
    # Customer insights
    customer_insights = {
        'repeat_customers': Customer.objects.annotate(
            booking_count=Count('bookings')
        ).filter(booking_count__gt=1).count(),
        'avg_customer_value': Customer.objects.annotate(
            total_spent=Sum('bookings__total_amount', filter=Q(bookings__status__in=['fully_paid', 'completed']))
        ).aggregate(avg=Avg('total_spent'))['avg'] or Decimal('0.00'),
        'top_customers': Customer.objects.annotate(
            total_spent=Sum('bookings__total_amount', filter=Q(bookings__status__in=['fully_paid', 'completed']))
        ).filter(total_spent__gt=0).order_by('-total_spent')[:5],
    }
    
    context = {
        'monthly_data': monthly_data,
        'event_analysis': event_analysis,
        'popular_items': popular_items,
        'customer_insights': customer_insights,
        'current_year': current_year,
    }
    
    return render(request, 'management/analytics.html', context)


@login_required
def management_payments(request):
    """Payment management and tracking."""
    if not request.user.is_staff:
        return redirect('savory_events:login')
        
    payments = Payment.objects.select_related('booking__customer').order_by('-payment_date')
    
    # Filters
    filters = {
        'payment_type': request.GET.get('payment_type', ''),
        'date_from': request.GET.get('date_from', ''),
        'date_to': request.GET.get('date_to', ''),
        'search': request.GET.get('search', ''),
    }
    
    # Apply filters
    if filters['payment_type']:
        payments = payments.filter(payment_type=filters['payment_type'])
    
    if filters['date_from']:
        try:
            date_from = timezone.datetime.strptime(filters['date_from'], '%Y-%m-%d').date()
            payments = payments.filter(payment_date__date__gte=date_from)
        except ValueError:
            pass
    
    if filters['date_to']:
        try:
            date_to = timezone.datetime.strptime(filters['date_to'], '%Y-%m-%d').date()
            payments = payments.filter(payment_date__date__lte=date_to)
        except ValueError:
            pass
    
    if filters['search']:
        payments = payments.filter(
            Q(booking__customer__name__icontains=filters['search']) |
            Q(booking__customer__email__icontains=filters['search']) |
            Q(notes__icontains=filters['search'])
        )
    
    # Summary statistics
    payment_summary = {
        'total_payments': payments.aggregate(Sum('amount'))['amount__sum'] or Decimal('0.00'),
        'payment_count': payments.count(),
        'avg_payment': payments.aggregate(Avg('amount'))['amount__avg'] or Decimal('0.00'),
    }
    
    # Outstanding balances
    outstanding_bookings = Booking.objects.filter(
        status__in=['confirmed', 'partial_paid']
    ).annotate(
        balance=F('total_amount') - Coalesce(Sum('payments__amount'), Decimal('0.00'))
    ).filter(balance__gt=0).select_related('customer').order_by('event_date')
    
    # Pagination
    paginator = Paginator(payments, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    context = {
        'payments': page_obj,
        'filters': filters,
        'payment_summary': payment_summary,
        'outstanding_bookings': outstanding_bookings,
        'payment_type_choices': Payment.PAYMENT_TYPE_CHOICES,
    }
    
    return render(request, 'management/payments.html', context)


@login_required
def management_add_payment(request, booking_id):
    """Add payment for a booking via AJAX."""
    if not request.user.is_staff:
        return JsonResponse({'error': 'Unauthorized'}, status=403)
        
    booking = get_object_or_404(Booking, id=booking_id)
    
    if request.method == 'POST':
        try:
            amount = Decimal(request.POST.get('amount', '0'))
            payment_type = request.POST.get('payment_type')
            notes = request.POST.get('notes', '')
            
            # Validate payment
            if amount <= 0:
                return JsonResponse({'error': 'Payment amount must be greater than 0'}, status=400)
            
            if amount > booking.get_remaining_balance():
                return JsonResponse({'error': 'Payment amount exceeds remaining balance'}, status=400)
            
            # Create payment
            payment = Payment.objects.create(
                booking=booking,
                amount=amount,
                payment_type=payment_type,
                notes=notes
            )
            
            return JsonResponse({
                'success': True,
                'message': f'Payment of ${amount} added successfully',
                'remaining_balance': float(booking.get_remaining_balance()),
                'total_paid': float(booking.get_total_paid())
            })
            
        except (ValueError, ValidationError) as e:
            return JsonResponse({'error': str(e)}, status=400)
    
    return JsonResponse({'error': 'Invalid request method'}, status=405)


@login_required
def management_update_booking_status(request, booking_id):
    """Update booking status via AJAX."""
    if not request.user.is_staff:
        return JsonResponse({'error': 'Unauthorized'}, status=403)
        
    booking = get_object_or_404(Booking, id=booking_id)
    
    if request.method == 'POST':
        new_status = request.POST.get('status')
        
        if new_status in dict(Booking.BOOKING_STATUS_CHOICES):
            booking.status = new_status
            booking.save()
            
            return JsonResponse({
                'success': True,
                'message': f'Booking status updated to {booking.get_status_display()}',
                'new_status': booking.get_status_display()
            })
        else:
            return JsonResponse({'error': 'Invalid status'}, status=400)
    
    return JsonResponse({'error': 'Invalid request method'}, status=405)


@login_required
def management_customer_detail(request, customer_id):
    """Detailed customer view with booking history."""
    if not request.user.is_staff:
        return redirect('savory_events:login')
        
    customer = get_object_or_404(Customer, id=customer_id)
    bookings = customer.bookings.all().order_by('-event_date')
    
    # Customer statistics
    stats = {
        'total_bookings': bookings.count(),
        'total_spent': bookings.filter(status__in=['fully_paid', 'completed']).aggregate(
            Sum('total_amount'))['total_amount__sum'] or Decimal('0.00'),
        'avg_booking_value': bookings.aggregate(Avg('total_amount'))['total_amount__avg'] or Decimal('0.00'),
        'last_booking_date': bookings.first().event_date if bookings.exists() else None,
    }
    
    context = {
        'customer': customer,
        'bookings': bookings,
        'stats': stats,
    }
    
    return render(request, 'management/customer_detail.html', context)


@login_required
def management_menu(request):
    """Menu management with dynamic CRUD operations."""
    if not request.user.is_staff:
        return redirect('savory_events:login')
    
    # Get all menu items with statistics
    menu_items = MenuItem.objects.annotate(
        order_count=Count('bookings__booking', distinct=True),
        total_revenue=Sum(
            F('bookings__quantity') * F('price_per_person') * F('bookings__booking__guest_count'),
            output_field=DecimalField()
        )
    ).order_by('category', 'name')
    
    # Apply filters
    category_filter = request.GET.get('category', '')
    search_query = request.GET.get('search', '')
    availability_filter = request.GET.get('availability', '')
    
    if category_filter:
        menu_items = menu_items.filter(category=category_filter)
    
    if search_query:
        menu_items = menu_items.filter(
            Q(name__icontains=search_query) |
            Q(description__icontains=search_query)
        )
    
    if availability_filter:
        is_available = availability_filter == 'available'
        menu_items = menu_items.filter(is_available=is_available)
    
    # Menu statistics
    stats = {
        'total_items': MenuItem.objects.count(),
        'available_items': MenuItem.objects.filter(is_available=True).count(),
        'categories': MenuItem.objects.values('category').annotate(count=Count('id')).order_by('category'),
        'avg_price': MenuItem.objects.aggregate(avg=Avg('price_per_person'))['avg'] or Decimal('0.00'),
    }
    
    context = {
        'menu_items': menu_items,
        'stats': stats,
        'category_filter': category_filter,
        'search_query': search_query,
        'availability_filter': availability_filter,
        'category_choices': MenuItem.CATEGORY_CHOICES,
    }
    
    return render(request, 'management/menu.html', context)


@login_required
@require_http_methods(["POST"])
def management_menu_create(request):
    """Create new menu item via AJAX."""
    if not request.user.is_staff:
        return JsonResponse({'error': 'Unauthorized'}, status=403)
    
    try:
        # Get form data
        name = request.POST.get('name', '').strip()
        description = request.POST.get('description', '').strip()
        category = request.POST.get('category', '')
        price_per_person = request.POST.get('price_per_person', '')
        is_available = request.POST.get('is_available') == 'on'
        image = request.FILES.get('image')
        
        # Validate required fields
        if not name:
            return JsonResponse({'error': 'Name is required'}, status=400)
        if not description:
            return JsonResponse({'error': 'Description is required'}, status=400)
        if not category:
            return JsonResponse({'error': 'Category is required'}, status=400)
        if not price_per_person:
            return JsonResponse({'error': 'Price per person is required'}, status=400)
        
        # Validate price
        try:
            price = Decimal(price_per_person)
            if price <= 0:
                return JsonResponse({'error': 'Price must be greater than 0'}, status=400)
        except (ValueError, TypeError):
            return JsonResponse({'error': 'Invalid price format'}, status=400)
        
        # Validate category
        valid_categories = [choice[0] for choice in MenuItem.CATEGORY_CHOICES]
        if category not in valid_categories:
            return JsonResponse({'error': 'Invalid category'}, status=400)
        
        # Validate image if provided
        if image:
            # Check file size (limit to 5MB)
            if image.size > 5 * 1024 * 1024:
                return JsonResponse({'error': 'Image file too large. Maximum size is 5MB.'}, status=400)
            
            # Check file type
            allowed_types = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp']
            if image.content_type not in allowed_types:
                return JsonResponse({'error': 'Invalid image format. Please use JPEG, PNG, GIF, or WebP.'}, status=400)
        
        # Create menu item
        menu_item = MenuItem.objects.create(
            name=name,
            description=description,
            category=category,
            price_per_person=price,
            is_available=is_available,
            image=image
        )
        
        return JsonResponse({
            'success': True,
            'message': f'Menu item "{name}" created successfully',
            'menu_item': {
                'id': menu_item.id,
                'name': menu_item.name,
                'description': menu_item.description,
                'category': menu_item.category,
                'category_display': menu_item.get_category_display(),
                'price_per_person': float(menu_item.price_per_person),
                'is_available': menu_item.is_available,
                'image_url': menu_item.image.url if menu_item.image else None,
                'created_at': menu_item.created_at.strftime('%Y-%m-%d %H:%M:%S'),
            }
        })
        
    except Exception as e:
        return JsonResponse({'error': f'Error creating menu item: {str(e)}'}, status=500)


@login_required
@require_http_methods(["POST"])
def management_menu_update(request, item_id):
    """Update menu item via AJAX."""
    if not request.user.is_staff:
        return JsonResponse({'error': 'Unauthorized'}, status=403)
    
    try:
        menu_item = get_object_or_404(MenuItem, id=item_id)
        
        # Get form data
        name = request.POST.get('name', '').strip()
        description = request.POST.get('description', '').strip()
        category = request.POST.get('category', '')
        price_per_person = request.POST.get('price_per_person', '')
        is_available = request.POST.get('is_available') == 'on'
        image = request.FILES.get('image')
        remove_image = request.POST.get('remove_image') == 'true'
        
        # Validate required fields
        if not name:
            return JsonResponse({'error': 'Name is required'}, status=400)
        if not description:
            return JsonResponse({'error': 'Description is required'}, status=400)
        if not category:
            return JsonResponse({'error': 'Category is required'}, status=400)
        if not price_per_person:
            return JsonResponse({'error': 'Price per person is required'}, status=400)
        
        # Validate price
        try:
            price = Decimal(price_per_person)
            if price <= 0:
                return JsonResponse({'error': 'Price must be greater than 0'}, status=400)
        except (ValueError, TypeError):
            return JsonResponse({'error': 'Invalid price format'}, status=400)
        
        # Validate category
        valid_categories = [choice[0] for choice in MenuItem.CATEGORY_CHOICES]
        if category not in valid_categories:
            return JsonResponse({'error': 'Invalid category'}, status=400)
        
        # Validate image if provided
        if image:
            # Check file size (limit to 5MB)
            if image.size > 5 * 1024 * 1024:
                return JsonResponse({'error': 'Image file too large. Maximum size is 5MB.'}, status=400)
            
            # Check file type
            allowed_types = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp']
            if image.content_type not in allowed_types:
                return JsonResponse({'error': 'Invalid image format. Please use JPEG, PNG, GIF, or WebP.'}, status=400)
        
        # Update menu item
        menu_item.name = name
        menu_item.description = description
        menu_item.category = category
        menu_item.price_per_person = price
        menu_item.is_available = is_available
        
        # Handle image update
        if remove_image:
            # Remove existing image
            if menu_item.image:
                menu_item.image.delete()
            menu_item.image = None
        elif image:
            # Replace existing image with new one
            if menu_item.image:
                menu_item.image.delete()
            menu_item.image = image
        
        menu_item.save()
        
        return JsonResponse({
            'success': True,
            'message': f'Menu item "{name}" updated successfully',
            'menu_item': {
                'id': menu_item.id,
                'name': menu_item.name,
                'description': menu_item.description,
                'category': menu_item.category,
                'category_display': menu_item.get_category_display(),
                'price_per_person': float(menu_item.price_per_person),
                'is_available': menu_item.is_available,
                'image_url': menu_item.image.url if menu_item.image else None,
                'updated_at': menu_item.updated_at.strftime('%Y-%m-%d %H:%M:%S'),
            }
        })
        
    except Exception as e:
        return JsonResponse({'error': f'Error updating menu item: {str(e)}'}, status=500)


@login_required
@require_http_methods(["POST"])
def management_menu_delete(request, item_id):
    """Delete menu item via AJAX."""
    if not request.user.is_staff:
        return JsonResponse({'error': 'Unauthorized'}, status=403)
    
    try:
        menu_item = get_object_or_404(MenuItem, id=item_id)
        
        # Check if item is used in any bookings
        booking_count = BookingMenuItem.objects.filter(menu_item=menu_item).count()
        if booking_count > 0:
            return JsonResponse({
                'error': f'Cannot delete menu item. It is used in {booking_count} booking(s). Consider marking it as unavailable instead.'
            }, status=400)
        
        item_name = menu_item.name
        menu_item.delete()
        
        return JsonResponse({
            'success': True,
            'message': f'Menu item "{item_name}" deleted successfully'
        })
        
    except Exception as e:
        return JsonResponse({'error': f'Error deleting menu item: {str(e)}'}, status=500)


@login_required
@require_http_methods(["POST"])
def management_menu_toggle_availability(request, item_id):
    """Toggle menu item availability via AJAX."""
    if not request.user.is_staff:
        return JsonResponse({'error': 'Unauthorized'}, status=403)
    
    try:
        menu_item = get_object_or_404(MenuItem, id=item_id)
        menu_item.is_available = not menu_item.is_available
        menu_item.save()
        
        status_text = "available" if menu_item.is_available else "unavailable"
        
        return JsonResponse({
            'success': True,
            'message': f'Menu item "{menu_item.name}" marked as {status_text}',
            'is_available': menu_item.is_available
        })
        
    except Exception as e:
        return JsonResponse({'error': f'Error toggling availability: {str(e)}'}, status=500)


@login_required
def management_menu_get_item(request, item_id):
    """Get menu item details for editing via AJAX."""
    if not request.user.is_staff:
        return JsonResponse({'error': 'Unauthorized'}, status=403)
    
    try:
        menu_item = get_object_or_404(MenuItem, id=item_id)
        
        return JsonResponse({
            'success': True,
            'menu_item': {
                'id': menu_item.id,
                'name': menu_item.name,
                'description': menu_item.description,
                'category': menu_item.category,
                'category_display': menu_item.get_category_display(),
                'price_per_person': float(menu_item.price_per_person),
                'is_available': menu_item.is_available,
                'image_url': menu_item.image.url if menu_item.image else None,
                'created_at': menu_item.created_at.strftime('%Y-%m-%d %H:%M:%S'),
                'updated_at': menu_item.updated_at.strftime('%Y-%m-%d %H:%M:%S'),
            }
        })
        
    except Exception as e:
        return JsonResponse({'error': f'Error getting menu item: {str(e)}'}, status=500)


# ============================================================================
# ENHANCED ADMIN BOOKING MANAGEMENT VIEWS
# ============================================================================

@login_required
@require_http_methods(["POST"])
def management_booking_edit(request, booking_id):
    """Edit booking details via AJAX."""
    if not request.user.is_staff:
        return JsonResponse({'error': 'Unauthorized'}, status=403)

    booking = get_object_or_404(Booking, id=booking_id)

    try:
        # Update basic booking details
        if 'event_date' in request.POST:
            event_date = request.POST.get('event_date')
            event_time = request.POST.get('event_time', '12:00')
            from datetime import datetime
            event_datetime = datetime.strptime(f"{event_date} {event_time}", '%Y-%m-%d %H:%M')
            event_datetime = timezone.make_aware(event_datetime)
            booking.event_date = event_datetime

        if 'guest_count' in request.POST:
            booking.guest_count = int(request.POST.get('guest_count'))

        if 'venue_address' in request.POST:
            booking.venue_address = request.POST.get('venue_address')

        if 'special_requests' in request.POST:
            booking.special_requests = request.POST.get('special_requests')

        if 'priority' in request.POST:
            booking.priority = request.POST.get('priority')

        if 'discount_amount' in request.POST:
            booking.discount_amount = Decimal(request.POST.get('discount_amount', '0.00'))

        if 'internal_notes' in request.POST:
            booking.internal_notes = request.POST.get('internal_notes')

        if 'assigned_coordinator' in request.POST:
            coordinator_id = request.POST.get('assigned_coordinator')
            if coordinator_id:
                from django.contrib.auth.models import User
                booking.assigned_coordinator = User.objects.get(id=coordinator_id)
            else:
                booking.assigned_coordinator = None

        booking.save()

        return JsonResponse({
            'success': True,
            'message': 'Booking updated successfully',
            'booking': {
                'id': booking.id,
                'event_date': booking.event_date.strftime('%Y-%m-%d %H:%M'),
                'guest_count': booking.guest_count,
                'venue_address': booking.venue_address,
                'priority': booking.get_priority_display(),
                'total_amount': str(booking.total_amount),
                'final_amount': str(booking.get_final_amount()),
            }
        })

    except Exception as e:
        return JsonResponse({'error': f'Error updating booking: {str(e)}'}, status=500)


@login_required
@require_http_methods(["POST"])
def management_booking_add_note(request, booking_id):
    """Add note to booking."""
    if not request.user.is_staff:
        return JsonResponse({'error': 'Unauthorized'}, status=403)

    booking = get_object_or_404(Booking, id=booking_id)

    try:
        note_text = request.POST.get('note', '').strip()
        priority = request.POST.get('priority', 'normal')
        is_internal = request.POST.get('is_internal', 'true') == 'true'

        if not note_text:
            return JsonResponse({'error': 'Note text is required'}, status=400)

        note = BookingNote.objects.create(
            booking=booking,
            staff_user=request.user,
            note=note_text,
            priority=priority,
            is_internal=is_internal
        )

        return JsonResponse({
            'success': True,
            'message': 'Note added successfully',
            'note': {
                'id': note.id,
                'note': note.note,
                'priority': note.get_priority_display(),
                'staff_user': note.staff_user.get_full_name() or note.staff_user.username,
                'created_at': note.created_at.strftime('%Y-%m-%d %H:%M'),
                'is_internal': note.is_internal,
            }
        })

    except Exception as e:
        return JsonResponse({'error': f'Error adding note: {str(e)}'}, status=500)


@login_required
@require_http_methods(["POST"])
def management_booking_update_status(request, booking_id):
    """Enhanced booking status update with history tracking."""
    if not request.user.is_staff:
        return JsonResponse({'error': 'Unauthorized'}, status=403)

    booking = get_object_or_404(Booking, id=booking_id)

    try:
        new_status = request.POST.get('status')
        reason = request.POST.get('reason', '')

        if new_status not in dict(Booking.BOOKING_STATUS_CHOICES):
            return JsonResponse({'error': 'Invalid status'}, status=400)

        old_status = booking.status

        # Create status history record
        if old_status != new_status:
            BookingStatusHistory.objects.create(
                booking=booking,
                old_status=old_status,
                new_status=new_status,
                changed_by=request.user,
                reason=reason
            )

            booking.status = new_status
            booking.save()

        return JsonResponse({
            'success': True,
            'message': f'Booking status updated to {booking.get_status_display()}',
            'new_status': booking.get_status_display(),
            'status_class': booking.get_status_display_class(),
            'completion_percentage': booking.get_completion_percentage(),
        })

    except Exception as e:
        return JsonResponse({'error': f'Error updating status: {str(e)}'}, status=500)


@login_required
@require_http_methods(["POST"])
def management_booking_add_reminder(request, booking_id):
    """Add reminder for booking."""
    if not request.user.is_staff:
        return JsonResponse({'error': 'Unauthorized'}, status=403)

    booking = get_object_or_404(Booking, id=booking_id)

    try:
        from django.contrib.auth.models import User

        title = request.POST.get('title', '').strip()
        description = request.POST.get('description', '').strip()
        reminder_type = request.POST.get('reminder_type', 'custom')
        due_date = request.POST.get('due_date')
        due_time = request.POST.get('due_time', '09:00')
        assigned_to_id = request.POST.get('assigned_to')

        if not title:
            return JsonResponse({'error': 'Title is required'}, status=400)

        if not due_date:
            return JsonResponse({'error': 'Due date is required'}, status=400)

        # Parse due date and time
        from datetime import datetime
        due_datetime = datetime.strptime(f"{due_date} {due_time}", '%Y-%m-%d %H:%M')
        due_datetime = timezone.make_aware(due_datetime)

        # Get assigned user
        assigned_to = request.user  # Default to current user
        if assigned_to_id:
            assigned_to = User.objects.get(id=assigned_to_id)

        reminder = BookingReminder.objects.create(
            booking=booking,
            assigned_to=assigned_to,
            reminder_type=reminder_type,
            title=title,
            description=description,
            due_date=due_datetime
        )

        return JsonResponse({
            'success': True,
            'message': 'Reminder added successfully',
            'reminder': {
                'id': reminder.id,
                'title': reminder.title,
                'type': reminder.get_reminder_type_display(),
                'due_date': reminder.due_date.strftime('%Y-%m-%d %H:%M'),
                'assigned_to': reminder.assigned_to.get_full_name() or reminder.assigned_to.username,
                'is_overdue': reminder.is_overdue(),
            }
        })

    except Exception as e:
        return JsonResponse({'error': f'Error adding reminder: {str(e)}'}, status=500)


@login_required
@require_http_methods(["POST"])
def management_booking_assign_staff(request, booking_id):
    """Assign staff to booking."""
    if not request.user.is_staff:
        return JsonResponse({'error': 'Unauthorized'}, status=403)

    booking = get_object_or_404(Booking, id=booking_id)

    try:
        from django.contrib.auth.models import User

        staff_id = request.POST.get('staff_id')
        role = request.POST.get('role')
        notes = request.POST.get('notes', '').strip()

        if not staff_id or not role:
            return JsonResponse({'error': 'Staff member and role are required'}, status=400)

        staff_user = User.objects.get(id=staff_id)

        # Create or update assignment
        assignment, created = BookingStaffAssignment.objects.get_or_create(
            booking=booking,
            staff_user=staff_user,
            role=role,
            defaults={
                'notes': notes,
                'assigned_by': request.user,
            }
        )

        if not created:
            assignment.notes = notes
            assignment.save()

        return JsonResponse({
            'success': True,
            'message': f'Staff assigned successfully',
            'assignment': {
                'id': assignment.id,
                'staff_name': assignment.staff_user.get_full_name() or assignment.staff_user.username,
                'role': assignment.get_role_display(),
                'notes': assignment.notes,
            }
        })

    except Exception as e:
        return JsonResponse({'error': f'Error assigning staff: {str(e)}'}, status=500)


@login_required
@require_http_methods(["POST"])
def management_booking_bulk_action(request):
    """Perform bulk actions on multiple bookings."""
    if not request.user.is_staff:
        return JsonResponse({'error': 'Unauthorized'}, status=403)

    try:
        booking_ids = request.POST.getlist('booking_ids[]')
        action = request.POST.get('action')

        if not booking_ids:
            return JsonResponse({'error': 'No bookings selected'}, status=400)

        bookings = Booking.objects.filter(id__in=booking_ids)
        updated_count = 0

        if action == 'update_status':
            new_status = request.POST.get('new_status')
            if new_status in dict(Booking.BOOKING_STATUS_CHOICES):
                for booking in bookings:
                    old_status = booking.status
                    if old_status != new_status:
                        BookingStatusHistory.objects.create(
                            booking=booking,
                            old_status=old_status,
                            new_status=new_status,
                            changed_by=request.user,
                            reason=f'Bulk update by {request.user.username}'
                        )
                        booking.status = new_status
                        booking.save()
                        updated_count += 1

        elif action == 'update_priority':
            new_priority = request.POST.get('new_priority')
            if new_priority in dict(Booking.PRIORITY_CHOICES):
                bookings.update(priority=new_priority)
                updated_count = bookings.count()

        elif action == 'assign_coordinator':
            coordinator_id = request.POST.get('coordinator_id')
            if coordinator_id:
                from django.contrib.auth.models import User
                coordinator = User.objects.get(id=coordinator_id)
                bookings.update(assigned_coordinator=coordinator)
                updated_count = bookings.count()

        return JsonResponse({
            'success': True,
            'message': f'Bulk action completed. {updated_count} bookings updated.',
            'updated_count': updated_count,
        })

    except Exception as e:
        return JsonResponse({'error': f'Error performing bulk action: {str(e)}'}, status=500)


@login_required
def management_booking_calendar(request):
    """Calendar view of all bookings."""
    if not request.user.is_staff:
        return redirect('savory_events:login')

    # Get date range for calendar
    from datetime import datetime, timedelta
    import calendar

    year = int(request.GET.get('year', timezone.now().year))
    month = int(request.GET.get('month', timezone.now().month))

    # Get first and last day of the month
    first_day = datetime(year, month, 1)
    last_day = datetime(year, month, calendar.monthrange(year, month)[1])

    # Get bookings for the month
    bookings = Booking.objects.filter(
        event_date__date__gte=first_day.date(),
        event_date__date__lte=last_day.date()
    ).select_related('customer').order_by('event_date')

    # Group bookings by date
    bookings_by_date = {}
    for booking in bookings:
        date_key = booking.event_date.date()
        if date_key not in bookings_by_date:
            bookings_by_date[date_key] = []
        bookings_by_date[date_key].append(booking)

    # Generate calendar data
    cal = calendar.monthcalendar(year, month)
    calendar_data = []

    for week in cal:
        week_data = []
        for day in week:
            if day == 0:
                week_data.append(None)
            else:
                date_obj = datetime(year, month, day).date()
                day_bookings = bookings_by_date.get(date_obj, [])
                week_data.append({
                    'day': day,
                    'date': date_obj,
                    'bookings': day_bookings,
                    'booking_count': len(day_bookings),
                    'has_conflicts': len(day_bookings) > 1,
                })
        calendar_data.append(week_data)

    # Navigation dates
    prev_month = first_day - timedelta(days=1)
    next_month = last_day + timedelta(days=1)

    context = {
        'calendar_data': calendar_data,
        'current_month': first_day,
        'prev_month': prev_month,
        'next_month': next_month,
        'bookings_by_date': bookings_by_date,
        'total_bookings': bookings.count(),
    }

    return render(request, 'management/booking_calendar.html', context)


@login_required
def management_booking_conflicts(request):
    """Detect and display booking conflicts."""
    if not request.user.is_staff:
        return redirect('savory_events:login')

    # Find potential conflicts (same date, overlapping times)
    from django.db.models import Count
    from datetime import timedelta

    # Group bookings by date
    conflict_dates = Booking.objects.filter(
        status__in=['confirmed', 'partial_paid', 'fully_paid']
    ).extra(
        select={'event_date_only': 'DATE(event_date)'}
    ).values('event_date_only').annotate(
        booking_count=Count('id')
    ).filter(booking_count__gt=1).order_by('event_date_only')

    conflicts = []
    for conflict_date in conflict_dates:
        date_str = conflict_date['event_date_only']
        date_bookings = Booking.objects.filter(
            event_date__date=date_str,
            status__in=['confirmed', 'partial_paid', 'fully_paid']
        ).select_related('customer').order_by('event_date')

        # Check for time overlaps (assuming 4-hour event duration)
        time_conflicts = []
        bookings_list = list(date_bookings)

        for i, booking1 in enumerate(bookings_list):
            for booking2 in bookings_list[i+1:]:
                # Calculate time difference
                time_diff = abs((booking1.event_date - booking2.event_date).total_seconds() / 3600)
                if time_diff < 4:  # Less than 4 hours apart
                    time_conflicts.append((booking1, booking2, time_diff))

        conflicts.append({
            'date': date_str,
            'bookings': date_bookings,
            'booking_count': len(bookings_list),
            'time_conflicts': time_conflicts,
        })

    context = {
        'conflicts': conflicts,
        'total_conflict_dates': len(conflicts),
    }

    return render(request, 'management/booking_conflicts.html', context)


@login_required
def management_booking_reports(request):
    """Generate booking reports and analytics."""
    if not request.user.is_staff:
        return redirect('savory_events:login')

    from datetime import datetime, timedelta
    from django.db.models import Avg, Sum, Count, Q

    # Date range for reports
    end_date = timezone.now().date()
    start_date = end_date - timedelta(days=30)  # Last 30 days

    if request.GET.get('start_date'):
        start_date = datetime.strptime(request.GET.get('start_date'), '%Y-%m-%d').date()
    if request.GET.get('end_date'):
        end_date = datetime.strptime(request.GET.get('end_date'), '%Y-%m-%d').date()

    # Basic statistics
    bookings_in_period = Booking.objects.filter(
        created_at__date__gte=start_date,
        created_at__date__lte=end_date
    )

    total_bookings = bookings_in_period.count()
    confirmed_bookings = bookings_in_period.filter(status='confirmed').count()
    completed_bookings = bookings_in_period.filter(status='completed').count()
    cancelled_bookings = bookings_in_period.filter(status='cancelled').count()

    stats = {
        'total_bookings': total_bookings,
        'confirmed_bookings': confirmed_bookings,
        'completed_bookings': completed_bookings,
        'cancelled_bookings': cancelled_bookings,
        'total_revenue': bookings_in_period.filter(
            status__in=['fully_paid', 'completed']
        ).aggregate(Sum('total_amount'))['total_amount__sum'] or Decimal('0.00'),
        'avg_booking_value': bookings_in_period.aggregate(
            Avg('total_amount'))['total_amount__avg'] or Decimal('0.00'),
        'avg_guest_count': bookings_in_period.aggregate(
            Avg('guest_count'))['guest_count__avg'] or 0,
        # Add calculated percentages
        'confirmation_rate': (confirmed_bookings * 100 / total_bookings) if total_bookings > 0 else 0,
        'completion_rate': (completed_bookings * 100 / total_bookings) if total_bookings > 0 else 0,
        'cancellation_rate': (cancelled_bookings * 100 / total_bookings) if total_bookings > 0 else 0,
        'revenue_per_day': 0,  # Will calculate below
    }

    # Calculate revenue per day
    if (end_date - start_date).days > 0:
        stats['revenue_per_day'] = stats['total_revenue'] / ((end_date - start_date).days + 1)
    else:
        stats['revenue_per_day'] = stats['total_revenue']

    # Bookings by status
    status_breakdown = bookings_in_period.values('status').annotate(
        count=Count('id'),
        revenue=Sum('total_amount')
    ).order_by('-count')

    # Add percentage calculations to status breakdown
    total_bookings = stats['total_bookings']
    for status_data in status_breakdown:
        if total_bookings > 0:
            status_data['percentage'] = (status_data['count'] * 100) / total_bookings
        else:
            status_data['percentage'] = 0

    # Bookings by event type
    event_type_breakdown = bookings_in_period.values('event_type').annotate(
        count=Count('id'),
        revenue=Sum('total_amount')
    ).order_by('-count')

    # Add percentage calculations to event type breakdown
    for event_data in event_type_breakdown:
        if total_bookings > 0:
            event_data['percentage'] = (event_data['count'] * 100) / total_bookings
        else:
            event_data['percentage'] = 0

    # Bookings by priority
    priority_breakdown = bookings_in_period.values('priority').annotate(
        count=Count('id')
    ).order_by('-count')

    # Top customers
    top_customers_raw = Customer.objects.annotate(
        booking_count=Count('bookings', filter=Q(
            bookings__created_at__date__gte=start_date,
            bookings__created_at__date__lte=end_date
        )),
        total_spent=Sum('bookings__total_amount', filter=Q(
            bookings__created_at__date__gte=start_date,
            bookings__created_at__date__lte=end_date,
            bookings__status__in=['fully_paid', 'completed']
        ))
    ).filter(booking_count__gt=0).order_by('-total_spent')[:10]

    # Calculate average per booking for each customer
    top_customers = []
    for customer in top_customers_raw:
        customer_data = {
            'id': customer.id,
            'name': customer.name,
            'email': customer.email,
            'booking_count': customer.booking_count,
            'total_spent': customer.total_spent or Decimal('0.00'),
            'avg_per_booking': Decimal('0.00')
        }

        if customer.booking_count > 0 and customer.total_spent:
            customer_data['avg_per_booking'] = customer.total_spent / customer.booking_count

        top_customers.append(customer_data)

    context = {
        'stats': stats,
        'status_breakdown': status_breakdown,
        'event_type_breakdown': event_type_breakdown,
        'priority_breakdown': priority_breakdown,
        'top_customers': top_customers,
        'start_date': start_date,
        'end_date': end_date,
        'date_range_days': (end_date - start_date).days + 1,
    }

    return render(request, 'management/booking_reports.html', context)
