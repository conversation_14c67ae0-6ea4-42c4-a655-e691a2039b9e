#!/usr/bin/env python
"""
Test script for the chatbot functionality.
"""

import os
import sys
import django
import json

# Setup Django
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'BonAppetit.settings')
django.setup()

from savory_events.services import ChatbotService, ChatSessionService
from savory_events.models import ChatConversation, ChatMessage, MenuItem, SoundSystemService

def test_knowledge_base():
    """Test knowledge base generation."""
    print("Testing Knowledge Base Generation...")
    
    chatbot = ChatbotService()
    knowledge_base = chatbot.get_knowledge_base()
    
    print(f"Knowledge base length: {len(knowledge_base)} characters")
    print("\nKnowledge base preview:")
    print(knowledge_base[:500] + "..." if len(knowledge_base) > 500 else knowledge_base)
    
    # Check if menu items are included
    menu_count = MenuItem.objects.filter(is_available=True).count()
    sound_count = SoundSystemService.objects.filter(is_available=True).count()
    
    print(f"\nAvailable menu items: {menu_count}")
    print(f"Available sound services: {sound_count}")
    
    return True

def test_quick_actions():
    """Test quick action responses."""
    print("\nTesting Quick Actions...")
    
    chatbot = ChatbotService()
    quick_actions = chatbot.get_quick_actions()
    
    print(f"Available quick actions: {len(quick_actions)}")
    for action in quick_actions:
        print(f"  - {action['text']}: {action['action']}")
    
    # Test a few quick actions
    test_actions = ['show_menu', 'pricing_info', 'book_event']
    for action in test_actions:
        response = chatbot.handle_quick_action(action)
        print(f"\nAction '{action}' response:")
        print(f"  {response[:100]}...")
    
    return True

def test_session_management():
    """Test chat session management."""
    print("\nTesting Session Management...")
    
    # Create a test conversation
    conversation = ChatSessionService.get_or_create_conversation("test-session-123")
    print(f"Created conversation: {conversation.session_id}")
    
    # Add some test messages
    user_msg = ChatSessionService.add_message(
        conversation, 
        'user', 
        'Hello, I need catering for my wedding',
        {'test': True}
    )
    
    bot_msg = ChatSessionService.add_message(
        conversation,
        'bot',
        'Congratulations on your upcoming wedding! I\'d be happy to help you with catering.',
        {'generated_at': '2024-01-01T12:00:00'}
    )
    
    print(f"Added user message: {user_msg.id}")
    print(f"Added bot message: {bot_msg.id}")
    
    # Get conversation context
    context = ChatSessionService.get_conversation_context(conversation)
    print(f"Conversation context: {len(context)} messages")
    
    return True

def test_ai_response():
    """Test AI response generation (if API key is available)."""
    print("\nTesting AI Response Generation...")
    
    try:
        chatbot = ChatbotService()
        
        # Test simple question
        test_message = "What menu items do you have for appetizers?"
        response, should_escalate = chatbot.generate_response(test_message)
        
        print(f"Test message: {test_message}")
        print(f"AI Response: {response[:200]}...")
        print(f"Should escalate: {should_escalate}")
        
        return True
        
    except Exception as e:
        print(f"AI response test failed (this is expected if no API key): {str(e)}")
        return False

def main():
    """Run all tests."""
    print("=" * 60)
    print("BonAppetit Chatbot Functionality Test")
    print("=" * 60)
    
    tests = [
        test_knowledge_base,
        test_quick_actions,
        test_session_management,
        test_ai_response,
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"Test {test.__name__} failed: {str(e)}")
            results.append(False)
    
    print("\n" + "=" * 60)
    print("Test Results:")
    print("=" * 60)
    
    for i, (test, result) in enumerate(zip(tests, results)):
        status = "PASS" if result else "FAIL"
        print(f"{i+1}. {test.__name__}: {status}")
    
    passed = sum(results)
    total = len(results)
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Chatbot is ready to use.")
    else:
        print("⚠️  Some tests failed. Check the implementation.")

if __name__ == "__main__":
    main()
