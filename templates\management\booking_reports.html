{% extends 'management/base.html' %}

{% block title %}Booking Reports & Analytics{% endblock %}
{% block page_title %}Booking Reports & Analytics{% endblock %}

{% block content %}
<!-- Reports Header -->
<div class="bg-white shadow rounded-lg mb-6">
    <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex items-center justify-between">
            <div>
                <h2 class="text-xl font-semibold text-gray-900">Booking Reports</h2>
                <p class="text-sm text-gray-500 mt-1">
                    Analytics and insights for {{ start_date|date:"M d, Y" }} - {{ end_date|date:"M d, Y" }}
                    ({{ date_range_days }} days)
                </p>
            </div>
            <div class="flex items-center space-x-4">
                <!-- Date Range Filter -->
                <form method="GET" class="flex items-center space-x-2">
                    <input type="date" name="start_date" value="{{ start_date|date:'Y-m-d' }}" 
                           class="border-gray-300 rounded-md text-sm">
                    <span class="text-gray-500">to</span>
                    <input type="date" name="end_date" value="{{ end_date|date:'Y-m-d' }}" 
                           class="border-gray-300 rounded-md text-sm">
                    <button type="submit" 
                            class="bg-blue-600 hover:bg-blue-700 text-white px-3 py-2 rounded-md text-sm">
                        Update
                    </button>
                </form>
                <button onclick="window.print()" 
                        class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                    <i class="fas fa-print mr-2"></i>Print Report
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Key Metrics -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
    <div class="bg-white shadow rounded-lg p-6">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <i class="fas fa-calendar-check text-blue-500 text-2xl"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-500">Total Bookings</p>
                <p class="text-2xl font-semibold text-gray-900">{{ stats.total_bookings }}</p>
            </div>
        </div>
    </div>
    
    <div class="bg-white shadow rounded-lg p-6">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <i class="fas fa-dollar-sign text-green-500 text-2xl"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-500">Total Revenue</p>
                <p class="text-2xl font-semibold text-gray-900">${{ stats.total_revenue|floatformat:2 }}</p>
            </div>
        </div>
    </div>
    
    <div class="bg-white shadow rounded-lg p-6">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <i class="fas fa-chart-line text-purple-500 text-2xl"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-500">Avg Booking Value</p>
                <p class="text-2xl font-semibold text-gray-900">${{ stats.avg_booking_value|floatformat:2 }}</p>
            </div>
        </div>
    </div>
    
    <div class="bg-white shadow rounded-lg p-6">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <i class="fas fa-users text-orange-500 text-2xl"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-500">Avg Guest Count</p>
                <p class="text-2xl font-semibold text-gray-900">{{ stats.avg_guest_count|floatformat:0 }}</p>
            </div>
        </div>
    </div>
</div>

<!-- Charts and Breakdowns -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
    
    <!-- Bookings by Status -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Bookings by Status</h3>
        </div>
        <div class="px-6 py-4">
            {% if status_breakdown %}
            <div class="space-y-4">
                {% for status_data in status_breakdown %}
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="w-4 h-4 rounded mr-3
                            {% if status_data.status == 'pending' %}bg-yellow-400
                            {% elif status_data.status == 'confirmed' %}bg-blue-400
                            {% elif status_data.status == 'partial_paid' %}bg-orange-400
                            {% elif status_data.status == 'fully_paid' %}bg-green-400
                            {% elif status_data.status == 'completed' %}bg-gray-400
                            {% else %}bg-red-400{% endif %}"></div>
                        <span class="text-sm font-medium text-gray-900">
                            {{ status_data.status|title|default:"Unknown" }}
                        </span>
                    </div>
                    <div class="text-right">
                        <div class="text-sm font-medium text-gray-900">{{ status_data.count }}</div>
                        <div class="text-xs text-gray-500">${{ status_data.revenue|default:0|floatformat:2 }}</div>
                    </div>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2">
                    <div class="h-2 rounded-full
                        {% if status_data.status == 'pending' %}bg-yellow-400
                        {% elif status_data.status == 'confirmed' %}bg-blue-400
                        {% elif status_data.status == 'partial_paid' %}bg-orange-400
                        {% elif status_data.status == 'fully_paid' %}bg-green-400
                        {% elif status_data.status == 'completed' %}bg-gray-400
                        {% else %}bg-red-400{% endif %}"
                        style="width: {{ status_data.percentage|floatformat:1 }}%"></div>
                </div>
                {% endfor %}
            </div>
            {% else %}
            <p class="text-gray-500 text-sm">No data available for this period.</p>
            {% endif %}
        </div>
    </div>
    
    <!-- Bookings by Event Type -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Bookings by Event Type</h3>
        </div>
        <div class="px-6 py-4">
            {% if event_type_breakdown %}
            <div class="space-y-4">
                {% for event_data in event_type_breakdown %}
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="w-4 h-4 bg-blue-400 rounded mr-3"></div>
                        <span class="text-sm font-medium text-gray-900">
                            {{ event_data.event_type|title|default:"Other" }}
                        </span>
                    </div>
                    <div class="text-right">
                        <div class="text-sm font-medium text-gray-900">{{ event_data.count }}</div>
                        <div class="text-xs text-gray-500">${{ event_data.revenue|default:0|floatformat:2 }}</div>
                    </div>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2">
                    <div class="bg-blue-400 h-2 rounded-full"
                        style="width: {{ event_data.percentage|floatformat:1 }}%"></div>
                </div>
                {% endfor %}
            </div>
            {% else %}
            <p class="text-gray-500 text-sm">No data available for this period.</p>
            {% endif %}
        </div>
    </div>
</div>

<!-- Priority and Performance -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
    
    <!-- Bookings by Priority -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Bookings by Priority</h3>
        </div>
        <div class="px-6 py-4">
            {% if priority_breakdown %}
            <div class="space-y-3">
                {% for priority_data in priority_breakdown %}
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="w-4 h-4 rounded mr-3
                            {% if priority_data.priority == 'urgent' %}bg-red-400
                            {% elif priority_data.priority == 'high' %}bg-orange-400
                            {% elif priority_data.priority == 'normal' %}bg-blue-400
                            {% else %}bg-gray-400{% endif %}"></div>
                        <span class="text-sm font-medium text-gray-900">
                            {{ priority_data.priority|title|default:"Normal" }}
                        </span>
                    </div>
                    <span class="text-sm font-medium text-gray-900">{{ priority_data.count }}</span>
                </div>
                {% endfor %}
            </div>
            {% else %}
            <p class="text-gray-500 text-sm">No data available for this period.</p>
            {% endif %}
        </div>
    </div>
    
    <!-- Performance Metrics -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Performance Metrics</h3>
        </div>
        <div class="px-6 py-4">
            <div class="space-y-4">
                <div class="flex items-center justify-between">
                    <span class="text-sm text-gray-600">Confirmation Rate</span>
                    <span class="text-sm font-medium text-gray-900">
                        {{ stats.confirmation_rate|floatformat:1 }}%
                    </span>
                </div>
                <div class="flex items-center justify-between">
                    <span class="text-sm text-gray-600">Completion Rate</span>
                    <span class="text-sm font-medium text-gray-900">
                        {{ stats.completion_rate|floatformat:1 }}%
                    </span>
                </div>
                <div class="flex items-center justify-between">
                    <span class="text-sm text-gray-600">Cancellation Rate</span>
                    <span class="text-sm font-medium text-gray-900">
                        {{ stats.cancellation_rate|floatformat:1 }}%
                    </span>
                </div>
                <div class="flex items-center justify-between">
                    <span class="text-sm text-gray-600">Revenue per Day</span>
                    <span class="text-sm font-medium text-gray-900">
                        ${{ stats.revenue_per_day|floatformat:2 }}
                    </span>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Top Customers -->
<div class="bg-white shadow rounded-lg">
    <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900">Top Customers</h3>
    </div>
    <div class="px-6 py-4">
        {% if top_customers %}
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Customer
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Bookings
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Total Spent
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Avg per Booking
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    {% for customer in top_customers %}
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center">
                                <div>
                                    <div class="text-sm font-medium text-gray-900">
                                        <a href="{% url 'savory_events:management_customer_detail' customer.id %}" 
                                           class="text-blue-600 hover:text-blue-800">
                                            {{ customer.name }}
                                        </a>
                                    </div>
                                    <div class="text-sm text-gray-500">{{ customer.email }}</div>
                                </div>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {{ customer.booking_count }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            ${{ customer.total_spent|default:0|floatformat:2 }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            ${{ customer.avg_per_booking|default:0|floatformat:2 }}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <p class="text-gray-500 text-sm">No customer data available for this period.</p>
        {% endif %}
    </div>
</div>

<!-- Export Options -->
<div class="mt-6 bg-white shadow rounded-lg">
    <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900">Export Options</h3>
    </div>
    <div class="px-6 py-4">
        <div class="flex items-center space-x-4">
            <button onclick="exportToCSV()" 
                    class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                <i class="fas fa-file-csv mr-2"></i>Export to CSV
            </button>
            <button onclick="window.print()" 
                    class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                <i class="fas fa-print mr-2"></i>Print Report
            </button>
            <button onclick="emailReport()" 
                    class="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                <i class="fas fa-envelope mr-2"></i>Email Report
            </button>
        </div>
    </div>
</div>

<script>
function exportToCSV() {
    // Implement CSV export functionality
    alert('CSV export functionality would be implemented here');
}

function emailReport() {
    // Implement email report functionality
    alert('Email report functionality would be implemented here');
}
</script>

<style>
@media print {
    .no-print {
        display: none !important;
    }
}
</style>
{% endblock %}
