#!/usr/bin/env python
"""
<PERSON><PERSON><PERSON> to populate the database with sample menu items for development.
"""

import os
import sys
import django

# Add the project directory to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'BonAppetit.settings')
django.setup()

from savory_events.models import MenuItem

def create_sample_menu():
    """Create sample menu items for development."""
    
    # Clear existing menu items
    MenuItem.objects.all().delete()
    
    menu_items = [
        # Appetizers
        {
            'name': 'Mediterranean Bruschetta',
            'description': 'Toasted artisan bread topped with fresh tomatoes, basil, feta cheese, and olive tapenade',
            'category': 'appetizer',
            'price_per_person': 8.50,
            'is_available': True
        },
        {
            'name': 'Spinach & Artichoke Dip',
            'description': 'Creamy spinach and artichoke dip served with warm pita chips and fresh vegetables',
            'category': 'appetizer',
            'price_per_person': 7.25,
            'is_available': True
        },
        {
            'name': 'Bacon-Wrapped Scallops',
            'description': 'Pan-seared scallops wrapped in crispy bacon with a honey-balsamic glaze',
            'category': 'appetizer',
            'price_per_person': 12.75,
            'is_available': True
        },
        {
            'name': 'Caprese Skewers',
            'description': 'Fresh mozzarella, cherry tomatoes, and basil drizzled with balsamic reduction',
            'category': 'appetizer',
            'price_per_person': 6.50,
            'is_available': True
        },
        
        # Main Dishes
        {
            'name': 'Grilled Salmon Teriyaki',
            'description': 'Fresh Atlantic salmon glazed with house-made teriyaki sauce, served with jasmine rice and steamed vegetables',
            'category': 'main',
            'price_per_person': 24.95,
            'is_available': True
        },
        {
            'name': 'Herb-Crusted Beef Tenderloin',
            'description': 'Premium beef tenderloin with rosemary and thyme crust, roasted potatoes, and seasonal vegetables',
            'category': 'main',
            'price_per_person': 32.50,
            'is_available': True
        },
        {
            'name': 'Chicken Marsala',
            'description': 'Pan-seared chicken breast in rich marsala wine sauce with mushrooms and garlic mashed potatoes',
            'category': 'main',
            'price_per_person': 19.75,
            'is_available': True
        },
        {
            'name': 'Vegetarian Lasagna',
            'description': 'Layers of pasta with ricotta, spinach, roasted vegetables, and marinara sauce',
            'category': 'main',
            'price_per_person': 16.25,
            'is_available': True
        },
        {
            'name': 'BBQ Pulled Pork',
            'description': 'Slow-cooked pulled pork with tangy BBQ sauce, coleslaw, and cornbread',
            'category': 'main',
            'price_per_person': 18.50,
            'is_available': True
        },
        
        # Desserts
        {
            'name': 'Chocolate Lava Cake',
            'description': 'Warm chocolate cake with molten center, served with vanilla ice cream and berry coulis',
            'category': 'dessert',
            'price_per_person': 8.95,
            'is_available': True
        },
        {
            'name': 'Tiramisu',
            'description': 'Classic Italian dessert with ladyfingers, espresso, mascarpone, and cocoa dusting',
            'category': 'dessert',
            'price_per_person': 7.50,
            'is_available': True
        },
        {
            'name': 'New York Cheesecake',
            'description': 'Rich and creamy cheesecake with graham cracker crust and fresh berry topping',
            'category': 'dessert',
            'price_per_person': 6.75,
            'is_available': True
        },
        {
            'name': 'Seasonal Fruit Tart',
            'description': 'Buttery pastry shell filled with vanilla custard and topped with fresh seasonal fruits',
            'category': 'dessert',
            'price_per_person': 6.25,
            'is_available': True
        },
        
        # Beverages
        {
            'name': 'Fresh Lemonade',
            'description': 'House-made lemonade with fresh lemons and mint',
            'category': 'drink',
            'price_per_person': 3.50,
            'is_available': True
        },
        {
            'name': 'Iced Tea Selection',
            'description': 'Choice of sweet tea, unsweetened tea, or flavored options',
            'category': 'drink',
            'price_per_person': 2.75,
            'is_available': True
        },
        {
            'name': 'Coffee Service',
            'description': 'Freshly brewed coffee with cream, sugar, and flavoring options',
            'category': 'drink',
            'price_per_person': 4.25,
            'is_available': True
        },
        {
            'name': 'Sparkling Water',
            'description': 'Premium sparkling water with lemon and lime garnish',
            'category': 'drink',
            'price_per_person': 2.95,
            'is_available': True
        },
    ]
    
    created_items = []
    for item_data in menu_items:
        item = MenuItem.objects.create(**item_data)
        created_items.append(item)
        print(f"Created: {item.name} (${item.price_per_person})")
    
    print(f"\nSuccessfully created {len(created_items)} menu items!")
    
    # Display summary by category
    categories = MenuItem.CATEGORY_CHOICES
    for category_code, category_name in categories:
        count = MenuItem.objects.filter(category=category_code).count()
        print(f"{category_name}: {count} items")

if __name__ == '__main__':
    create_sample_menu()
