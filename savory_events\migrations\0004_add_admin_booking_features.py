# Generated by Django 4.2.17 on 2025-08-03 15:10

from decimal import Decimal
from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('savory_events', '0003_alter_customer_phone'),
    ]

    operations = [
        migrations.AddField(
            model_name='booking',
            name='assigned_coordinator',
            field=models.ForeignKey(blank=True, help_text='Staff member assigned as coordinator', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='coordinated_bookings', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='booking',
            name='discount_amount',
            field=models.DecimalField(decimal_places=2, default=Decimal('0.00'), help_text='Discount applied to booking', max_digits=10),
        ),
        migrations.AddField(
            model_name='booking',
            name='internal_notes',
            field=models.TextField(blank=True, help_text='Internal notes visible only to staff'),
        ),
        migrations.AddField(
            model_name='booking',
            name='priority',
            field=models.CharField(choices=[('low', 'Low Priority'), ('normal', 'Normal Priority'), ('high', 'High Priority'), ('urgent', 'Urgent')], default='normal', max_length=10),
        ),
        migrations.AlterField(
            model_name='customer',
            name='phone',
            field=models.CharField(default='************', help_text="Customer's phone number", max_length=20),
            preserve_default=False,
        ),
        migrations.CreateModel(
            name='BookingStatusHistory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('old_status', models.CharField(choices=[('pending', 'Pending'), ('confirmed', 'Confirmed'), ('partial_paid', 'Partially Paid'), ('fully_paid', 'Fully Paid'), ('completed', 'Completed'), ('cancelled', 'Cancelled')], max_length=20)),
                ('new_status', models.CharField(choices=[('pending', 'Pending'), ('confirmed', 'Confirmed'), ('partial_paid', 'Partially Paid'), ('fully_paid', 'Fully Paid'), ('completed', 'Completed'), ('cancelled', 'Cancelled')], max_length=20)),
                ('reason', models.TextField(blank=True, help_text='Reason for status change')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('booking', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='status_history', to='savory_events.booking')),
                ('changed_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='status_changes', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name_plural': 'Booking status histories',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='BookingReminder',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('reminder_type', models.CharField(choices=[('follow_up', 'Follow Up'), ('confirmation', 'Confirmation Call'), ('payment', 'Payment Reminder'), ('preparation', 'Event Preparation'), ('delivery', 'Delivery/Setup'), ('custom', 'Custom Task')], max_length=20)),
                ('title', models.CharField(help_text='Reminder title', max_length=200)),
                ('description', models.TextField(blank=True, help_text='Detailed description')),
                ('due_date', models.DateTimeField(help_text='When this reminder is due')),
                ('is_completed', models.BooleanField(default=False)),
                ('completed_at', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('assigned_to', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='assigned_reminders', to=settings.AUTH_USER_MODEL)),
                ('booking', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='reminders', to='savory_events.booking')),
                ('completed_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='completed_reminders', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['due_date'],
            },
        ),
        migrations.CreateModel(
            name='BookingNote',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('note', models.TextField(help_text='Internal note or comment')),
                ('priority', models.CharField(choices=[('low', 'Low'), ('normal', 'Normal'), ('high', 'High'), ('urgent', 'Urgent')], default='normal', max_length=10)),
                ('is_internal', models.BooleanField(default=True, help_text='Internal note (not visible to customer)')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('booking', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='notes', to='savory_events.booking')),
                ('staff_user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='booking_notes', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='BookingStaffAssignment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('role', models.CharField(choices=[('coordinator', 'Event Coordinator'), ('chef', 'Head Chef'), ('server', 'Server'), ('setup', 'Setup Crew'), ('manager', 'Event Manager')], max_length=20)),
                ('notes', models.TextField(blank=True, help_text='Special instructions for this assignment')),
                ('assigned_at', models.DateTimeField(auto_now_add=True)),
                ('assigned_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='staff_assignments_made', to=settings.AUTH_USER_MODEL)),
                ('booking', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='staff_assignments', to='savory_events.booking')),
                ('staff_user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='booking_assignments', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'unique_together': {('booking', 'staff_user', 'role')},
            },
        ),
    ]
