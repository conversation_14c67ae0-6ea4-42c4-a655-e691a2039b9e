#!/usr/bin/env python
"""
Simple test script to verify the new management dashboard is working correctly.
"""

import os
import sys
import django

# Setup Django first
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'BonAppetit.settings')
django.setup()

# Now import Django modules
from django.test import TestCase, Client
from django.contrib.auth.models import User
from django.urls import reverse
from savory_events.models import Customer, Booking, MenuItem, Payment
from decimal import Decimal
from datetime import datetime, timedelta


def test_management_dashboard():
    """Test that the management dashboard loads correctly."""
    print("Testing Management Dashboard...")
    
    # Create test client
    client = Client()
    
    # Create staff user
    staff_user = User.objects.create_user(
        username='admin',
        email='<EMAIL>',
        password='testpass',
        is_staff=True
    )
    
    # Login as staff
    client.login(username='admin', password='testpass')
    
    # Test management dashboard
    print("Testing management dashboard...")
    response = client.get('/management/')
    print(f"   Status: {response.status_code}")
    assert response.status_code == 200, f"Expected 200, got {response.status_code}"
    assert 'Management Dashboard' in response.content.decode()
    print("   [PASS] Management dashboard loads successfully")
    
    # Test management bookings
    print("Testing management bookings...")
    response = client.get('/management/bookings/')
    print(f"   Status: {response.status_code}")
    assert response.status_code == 200, f"Expected 200, got {response.status_code}"
    print("   [PASS] Management bookings loads successfully")
    
    # Test management customers
    print("Testing management customers...")
    response = client.get('/management/customers/')
    print(f"   Status: {response.status_code}")
    assert response.status_code == 200, f"Expected 200, got {response.status_code}"
    print("   [PASS] Management customers loads successfully")
    
    # Test management analytics
    print("Testing management analytics...")
    response = client.get('/management/analytics/')
    print(f"   Status: {response.status_code}")
    assert response.status_code == 200, f"Expected 200, got {response.status_code}"
    print("   [PASS] Management analytics loads successfully")
    
    # Test management payments
    print("Testing management payments...")
    response = client.get('/management/payments/')
    print(f"   Status: {response.status_code}")
    assert response.status_code == 200, f"Expected 200, got {response.status_code}"
    print("   [PASS] Management payments loads successfully")
    
    print("\nAll management dashboard tests passed!")


def test_with_sample_data():
    """Test dashboard with sample data."""
    print("\nTesting with sample data...")
    
    # Create test client and staff user
    client = Client()
    staff_user = User.objects.create_user(
        username='admin2',
        email='<EMAIL>',
        password='testpass',
        is_staff=True
    )
    client.login(username='admin2', password='testpass')
    
    # Create sample data
    customer = Customer.objects.create(
        name='John Doe',
        email='<EMAIL>',
        phone='************'
    )
    
    menu_item = MenuItem.objects.create(
        name='Deluxe Catering Package',
        description='Full service catering',
        category='main',
        price_per_person=Decimal('25.00'),
        is_available=True
    )
    
    booking = Booking.objects.create(
        customer=customer,
        event_date=datetime.now() + timedelta(days=30),
        event_type='wedding',
        guest_count=50,
        venue_address='123 Test Street, Test City',
        status='confirmed',
        total_amount=Decimal('1250.00')
    )
    
    payment = Payment.objects.create(
        booking=booking,
        amount=Decimal('625.00'),
        payment_type='deposit',
        notes='Deposit payment via credit card'
    )
    
    # Test dashboard with data
    print("Testing dashboard with sample data...")
    response = client.get('/management/')
    content = response.content.decode()
    
    # Check if data appears correctly
    assert '1' in content or 'John Doe' in content, "Sample booking should appear in dashboard"
    assert '$1,250.00' in content or '$1250.00' in content, "Booking amount should appear"
    print("   [PASS] Dashboard displays sample data correctly")
    
    # Test booking detail page
    print("Testing booking detail page...")
    response = client.get(f'/management/bookings/{booking.id}/')
    print(f"   Status: {response.status_code}")
    assert response.status_code == 200, f"Expected 200, got {response.status_code}"
    content = response.content.decode()
    assert 'John Doe' in content, "Customer name should appear in booking detail"
    assert 'Wedding' in content, "Event type should appear in booking detail"
    print("   [PASS] Booking detail page works correctly")
    
    # Test customer detail page
    print("Testing customer detail page...")
    response = client.get(f'/management/customers/{customer.id}/')
    print(f"   Status: {response.status_code}")
    assert response.status_code == 200, f"Expected 200, got {response.status_code}"
    content = response.content.decode()
    assert 'John Doe' in content, "Customer name should appear"
    assert '<EMAIL>' in content, "Customer email should appear"
    print("   [PASS] Customer detail page works correctly")
    
    print("\nAll tests with sample data passed!")


def test_template_responsiveness():
    """Test that templates are responsive and include required CSS/JS."""
    print("\nTesting template structure...")
    
    client = Client()
    staff_user = User.objects.create_user(
        username='admin3',
        email='<EMAIL>',
        password='testpass',
        is_staff=True
    )
    client.login(username='admin3', password='testpass')
    
    response = client.get('/management/')
    content = response.content.decode()
    
    # Check for required CSS/JS libraries
    assert 'tailwindcss' in content, "TailwindCSS should be included"
    assert 'alpinejs' in content, "Alpine.js should be included"
    assert 'htmx' in content, "HTMX should be included"
    assert 'chart.js' in content, "Chart.js should be included"
    assert 'font-awesome' in content, "Font Awesome should be included"
    print("   [PASS] Required CSS/JS libraries are included")
    
    # Check for responsive design elements
    assert 'md:' in content, "Responsive design classes should be present"
    assert 'sidebar' in content.lower(), "Sidebar should be present"
    assert 'mobile' in content.lower() or 'sidebarOpen' in content, "Mobile navigation should be present"
    print("   [PASS] Responsive design elements are present")
    
    # Check for color scheme
    assert 'deep-olive' in content or '#556B2F' in content, "Custom color scheme should be applied"
    print("   [PASS] Custom color scheme is applied")
    
    print("\nTemplate structure tests passed!")


if __name__ == '__main__':
    print("Starting Management Dashboard Tests...")
    print("=" * 50)
    
    try:
        # Clean up any existing test data
        User.objects.filter(username__startswith='admin').delete()
        Customer.objects.all().delete()
        Booking.objects.all().delete()
        MenuItem.objects.all().delete()
        Payment.objects.all().delete()
        
        # Run tests
        test_management_dashboard()
        test_with_sample_data()
        test_template_responsiveness()
        
        print("\n" + "=" * 50)
        print("ALL TESTS PASSED! Management Dashboard is working correctly.")
        print("The new management dashboard has been successfully implemented!")
        
    except Exception as e:
        print(f"\n[FAIL] Test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
    
    finally:
        # Clean up test data
        User.objects.filter(username__startswith='admin').delete()
        Customer.objects.all().delete()
        Booking.objects.all().delete()
        MenuItem.objects.all().delete()
        Payment.objects.all().delete()
