{% extends 'management/base.html' %}

{% block title %}Customer Details{% endblock %}
{% block page_title %}Customer Details{% endblock %}

{% block content %}
<!-- Customer Header -->
<div class="bg-white shadow rounded-lg mb-6">
    <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex items-center justify-between">
            <div class="flex items-center">
                <div class="h-16 w-16 rounded-full bg-deep-olive flex items-center justify-center mr-4">
                    <span class="text-white font-bold text-2xl">
                        {{ customer.name|first|upper }}
                    </span>
                </div>
                <div>
                    <h2 class="text-2xl font-semibold text-gray-900">{{ customer.name }}</h2>
                    <p class="text-sm text-gray-500">Customer since {{ customer.created_at|date:"M d, Y" }}</p>
                </div>
            </div>
            <div class="flex space-x-3">
                <a href="mailto:{{ customer.email }}" 
                   class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors duration-200">
                    <i class="fas fa-envelope mr-2"></i>
                    Send Email
                </a>
                <a href="tel:{{ customer.phone }}" 
                   class="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 transition-colors duration-200">
                    <i class="fas fa-phone mr-2"></i>
                    Call
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Customer Statistics -->
<div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
    <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="h-8 w-8 bg-blue-500 rounded-md flex items-center justify-center">
                        <i class="fas fa-calendar-alt text-white"></i>
                    </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Total Bookings</dt>
                        <dd class="text-lg font-medium text-gray-900">{{ stats.total_bookings }}</dd>
                    </dl>
                </div>
            </div>
        </div>
    </div>

    <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="h-8 w-8 bg-green-500 rounded-md flex items-center justify-center">
                        <i class="fas fa-dollar-sign text-white"></i>
                    </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Total Spent</dt>
                        <dd class="text-lg font-medium text-gray-900">${{ stats.total_spent|floatformat:2 }}</dd>
                    </dl>
                </div>
            </div>
        </div>
    </div>

    <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="h-8 w-8 bg-purple-500 rounded-md flex items-center justify-center">
                        <i class="fas fa-chart-line text-white"></i>
                    </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Avg. Booking</dt>
                        <dd class="text-lg font-medium text-gray-900">${{ stats.avg_booking_value|floatformat:2 }}</dd>
                    </dl>
                </div>
            </div>
        </div>
    </div>

    <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="h-8 w-8 bg-orange-500 rounded-md flex items-center justify-center">
                        <i class="fas fa-clock text-white"></i>
                    </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Last Booking</dt>
                        <dd class="text-lg font-medium text-gray-900">
                            {% if stats.last_booking_date %}
                                {{ stats.last_booking_date|date:"M d" }}
                            {% else %}
                                Never
                            {% endif %}
                        </dd>
                    </dl>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Main Content Grid -->
<div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
    
    <!-- Booking History -->
    <div class="lg:col-span-2">
        <div class="bg-white shadow rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-medium text-gray-900">Booking History</h3>
                    <a href="{% url 'savory_events:booking_wizard' %}" 
                       class="bg-deep-olive text-white px-4 py-2 rounded-md hover:bg-burnt-orange transition-colors duration-200">
                        <i class="fas fa-plus mr-2"></i>
                        New Booking
                    </a>
                </div>
            </div>
            <div class="overflow-hidden">
                {% if bookings %}
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Event</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Guests</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            {% for booking in bookings %}
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900">{{ booking.get_event_type_display }}</div>
                                    <div class="text-sm text-gray-500">{{ booking.venue_address|truncatechars:30 }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">{{ booking.event_date|date:"M d, Y" }}</div>
                                    <div class="text-sm text-gray-500">{{ booking.event_date|time:"h:i A" }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    {{ booking.guest_count }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full 
                                        {% if booking.status == 'pending' %}bg-yellow-100 text-yellow-800
                                        {% elif booking.status == 'confirmed' %}bg-blue-100 text-blue-800
                                        {% elif booking.status == 'partial_paid' %}bg-orange-100 text-orange-800
                                        {% elif booking.status == 'fully_paid' %}bg-green-100 text-green-800
                                        {% elif booking.status == 'completed' %}bg-gray-100 text-gray-800
                                        {% else %}bg-red-100 text-red-800{% endif %}">
                                        {{ booking.get_status_display }}
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900">${{ booking.total_amount|floatformat:2 }}</div>
                                    <div class="text-sm text-gray-500">
                                        Paid: ${{ booking.get_total_paid|floatformat:2 }}
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                    <a href="{% url 'savory_events:management_booking_detail' booking.id %}" 
                                       class="text-deep-olive hover:text-burnt-orange">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-12">
                    <div class="text-gray-500">
                        <i class="fas fa-calendar-alt text-4xl mb-4 text-gray-300"></i>
                        <p class="text-lg font-medium">No bookings yet</p>
                        <p class="text-sm">This customer hasn't made any bookings.</p>
                        <a href="{% url 'savory_events:booking_wizard' %}" 
                           class="inline-flex items-center mt-4 px-4 py-2 bg-deep-olive text-white rounded-md hover:bg-burnt-orange transition-colors duration-200">
                            <i class="fas fa-plus mr-2"></i>
                            Create First Booking
                        </a>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Customer Information -->
    <div class="space-y-6">
        
        <!-- Contact Information -->
        <div class="bg-white shadow rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Contact Information</h3>
            </div>
            <div class="px-6 py-4">
                <dl class="space-y-4">
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Full Name</dt>
                        <dd class="mt-1 text-sm text-gray-900">{{ customer.name }}</dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Email Address</dt>
                        <dd class="mt-1 text-sm text-gray-900">
                            <a href="mailto:{{ customer.email }}" class="text-deep-olive hover:text-burnt-orange">
                                {{ customer.email }}
                            </a>
                        </dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Phone Number</dt>
                        <dd class="mt-1 text-sm text-gray-900">
                            {% if customer.phone %}
                                <a href="tel:{{ customer.phone }}" class="text-deep-olive hover:text-burnt-orange">
                                    {{ customer.phone }}
                                </a>
                            {% else %}
                                <span class="text-gray-400">Not provided</span>
                            {% endif %}
                        </dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Customer Since</dt>
                        <dd class="mt-1 text-sm text-gray-900">{{ customer.created_at|date:"F d, Y" }}</dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Last Updated</dt>
                        <dd class="mt-1 text-sm text-gray-900">{{ customer.updated_at|date:"F d, Y" }}</dd>
                    </div>
                </dl>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="bg-white shadow rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Quick Actions</h3>
            </div>
            <div class="px-6 py-4 space-y-3">
                <a href="{% url 'savory_events:booking_wizard' %}" 
                   class="w-full flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-deep-olive hover:bg-burnt-orange transition-colors duration-200">
                    <i class="fas fa-plus mr-2"></i>
                    Create New Booking
                </a>
                <a href="mailto:{{ customer.email }}?subject=Follow up from BonAppetit Catering" 
                   class="w-full flex items-center justify-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    <i class="fas fa-envelope mr-2"></i>
                    Send Follow-up Email
                </a>
                <a href="tel:{{ customer.phone }}" 
                   class="w-full flex items-center justify-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    <i class="fas fa-phone mr-2"></i>
                    Call Customer
                </a>
                <a href="{% url 'savory_events:management_customers' %}" 
                   class="w-full flex items-center justify-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    <i class="fas fa-arrow-left mr-2"></i>
                    Back to Customers
                </a>
            </div>
        </div>

        {% if bookings %}
        <!-- Event Type Preferences -->
        <div class="bg-white shadow rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Event Preferences</h3>
            </div>
            <div class="px-6 py-4">
                <div class="space-y-3">
                    {% regroup bookings by event_type as event_groups %}
                    {% for event_group in event_groups %}
                    <div class="flex justify-between items-center">
                        <div class="text-sm text-gray-900">{{ event_group.grouper|title }}</div>
                        <div class="text-sm text-gray-500">{{ event_group.list|length }} booking{{ event_group.list|length|pluralize }}</div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
        {% endif %}
    </div>
</div>

{% csrf_token %}
{% endblock %}
