{% extends 'base.html' %}

{% block title %}Chat Support - BonAppetit Catering{% endblock %}

{% block content %}
<div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <div class="bg-white rounded-lg shadow-lg overflow-hidden" x-data="chatInterface()">
        <!-- Chat Header -->
        <div class="bg-deep-olive text-white px-6 py-4 flex items-center justify-between">
            <div class="flex items-center">
                <div class="w-10 h-10 bg-burnt-orange rounded-full flex items-center justify-center mr-3">
                    <i class="fas fa-comments text-white"></i>
                </div>
                <div>
                    <h2 class="text-lg font-semibold">BonAppetit Support</h2>
                    <p class="text-sm text-sage-green">Ask us about our catering services</p>
                </div>
            </div>
            <div class="flex items-center space-x-2">
                <button @click="clearConversation()" 
                        class="text-sage-green hover:text-white transition-colors p-2 rounded"
                        title="Clear conversation">
                    <i class="fas fa-trash-alt"></i>
                </button>
                <button @click="toggleChat()" 
                        class="text-sage-green hover:text-white transition-colors p-2 rounded lg:hidden"
                        title="Close chat">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>

        <!-- Chat Messages Container -->
        <div class="h-96 overflow-y-auto bg-gray-50 p-4" 
             id="chat-messages" 
             x-ref="messagesContainer">
            
            <!-- Welcome Message -->
            <div class="mb-4" x-show="messages.length === 0">
                <div class="flex items-start space-x-3">
                    <div class="w-8 h-8 bg-burnt-orange rounded-full flex items-center justify-center flex-shrink-0">
                        <i class="fas fa-robot text-white text-sm"></i>
                    </div>
                    <div class="bg-white rounded-lg p-3 shadow-sm max-w-xs lg:max-w-md">
                        <p class="text-charcoal text-sm">
                            Hello! I'm here to help you with your catering needs. Ask me about our menu, pricing, booking process, or anything else related to BonAppetit's services.
                        </p>
                    </div>
                </div>
            </div>

            <!-- Messages -->
            <template x-for="message in messages" :key="message.id">
                <div class="mb-4" :class="message.type === 'user' ? 'flex justify-end' : 'flex justify-start'">
                    <div class="flex items-start space-x-3 max-w-xs lg:max-w-md" 
                         :class="message.type === 'user' ? 'flex-row-reverse space-x-reverse' : ''">
                        
                        <!-- Avatar -->
                        <div class="w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0"
                             :class="message.type === 'user' ? 'bg-sage-green' : 'bg-burnt-orange'">
                            <i :class="message.type === 'user' ? 'fas fa-user text-white text-sm' : 'fas fa-robot text-white text-sm'"></i>
                        </div>
                        
                        <!-- Message Bubble -->
                        <div class="rounded-lg p-3 shadow-sm"
                             :class="message.type === 'user' ? 'bg-sage-green text-white' : 'bg-white text-charcoal'">
                            <p class="text-sm" x-text="message.content"></p>
                            <p class="text-xs mt-1 opacity-70" x-text="formatTime(message.timestamp)"></p>
                        </div>
                    </div>
                </div>
            </template>

            <!-- Typing Indicator -->
            <div class="mb-4 flex justify-start" x-show="isTyping">
                <div class="flex items-start space-x-3 max-w-xs lg:max-w-md">
                    <div class="w-8 h-8 bg-burnt-orange rounded-full flex items-center justify-center flex-shrink-0">
                        <i class="fas fa-robot text-white text-sm"></i>
                    </div>
                    <div class="bg-white rounded-lg p-3 shadow-sm">
                        <div class="flex space-x-1">
                            <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                            <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style="animation-delay: 0.1s"></div>
                            <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style="animation-delay: 0.2s"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="px-4 py-2 bg-gray-100 border-t border-gray-200">
            <div class="flex flex-wrap gap-2">
                {% for action in quick_actions %}
                <button @click="sendQuickAction('{{ action.action }}')"
                        class="px-3 py-1 bg-sage-green text-white text-xs rounded-full hover:bg-deep-olive transition-colors"
                        :disabled="isLoading">
                    {{ action.text }}
                </button>
                {% endfor %}
            </div>
        </div>

        <!-- Message Input -->
        <div class="p-4 border-t border-gray-200 bg-white">
            <form @submit.prevent="sendMessage()" class="flex space-x-2">
                <input type="text" 
                       x-model="currentMessage"
                       placeholder="Type your message..."
                       class="flex-1 px-3 py-2 border border-sage-green rounded-md focus:outline-none focus:ring-2 focus:ring-burnt-orange transition-colors"
                       :disabled="isLoading"
                       maxlength="500">
                <button type="submit" 
                        :disabled="isLoading || !currentMessage.trim()"
                        class="px-4 py-2 bg-burnt-orange text-white rounded-md hover:bg-opacity-80 focus:outline-none focus:ring-2 focus:ring-burnt-orange transition-colors disabled:opacity-50 disabled:cursor-not-allowed">
                    <i class="fas fa-paper-plane" x-show="!isLoading"></i>
                    <i class="fas fa-spinner fa-spin" x-show="isLoading"></i>
                </button>
            </form>
        </div>
    </div>
</div>

<script>
function chatInterface() {
    return {
        messages: [],
        currentMessage: '',
        isLoading: false,
        isTyping: false,
        sessionId: '{{ session_id }}',

        init() {
            this.loadMessages();
            // Auto-scroll to bottom when new messages arrive
            this.$watch('messages', () => {
                this.$nextTick(() => {
                    this.scrollToBottom();
                });
            });
        },

        async loadMessages() {
            try {
                const response = await fetch('/chat/messages/', {
                    method: 'GET',
                    headers: {
                        'X-CSRFToken': this.getCsrfToken(),
                    },
                });
                
                if (response.ok) {
                    const data = await response.json();
                    this.messages = data.messages;
                }
            } catch (error) {
                console.error('Error loading messages:', error);
            }
        },

        async sendMessage() {
            if (!this.currentMessage.trim() || this.isLoading) return;

            const message = this.currentMessage.trim();
            this.currentMessage = '';
            this.isLoading = true;
            this.isTyping = true;

            try {
                const response = await fetch('/chat/send/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': this.getCsrfToken(),
                    },
                    body: JSON.stringify({
                        message: message
                    }),
                });

                if (response.ok) {
                    const data = await response.json();
                    this.messages.push(data.user_message);
                    
                    // Simulate typing delay
                    setTimeout(() => {
                        this.isTyping = false;
                        this.messages.push(data.bot_response);
                    }, 1000);
                } else {
                    throw new Error('Failed to send message');
                }
            } catch (error) {
                console.error('Error sending message:', error);
                this.isTyping = false;
                // Add error message
                this.messages.push({
                    id: Date.now(),
                    content: 'Sorry, I encountered an error. Please try again.',
                    type: 'bot',
                    timestamp: new Date().toISOString()
                });
            } finally {
                this.isLoading = false;
            }
        },

        async sendQuickAction(action) {
            if (this.isLoading) return;

            this.isLoading = true;
            this.isTyping = true;

            try {
                const response = await fetch('/chat/send/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': this.getCsrfToken(),
                    },
                    body: JSON.stringify({
                        action: action
                    }),
                });

                if (response.ok) {
                    const data = await response.json();
                    this.messages.push(data.user_message);
                    
                    setTimeout(() => {
                        this.isTyping = false;
                        this.messages.push(data.bot_response);
                    }, 800);
                } else {
                    throw new Error('Failed to send quick action');
                }
            } catch (error) {
                console.error('Error sending quick action:', error);
                this.isTyping = false;
            } finally {
                this.isLoading = false;
            }
        },

        async clearConversation() {
            if (!confirm('Are you sure you want to clear this conversation?')) return;

            try {
                const response = await fetch('/chat/clear/', {
                    method: 'POST',
                    headers: {
                        'X-CSRFToken': this.getCsrfToken(),
                    },
                });

                if (response.ok) {
                    this.messages = [];
                    const data = await response.json();
                    this.sessionId = data.new_session_id;
                }
            } catch (error) {
                console.error('Error clearing conversation:', error);
            }
        },

        scrollToBottom() {
            const container = this.$refs.messagesContainer;
            container.scrollTop = container.scrollHeight;
        },

        formatTime(timestamp) {
            const date = new Date(timestamp);
            return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
        },

        getCsrfToken() {
            return document.querySelector('[name=csrfmiddlewaretoken]').value;
        },

        toggleChat() {
            // For mobile - could be used to minimize chat widget
            console.log('Toggle chat');
        }
    }
}
</script>
{% endblock %}
