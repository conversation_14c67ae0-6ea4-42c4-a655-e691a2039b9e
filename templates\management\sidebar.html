<div class="flex-1 flex flex-col pt-5 pb-4 overflow-y-auto">
    <div class="flex items-center flex-shrink-0 px-4">
        <h2 class="text-lg font-semibold text-cream">BonAppetit</h2>
        <span class="ml-2 text-sm text-gray-400">Management</span>
    </div>
    
    <nav class="mt-8 flex-1 px-2 space-y-1">
        
        <!-- Dashboard -->
        <a href="{% url 'savory_events:management_dashboard' %}" 
           class="group flex items-center px-2 py-2 text-sm font-medium rounded-md {% if request.resolver_match.url_name == 'management_dashboard' %}bg-deep-olive text-cream{% else %}text-gray-300 hover:bg-gray-700 hover:text-white{% endif %}">
            <i class="fas fa-tachometer-alt mr-3 text-gray-400 group-hover:text-gray-300"></i>
            Dashboard
        </a>
        
        <!-- Bookings -->
        <a href="{% url 'savory_events:management_bookings' %}"
           class="group flex items-center px-2 py-2 text-sm font-medium rounded-md {% if 'booking' in request.resolver_match.url_name and 'calendar' not in request.resolver_match.url_name and 'conflicts' not in request.resolver_match.url_name and 'reports' not in request.resolver_match.url_name %}bg-deep-olive text-cream{% else %}text-gray-300 hover:bg-gray-700 hover:text-white{% endif %}">
            <i class="fas fa-calendar-alt mr-3 text-gray-400 group-hover:text-gray-300"></i>
            Bookings
        </a>

        <!-- Enhanced Booking Management -->
        <div class="mt-4">
            <p class="px-2 text-xs font-semibold text-gray-400 uppercase tracking-wider">
                Advanced Tools
            </p>

            <!-- Calendar View -->
            <a href="{% url 'savory_events:management_booking_calendar' %}"
               class="group flex items-center px-2 py-2 text-sm font-medium rounded-md {% if 'calendar' in request.resolver_match.url_name %}bg-deep-olive text-cream{% else %}text-gray-300 hover:bg-gray-700 hover:text-white{% endif %}">
                <i class="fas fa-calendar-week mr-3 text-gray-400 group-hover:text-gray-300"></i>
                Calendar View
            </a>

            <!-- Conflicts -->
            <a href="{% url 'savory_events:management_booking_conflicts' %}"
               class="group flex items-center px-2 py-2 text-sm font-medium rounded-md {% if 'conflicts' in request.resolver_match.url_name %}bg-deep-olive text-cream{% else %}text-gray-300 hover:bg-gray-700 hover:text-white{% endif %}">
                <i class="fas fa-exclamation-triangle mr-3 text-gray-400 group-hover:text-gray-300"></i>
                Conflicts
            </a>

            <!-- Reports -->
            <a href="{% url 'savory_events:management_booking_reports' %}"
               class="group flex items-center px-2 py-2 text-sm font-medium rounded-md {% if 'reports' in request.resolver_match.url_name %}bg-deep-olive text-cream{% else %}text-gray-300 hover:bg-gray-700 hover:text-white{% endif %}">
                <i class="fas fa-chart-bar mr-3 text-gray-400 group-hover:text-gray-300"></i>
                Reports
            </a>
        </div>
        
        <!-- Customers -->
        <a href="{% url 'savory_events:management_customers' %}" 
           class="group flex items-center px-2 py-2 text-sm font-medium rounded-md {% if 'customer' in request.resolver_match.url_name %}bg-deep-olive text-cream{% else %}text-gray-300 hover:bg-gray-700 hover:text-white{% endif %}">
            <i class="fas fa-users mr-3 text-gray-400 group-hover:text-gray-300"></i>
            Customers
        </a>
        
        <!-- Payments -->
        <a href="{% url 'savory_events:management_payments' %}" 
           class="group flex items-center px-2 py-2 text-sm font-medium rounded-md {% if 'payment' in request.resolver_match.url_name %}bg-deep-olive text-cream{% else %}text-gray-300 hover:bg-gray-700 hover:text-white{% endif %}">
            <i class="fas fa-credit-card mr-3 text-gray-400 group-hover:text-gray-300"></i>
            Payments
        </a>
        
        <!-- Analytics -->
        <a href="{% url 'savory_events:management_analytics' %}" 
           class="group flex items-center px-2 py-2 text-sm font-medium rounded-md {% if request.resolver_match.url_name == 'management_analytics' %}bg-deep-olive text-cream{% else %}text-gray-300 hover:bg-gray-700 hover:text-white{% endif %}">
            <i class="fas fa-chart-line mr-3 text-gray-400 group-hover:text-gray-300"></i>
            Analytics
        </a>
        
        <!-- Menu Management -->
        <a href="{% url 'savory_events:management_menu' %}" 
           class="group flex items-center px-2 py-2 text-sm font-medium rounded-md {% if 'menu' in request.resolver_match.url_name %}bg-deep-olive text-cream{% else %}text-gray-300 hover:bg-gray-700 hover:text-white{% endif %}">
            <i class="fas fa-utensils mr-3 text-gray-400 group-hover:text-gray-300"></i>
            Menu Management
        </a>
        
        <!-- Divider -->
        <div class="border-t border-gray-700 my-4"></div>
        
        <!-- Quick Actions -->
        <div class="px-2 py-2">
            <h3 class="text-xs uppercase tracking-wider text-gray-400 font-semibold">Quick Actions</h3>
        </div>
        
        <a href="{% url 'savory_events:booking_wizard' %}" 
           class="group flex items-center px-2 py-2 text-sm font-medium rounded-md text-gray-300 hover:bg-gray-700 hover:text-white">
            <i class="fas fa-plus mr-3 text-gray-400 group-hover:text-gray-300"></i>
            New Booking
        </a>
        
        <a href="{% url 'savory_events:contact' %}" 
           class="group flex items-center px-2 py-2 text-sm font-medium rounded-md text-gray-300 hover:bg-gray-700 hover:text-white">
            <i class="fas fa-envelope mr-3 text-gray-400 group-hover:text-gray-300"></i>
            Contact Form
        </a>
        
        <!-- Settings -->
        <div class="border-t border-gray-700 my-4"></div>
        
        <a href="/admin/" 
           class="group flex items-center px-2 py-2 text-sm font-medium rounded-md text-gray-300 hover:bg-gray-700 hover:text-white">
            <i class="fas fa-cog mr-3 text-gray-400 group-hover:text-gray-300"></i>
            Django Admin
        </a>
        
    </nav>
    
    <!-- User info at bottom -->
    <div class="flex-shrink-0 border-t border-gray-700 p-4">
        <div class="flex items-center">
            <div class="h-8 w-8 rounded-full bg-deep-olive flex items-center justify-center">
                <i class="fas fa-user text-white text-sm"></i>
            </div>
            <div class="ml-3">
                <p class="text-sm font-medium text-cream">{{ user.get_full_name|default:user.username }}</p>
                <p class="text-xs text-gray-400">Administrator</p>
            </div>
        </div>
    </div>
</div>
