{% extends 'base.html' %}

{% block title %}Book Your Event - Step by Step - BonAppetit Catering{% endblock %}

{% block content %}
<div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Progress Indicator -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div class="flex items-center">
                <div x-data="{ currentStep: 1 }" class="flex items-center space-x-4">
                    <!-- Step 1: Event Details -->
                    <div class="flex items-center">
                        <div :class="currentStep >= 1 ? 'bg-burnt-orange text-white' : 'bg-gray-300 text-gray-600'" 
                             class="w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium">
                            1
                        </div>
                        <span class="ml-2 text-sm font-medium text-charcoal">Event Details</span>
                    </div>
                    
                    <div class="w-8 h-0.5 bg-gray-300"></div>
                    
                    <!-- Step 2: Menu Selection -->
                    <div class="flex items-center">
                        <div :class="currentStep >= 2 ? 'bg-burnt-orange text-white' : 'bg-gray-300 text-gray-600'" 
                             class="w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium">
                            2
                        </div>
                        <span class="ml-2 text-sm font-medium text-charcoal">Menu Selection</span>
                    </div>
                    
                    <div class="w-8 h-0.5 bg-gray-300"></div>
                    
                    <!-- Step 3: Services -->
                    <div class="flex items-center">
                        <div :class="currentStep >= 3 ? 'bg-burnt-orange text-white' : 'bg-gray-300 text-gray-600'" 
                             class="w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium">
                            3
                        </div>
                        <span class="ml-2 text-sm font-medium text-charcoal">Services</span>
                    </div>
                    
                    <div class="w-8 h-0.5 bg-gray-300"></div>
                    
                    <!-- Step 4: Confirmation -->
                    <div class="flex items-center">
                        <div :class="currentStep >= 4 ? 'bg-burnt-orange text-white' : 'bg-gray-300 text-gray-600'" 
                             class="w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium">
                            4
                        </div>
                        <span class="ml-2 text-sm font-medium text-charcoal">Confirmation</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Booking Wizard Form -->
    <div class="bg-white rounded-lg shadow-lg" x-data="bookingWizard()">
        <!-- Step 1: Event Details -->
        <div x-show="currentStep === 1" class="p-6 md:p-8">
            <h2 class="text-2xl font-bold text-charcoal mb-6">Event Details</h2>
            
            <form @submit.prevent="nextStep()" class="space-y-6">
                <!-- Customer Information -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="customer_name" class="block text-sm font-medium text-charcoal mb-2">Full Name *</label>
                        <input type="text" 
                               id="customer_name" 
                               x-model="formData.customer_name"
                               @input="validateField('customer_name')"
                               class="w-full px-3 py-2 border border-sage-green rounded-md focus:outline-none focus:ring-2 focus:ring-burnt-orange"
                               :class="{'border-red-500': errors.customer_name}"
                               required>
                        <div x-show="errors.customer_name" class="text-red-500 text-sm mt-1" x-text="errors.customer_name"></div>
                    </div>
                    
                    <div>
                        <label for="customer_email" class="block text-sm font-medium text-charcoal mb-2">Email Address *</label>
                        <input type="email" 
                               id="customer_email" 
                               x-model="formData.customer_email"
                               @input="validateField('customer_email')"
                               class="w-full px-3 py-2 border border-sage-green rounded-md focus:outline-none focus:ring-2 focus:ring-burnt-orange"
                               :class="{'border-red-500': errors.customer_email}"
                               required>
                        <div x-show="errors.customer_email" class="text-red-500 text-sm mt-1" x-text="errors.customer_email"></div>
                    </div>
                    
                    <div>
                        <label for="customer_phone" class="block text-sm font-medium text-charcoal mb-2">Phone Number *</label>
                        <input type="tel" 
                               id="customer_phone" 
                               x-model="formData.customer_phone"
                               @input="validateField('customer_phone')"
                               class="w-full px-3 py-2 border border-sage-green rounded-md focus:outline-none focus:ring-2 focus:ring-burnt-orange"
                               :class="{'border-red-500': errors.customer_phone}"
                               required>
                        <div x-show="errors.customer_phone" class="text-red-500 text-sm mt-1" x-text="errors.customer_phone"></div>
                    </div>
                    
                    <div>
                        <label for="event_type" class="block text-sm font-medium text-charcoal mb-2">Event Type *</label>
                        <select id="event_type" 
                                x-model="formData.event_type"
                                class="w-full px-3 py-2 border border-sage-green rounded-md focus:outline-none focus:ring-2 focus:ring-burnt-orange"
                                required>
                            <option value="">Select Event Type</option>
                            <option value="wedding">Wedding</option>
                            <option value="corporate">Corporate Event</option>
                            <option value="birthday">Birthday Party</option>
                            <option value="anniversary">Anniversary</option>
                            <option value="other">Other</option>
                        </select>
                    </div>
                </div>
                
                <!-- Event Information -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div>
                        <label for="event_date" class="block text-sm font-medium text-charcoal mb-2">Event Date *</label>
                        <input type="date" 
                               id="event_date" 
                               x-model="formData.event_date"
                               @input="validateField('event_date')"
                               class="w-full px-3 py-2 border border-sage-green rounded-md focus:outline-none focus:ring-2 focus:ring-burnt-orange"
                               :class="{'border-red-500': errors.event_date}"
                               required>
                        <div x-show="errors.event_date" class="text-red-500 text-sm mt-1" x-text="errors.event_date"></div>
                    </div>
                    
                    <div>
                        <label for="event_time" class="block text-sm font-medium text-charcoal mb-2">Event Time *</label>
                        <input type="time" 
                               id="event_time" 
                               x-model="formData.event_time"
                               class="w-full px-3 py-2 border border-sage-green rounded-md focus:outline-none focus:ring-2 focus:ring-burnt-orange"
                               required>
                    </div>
                    
                    <div>
                        <label for="guest_count" class="block text-sm font-medium text-charcoal mb-2">Number of Guests *</label>
                        <input type="number" 
                               id="guest_count" 
                               x-model="formData.guest_count"
                               @input="validateField('guest_count')"
                               min="1" 
                               class="w-full px-3 py-2 border border-sage-green rounded-md focus:outline-none focus:ring-2 focus:ring-burnt-orange"
                               :class="{'border-red-500': errors.guest_count}"
                               required>
                        <div x-show="errors.guest_count" class="text-red-500 text-sm mt-1" x-text="errors.guest_count"></div>
                    </div>
                </div>
                
                <div>
                    <label for="venue_address" class="block text-sm font-medium text-charcoal mb-2">Venue Address *</label>
                    <textarea id="venue_address" 
                              x-model="formData.venue_address"
                              rows="3" 
                              class="w-full px-3 py-2 border border-sage-green rounded-md focus:outline-none focus:ring-2 focus:ring-burnt-orange"
                              placeholder="Enter the complete venue address..."
                              required></textarea>
                </div>
                
                <div class="flex justify-end">
                    <button type="submit" 
                            :disabled="!isStep1Valid()"
                            class="bg-burnt-orange hover:bg-opacity-80 disabled:bg-gray-400 disabled:cursor-not-allowed text-white font-bold py-3 px-8 rounded-lg transition-colors">
                        Next: Select Menu
                    </button>
                </div>
            </form>
        </div>
        
        <!-- Step 2: Menu Selection -->
        <div x-show="currentStep === 2" class="p-6 md:p-8">
            <h2 class="text-2xl font-bold text-charcoal mb-6">Menu Selection</h2>
            
            <!-- Menu Categories -->
            <div class="mb-6">
                <div class="flex flex-wrap gap-2">
                    <button @click="setActiveCategory('all')" 
                            :class="activeCategory === 'all' ? 'bg-burnt-orange text-white' : 'bg-gray-200 text-charcoal hover:bg-sage-green hover:text-white'"
                            class="px-4 py-2 rounded-lg font-medium transition-colors">
                        All Items
                    </button>
                    <button @click="setActiveCategory('appetizer')" 
                            :class="activeCategory === 'appetizer' ? 'bg-burnt-orange text-white' : 'bg-gray-200 text-charcoal hover:bg-sage-green hover:text-white'"
                            class="px-4 py-2 rounded-lg font-medium transition-colors">
                        Appetizers
                    </button>
                    <button @click="setActiveCategory('main')" 
                            :class="activeCategory === 'main' ? 'bg-burnt-orange text-white' : 'bg-gray-200 text-charcoal hover:bg-sage-green hover:text-white'"
                            class="px-4 py-2 rounded-lg font-medium transition-colors">
                        Main Dishes
                    </button>
                    <button @click="setActiveCategory('dessert')" 
                            :class="activeCategory === 'dessert' ? 'bg-burnt-orange text-white' : 'bg-gray-200 text-charcoal hover:bg-sage-green hover:text-white'"
                            class="px-4 py-2 rounded-lg font-medium transition-colors">
                        Desserts
                    </button>
                    <button @click="setActiveCategory('drink')" 
                            :class="activeCategory === 'drink' ? 'bg-burnt-orange text-white' : 'bg-gray-200 text-charcoal hover:bg-sage-green hover:text-white'"
                            class="px-4 py-2 rounded-lg font-medium transition-colors">
                        Beverages
                    </button>
                </div>
            </div>
            
            <!-- Menu Items Grid -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-6">
                <template x-for="item in filteredMenuItems" :key="item.id">
                    <div class="bg-gray-50 rounded-lg p-4 border border-gray-200 hover:border-sage-green transition-colors">
                        <h4 class="font-semibold text-charcoal mb-2" x-text="item.name"></h4>
                        <p class="text-sm text-gray-600 mb-3" x-text="item.description"></p>
                        <div class="flex justify-between items-center">
                            <span class="text-burnt-orange font-bold" x-text="'$' + item.price + ' per person'"></span>
                            <label class="flex items-center cursor-pointer">
                                <input type="checkbox" 
                                       :value="item.id"
                                       x-model="formData.selectedMenuItems"
                                       @change="updateMenuTotal()"
                                       class="h-4 w-4 text-burnt-orange focus:ring-burnt-orange border-sage-green rounded mr-2">
                                <span class="text-sm font-medium text-charcoal">Select</span>
                            </label>
                        </div>
                    </div>
                </template>
            </div>
            
            <!-- Menu Summary -->
            <div x-show="formData.selectedMenuItems.length > 0" class="bg-cream rounded-lg p-4 mb-6">
                <h3 class="font-semibold text-charcoal mb-2">Selected Items</h3>
                <div class="space-y-1 text-sm">
                    <template x-for="item in getSelectedMenuItems()" :key="item.id">
                        <div class="flex justify-between">
                            <span x-text="item.name"></span>
                            <span x-text="'$' + item.price + ' × ' + formData.guest_count + ' = $' + (item.price * formData.guest_count)"></span>
                        </div>
                    </template>
                </div>
                <div class="border-t border-sage-green pt-2 mt-2 font-semibold">
                    <div class="flex justify-between">
                        <span>Menu Total:</span>
                        <span x-text="'$' + menuTotal"></span>
                    </div>
                </div>
            </div>
            
            <div class="flex justify-between">
                <button @click="currentStep = 1" 
                        class="bg-gray-300 hover:bg-gray-400 text-charcoal font-bold py-3 px-8 rounded-lg transition-colors">
                    Back: Event Details
                </button>
                <button @click="nextStep()" 
                        :disabled="formData.selectedMenuItems.length === 0"
                        class="bg-burnt-orange hover:bg-opacity-80 disabled:bg-gray-400 disabled:cursor-not-allowed text-white font-bold py-3 px-8 rounded-lg transition-colors">
                    Next: Add Services
                </button>
            </div>
        </div>
        
        <!-- Step 3: Services -->
        <div x-show="currentStep === 3" class="p-6 md:p-8">
            <h2 class="text-2xl font-bold text-charcoal mb-6">Additional Services</h2>
            
            <!-- Sound System Services -->
            <div class="mb-6">
                <h3 class="text-lg font-semibold text-charcoal mb-4">Sound System Services (Optional)</h3>
                <div class="space-y-3">
                    <label class="flex items-start cursor-pointer">
                        <input type="radio" 
                               name="sound_system" 
                               value=""
                               x-model="formData.sound_system"
                               @change="updateServiceTotal()"
                               class="mt-1 h-4 w-4 text-burnt-orange focus:ring-burnt-orange border-sage-green">
                        <div class="ml-3">
                            <div class="text-sm font-medium text-charcoal">No Sound System</div>
                            <div class="text-sm text-gray-600">We'll provide our own audio equipment</div>
                        </div>
                    </label>
                    <label class="flex items-start cursor-pointer">
                        <input type="radio" 
                               name="sound_system" 
                               value="microphones"
                               x-model="formData.sound_system"
                               @change="updateServiceTotal()"
                               class="mt-1 h-4 w-4 text-burnt-orange focus:ring-burnt-orange border-sage-green">
                        <div class="ml-3">
                            <div class="text-sm font-medium text-charcoal">Microphones Only - $150</div>
                            <div class="text-sm text-gray-600">Basic microphone setup for speeches</div>
                        </div>
                    </label>
                    <label class="flex items-start cursor-pointer">
                        <input type="radio" 
                               name="sound_system" 
                               value="dj"
                               x-model="formData.sound_system"
                               @change="updateServiceTotal()"
                               class="mt-1 h-4 w-4 text-burnt-orange focus:ring-burnt-orange border-sage-green">
                        <div class="ml-3">
                            <div class="text-sm font-medium text-charcoal">DJ Package - $400</div>
                            <div class="text-sm text-gray-600">Professional DJ with music and microphones</div>
                        </div>
                    </label>
                    <label class="flex items-start cursor-pointer">
                        <input type="radio" 
                               name="sound_system" 
                               value="full_audio"
                               x-model="formData.sound_system"
                               @change="updateServiceTotal()"
                               class="mt-1 h-4 w-4 text-burnt-orange focus:ring-burnt-orange border-sage-green">
                        <div class="ml-3">
                            <div class="text-sm font-medium text-charcoal">Full Audio Set - $600</div>
                            <div class="text-sm text-gray-600">Complete audio system with DJ, lighting, and equipment</div>
                        </div>
                    </label>
                </div>
            </div>
            
            <!-- Special Requests -->
            <div class="mb-6">
                <label for="special_requests" class="block text-sm font-medium text-charcoal mb-2">Special Requests</label>
                <textarea id="special_requests" 
                          x-model="formData.special_requests"
                          rows="4" 
                          placeholder="Any dietary restrictions, special accommodations, or additional requests..."
                          class="w-full px-3 py-2 border border-sage-green rounded-md focus:outline-none focus:ring-2 focus:ring-burnt-orange"></textarea>
            </div>
            
            <div class="flex justify-between">
                <button @click="currentStep = 2" 
                        class="bg-gray-300 hover:bg-gray-400 text-charcoal font-bold py-3 px-8 rounded-lg transition-colors">
                    Back: Menu Selection
                </button>
                <button @click="nextStep()" 
                        class="bg-burnt-orange hover:bg-opacity-80 text-white font-bold py-3 px-8 rounded-lg transition-colors">
                    Next: Review & Confirm
                </button>
            </div>
        </div>
        
        <!-- Step 4: Confirmation -->
        <div x-show="currentStep === 4" class="p-6 md:p-8">
            <h2 class="text-2xl font-bold text-charcoal mb-6">Review & Confirm Your Booking</h2>
            
            <!-- Booking Summary -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <!-- Event Details -->
                <div class="bg-gray-50 rounded-lg p-6">
                    <h3 class="text-lg font-semibold text-charcoal mb-4">Event Details</h3>
                    <div class="space-y-2 text-sm">
                        <div><strong>Customer:</strong> <span x-text="formData.customer_name"></span></div>
                        <div><strong>Email:</strong> <span x-text="formData.customer_email"></span></div>
                        <div><strong>Phone:</strong> <span x-text="formData.customer_phone"></span></div>
                        <div><strong>Event Type:</strong> <span x-text="getEventTypeDisplay()"></span></div>
                        <div><strong>Date & Time:</strong> <span x-text="formData.event_date + ' at ' + formData.event_time"></span></div>
                        <div><strong>Guests:</strong> <span x-text="formData.guest_count"></span></div>
                        <div><strong>Venue:</strong> <span x-text="formData.venue_address"></span></div>
                    </div>
                </div>
                
                <!-- Cost Breakdown -->
                <div class="bg-cream rounded-lg p-6">
                    <h3 class="text-lg font-semibold text-charcoal mb-4">Cost Breakdown</h3>
                    <div class="space-y-2 text-sm">
                        <div class="flex justify-between">
                            <span>Menu Items:</span>
                            <span x-text="'$' + menuTotal"></span>
                        </div>
                        <div class="flex justify-between" x-show="serviceTotal > 0">
                            <span>Sound System:</span>
                            <span x-text="'$' + serviceTotal"></span>
                        </div>
                        <div class="border-t border-sage-green pt-2 mt-2">
                            <div class="flex justify-between font-semibold text-lg">
                                <span>Total Amount:</span>
                                <span x-text="'$' + grandTotal"></span>
                            </div>
                            <div class="text-xs text-gray-600 mt-1">
                                50% deposit required: <span x-text="'$' + (grandTotal * 0.5)"></span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Submit Form -->
            <div class="mt-8">
                <form hx-post="{% url 'savory_events:htmx_booking_submit' %}"
                      hx-target="#booking-response"
                      hx-swap="innerHTML"
                      hx-indicator="#loading-indicator"
                      up-submit
                      up-target="#booking-response"
                      @submit="prepareFormSubmission()">
                    {% csrf_token %}
                    
                    <!-- Hidden form fields -->
                    <input type="hidden" name="customer_name" :value="formData.customer_name">
                    <input type="hidden" name="customer_email" :value="formData.customer_email">
                    <input type="hidden" name="customer_phone" :value="formData.customer_phone">
                    <input type="hidden" name="event_type" :value="formData.event_type">
                    <input type="hidden" name="event_date" :value="formData.event_date">
                    <input type="hidden" name="event_time" :value="formData.event_time">
                    <input type="hidden" name="guest_count" :value="formData.guest_count">
                    <input type="hidden" name="venue_address" :value="formData.venue_address">
                    <input type="hidden" name="sound_system" :value="formData.sound_system">
                    <input type="hidden" name="special_requests" :value="formData.special_requests">
                    <template x-for="itemId in formData.selectedMenuItems" :key="itemId">
                        <input type="hidden" name="selected_menu_items" :value="itemId">
                    </template>
                    
                    <div class="flex justify-between">
                        <button type="button" @click="currentStep = 3" 
                                class="bg-gray-300 hover:bg-gray-400 text-charcoal font-bold py-3 px-8 rounded-lg transition-colors">
                            Back: Services
                        </button>
                        <button type="submit" 
                                class="bg-burnt-orange hover:bg-opacity-80 text-white font-bold py-3 px-8 rounded-lg transition-colors">
                            <span id="loading-indicator" class="htmx-indicator">
                                <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white inline" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                                Processing...
                            </span>
                            <span class="htmx-indicator:not(.htmx-request)">Confirm Booking</span>
                        </button>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- HTMX Response Container -->
        <div id="booking-response" class="mt-6"></div>
    </div>
</div>

<script>
function bookingWizard() {
    return {
        currentStep: 1,
        activeCategory: 'all',
        formData: {
            customer_name: '',
            customer_email: '',
            customer_phone: '',
            event_type: '',
            event_date: '',
            event_time: '',
            guest_count: 1,
            venue_address: '',
            sound_system: '',
            special_requests: '',
            selectedMenuItems: []
        },
        errors: {},
        menuItems: {{ menu_items|safe }},
        menuTotal: 0,
        serviceTotal: 0,
        
        get grandTotal() {
            return this.menuTotal + this.serviceTotal;
        },
        
        get filteredMenuItems() {
            if (this.activeCategory === 'all') {
                return this.menuItems;
            }
            return this.menuItems.filter(item => item.category === this.activeCategory);
        },
        
        setActiveCategory(category) {
            this.activeCategory = category;
        },
        
        validateField(field) {
            switch(field) {
                case 'customer_name':
                    this.errors.customer_name = this.formData.customer_name.length < 2 ? 'Name must be at least 2 characters' : '';
                    break;
                case 'customer_email':
                    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                    this.errors.customer_email = !emailRegex.test(this.formData.customer_email) ? 'Please enter a valid email address' : '';
                    break;
                case 'customer_phone':
                    const phoneRegex = /^[\d\s\-\+\(\)]+$/;
                    this.errors.customer_phone = !phoneRegex.test(this.formData.customer_phone) ? 'Please enter a valid phone number' : '';
                    break;
                case 'event_date':
                    const selectedDate = new Date(this.formData.event_date);
                    const today = new Date();
                    this.errors.event_date = selectedDate <= today ? 'Event date must be in the future' : '';
                    break;
                case 'guest_count':
                    this.errors.guest_count = this.formData.guest_count < 1 ? 'Must have at least 1 guest' : '';
                    break;
            }
        },
        
        isStep1Valid() {
            return this.formData.customer_name && 
                   this.formData.customer_email && 
                   this.formData.customer_phone && 
                   this.formData.event_date && 
                   this.formData.event_time && 
                   this.formData.event_type && 
                   this.formData.guest_count &&
                   this.formData.venue_address &&
                   !Object.values(this.errors).some(error => error);
        },
        
        nextStep() {
            if (this.currentStep < 4) {
                this.currentStep++;
            }
        },
        
        getSelectedMenuItems() {
            return this.menuItems.filter(item => this.formData.selectedMenuItems.includes(item.id.toString()));
        },
        
        updateMenuTotal() {
            const selectedItems = this.getSelectedMenuItems();
            this.menuTotal = selectedItems.reduce((total, item) => {
                return total + (item.price * this.formData.guest_count);
            }, 0);
        },
        
        updateServiceTotal() {
            const costs = {
                '': 0,
                'microphones': 150,
                'dj': 400,
                'full_audio': 600
            };
            this.serviceTotal = costs[this.formData.sound_system] || 0;
        },
        
        getEventTypeDisplay() {
            const types = {
                'wedding': 'Wedding',
                'corporate': 'Corporate Event',
                'birthday': 'Birthday Party',
                'anniversary': 'Anniversary',
                'other': 'Other'
            };
            return types[this.formData.event_type] || this.formData.event_type;
        },
        
        prepareFormSubmission() {
            // Additional preparation before form submission
            console.log('Submitting booking:', this.formData);
        }
    }
}
</script>
{% endblock %}
