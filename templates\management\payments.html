{% extends 'management/base.html' %}

{% block title %}Payment Management{% endblock %}
{% block page_title %}Payment Management{% endblock %}

{% block content %}
<!-- Payment Summary Cards -->
<div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
    <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="h-8 w-8 bg-green-500 rounded-md flex items-center justify-center">
                        <i class="fas fa-dollar-sign text-white"></i>
                    </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Total Payments</dt>
                        <dd class="text-lg font-medium text-gray-900">${{ payment_summary.total_payments|floatformat:2 }}</dd>
                    </dl>
                </div>
            </div>
        </div>
        <div class="bg-gray-50 px-5 py-3">
            <div class="text-sm">
                <span class="text-green-600 font-medium">{{ payment_summary.payment_count }}</span>
                <span class="text-gray-500">transactions</span>
            </div>
        </div>
    </div>

    <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="h-8 w-8 bg-blue-500 rounded-md flex items-center justify-center">
                        <i class="fas fa-chart-line text-white"></i>
                    </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Average Payment</dt>
                        <dd class="text-lg font-medium text-gray-900">${{ payment_summary.avg_payment|floatformat:2 }}</dd>
                    </dl>
                </div>
            </div>
        </div>
    </div>

    <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="h-8 w-8 bg-orange-500 rounded-md flex items-center justify-center">
                        <i class="fas fa-exclamation-triangle text-white"></i>
                    </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Outstanding Bookings</dt>
                        <dd class="text-lg font-medium text-gray-900">{{ outstanding_bookings|length }}</dd>
                    </dl>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Filters -->
<div class="bg-white shadow rounded-lg mb-6">
    <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900">Filters & Search</h3>
    </div>
    <div class="px-6 py-4">
        <form method="get" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
            
            <!-- Search -->
            <div class="lg:col-span-2">
                <label for="search" class="block text-sm font-medium text-gray-700 mb-1">Search</label>
                <input type="text" 
                       name="search" 
                       id="search"
                       value="{{ filters.search }}"
                       placeholder="Customer name, email, notes..."
                       class="block w-full border-gray-300 rounded-md shadow-sm focus:ring-deep-olive focus:border-deep-olive">
            </div>
            
            <!-- Payment Type Filter -->
            <div>
                <label for="payment_type" class="block text-sm font-medium text-gray-700 mb-1">Payment Type</label>
                <select name="payment_type" 
                        id="payment_type"
                        class="block w-full border-gray-300 rounded-md shadow-sm focus:ring-deep-olive focus:border-deep-olive">
                    <option value="">All Types</option>
                    {% for value, label in payment_type_choices %}
                    <option value="{{ value }}" {% if filters.payment_type == value %}selected{% endif %}>{{ label }}</option>
                    {% endfor %}
                </select>
            </div>
            
            <!-- Date From -->
            <div>
                <label for="date_from" class="block text-sm font-medium text-gray-700 mb-1">From Date</label>
                <input type="date" 
                       name="date_from" 
                       id="date_from"
                       value="{{ filters.date_from }}"
                       class="block w-full border-gray-300 rounded-md shadow-sm focus:ring-deep-olive focus:border-deep-olive">
            </div>
            
            <!-- Filter Actions -->
            <div class="flex items-end space-x-2">
                <button type="submit" 
                        class="flex-1 bg-deep-olive text-white px-4 py-2 rounded-md hover:bg-burnt-orange transition-colors duration-200">
                    <i class="fas fa-search mr-2"></i>
                    Filter
                </button>
                <a href="{% url 'savory_events:management_payments' %}" 
                   class="px-3 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50">
                    <i class="fas fa-times"></i>
                </a>
            </div>
        </form>
    </div>
</div>

<!-- Outstanding Balances -->
{% if outstanding_bookings %}
<div class="bg-white shadow rounded-lg mb-6">
    <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900">Outstanding Balances</h3>
    </div>
    <div class="overflow-hidden">
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Event</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total Amount</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Balance Due</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Event Date</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    {% for booking in outstanding_bookings %}
                    <tr class="hover:bg-gray-50">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm font-medium text-gray-900">{{ booking.customer.name }}</div>
                            <div class="text-sm text-gray-500">{{ booking.customer.email }}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-gray-900">{{ booking.get_event_type_display }}</div>
                            <div class="text-sm text-gray-500">{{ booking.guest_count }} guests</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                            ${{ booking.total_amount|floatformat:2 }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-red-600">
                            ${{ booking.balance|floatformat:2 }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {{ booking.event_date|date:"M d, Y" }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                            <a href="{% url 'savory_events:management_booking_detail' booking.id %}" 
                               class="text-deep-olive hover:text-burnt-orange mr-3">
                                <i class="fas fa-eye"></i>
                            </a>
                            <button onclick="addPayment({{ booking.id }})" 
                                    class="text-green-600 hover:text-green-900">
                                <i class="fas fa-credit-card"></i>
                            </button>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endif %}

<!-- Payment History -->
<div class="bg-white shadow rounded-lg">
    <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex items-center justify-between">
            <h3 class="text-lg font-medium text-gray-900">
                Payment History ({{ payments.paginator.count }} total)
            </h3>
        </div>
    </div>
    
    <div class="overflow-hidden">
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Booking</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Notes</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    {% for payment in payments %}
                    <tr class="hover:bg-gray-50">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-gray-900">{{ payment.payment_date|date:"M d, Y" }}</div>
                            <div class="text-sm text-gray-500">{{ payment.payment_date|time:"h:i A" }}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm font-medium text-gray-900">{{ payment.booking.customer.name }}</div>
                            <div class="text-sm text-gray-500">{{ payment.booking.customer.email }}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-gray-900">{{ payment.booking.get_event_type_display }}</div>
                            <div class="text-sm text-gray-500">{{ payment.booking.event_date|date:"M d, Y" }}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full 
                                {% if payment.payment_type == 'deposit' %}bg-blue-100 text-blue-800
                                {% elif payment.payment_type == 'balance' %}bg-green-100 text-green-800
                                {% else %}bg-gray-100 text-gray-800{% endif %}">
                                {{ payment.get_payment_type_display }}
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                            ${{ payment.amount|floatformat:2 }}
                        </td>
                        <td class="px-6 py-4">
                            <div class="text-sm text-gray-900 max-w-xs truncate">
                                {{ payment.notes|default:"-" }}
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                            <a href="{% url 'savory_events:management_booking_detail' payment.booking.id %}" 
                               class="text-deep-olive hover:text-burnt-orange">
                                <i class="fas fa-eye"></i>
                            </a>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="7" class="px-6 py-8 text-center">
                            <div class="text-gray-500">
                                <i class="fas fa-credit-card text-4xl mb-4 text-gray-300"></i>
                                <p class="text-lg font-medium">No payments found</p>
                                <p class="text-sm">Try adjusting your filters or process some payments.</p>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>

    <!-- Pagination -->
    {% if payments.has_other_pages %}
    <div class="bg-white px-4 py-3 border-t border-gray-200 sm:px-6">
        <div class="flex-1 flex justify-between sm:hidden">
            {% if payments.has_previous %}
                <a href="?page={{ payments.previous_page_number }}&{{ request.GET.urlencode }}" 
                   class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    Previous
                </a>
            {% endif %}
            
            {% if payments.has_next %}
                <a href="?page={{ payments.next_page_number }}&{{ request.GET.urlencode }}" 
                   class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    Next
                </a>
            {% endif %}
        </div>
        
        <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
            <div>
                <p class="text-sm text-gray-700">
                    Showing
                    <span class="font-medium">{{ payments.start_index }}</span>
                    to
                    <span class="font-medium">{{ payments.end_index }}</span>
                    of
                    <span class="font-medium">{{ payments.paginator.count }}</span>
                    results
                </p>
            </div>
            
            <div>
                <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                    {% if payments.has_previous %}
                        <a href="?page={{ payments.previous_page_number }}&{{ request.GET.urlencode }}" 
                           class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                            <span class="sr-only">Previous</span>
                            <i class="fas fa-chevron-left"></i>
                        </a>
                    {% endif %}
                    
                    {% for num in payments.paginator.page_range %}
                        {% if payments.number == num %}
                            <span class="bg-deep-olive border-deep-olive text-white relative inline-flex items-center px-4 py-2 border text-sm font-medium">
                                {{ num }}
                            </span>
                        {% else %}
                            <a href="?page={{ num }}&{{ request.GET.urlencode }}" 
                               class="bg-white border-gray-300 text-gray-500 hover:bg-gray-50 relative inline-flex items-center px-4 py-2 border text-sm font-medium">
                                {{ num }}
                            </a>
                        {% endif %}
                    {% endfor %}
                    
                    {% if payments.has_next %}
                        <a href="?page={{ payments.next_page_number }}&{{ request.GET.urlencode }}" 
                           class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                            <span class="sr-only">Next</span>
                            <i class="fas fa-chevron-right"></i>
                        </a>
                    {% endif %}
                </nav>
            </div>
        </div>
    </div>
    {% endif %}
</div>

{% csrf_token %}
{% endblock %}

{% block extra_scripts %}
<script>
function addPayment(bookingId) {
    // Simple implementation - could be enhanced with proper modal
    const amount = prompt("Enter payment amount:");
    const type = prompt("Enter payment type (deposit/balance/full):");
    const notes = prompt("Enter payment notes (optional):");
    
    if (amount && type) {
        fetch(`/management/payments/add/${bookingId}/`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
            },
            body: `amount=${amount}&payment_type=${type}&notes=${notes || ''}`
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(data.message);
                location.reload();
            } else {
                alert(data.error);
            }
        })
        .catch(error => {
            alert('Error adding payment: ' + error);
        });
    }
}
</script>
{% endblock %}
