from django.test import TestCase
from django.utils import timezone
from datetime import timedelta
from decimal import Decimal

from savory_events.forms import BookingForm, PaymentForm, MenuSelectionForm


class BookingFormTest(TestCase):
    """Test cases for BookingForm."""
    
    def setUp(self):
        self.valid_form_data = {
            'customer_name': '<PERSON>',
            'customer_email': '<EMAIL>',
            'customer_phone': '(*************',
            'event_date': (timezone.now() + timedelta(days=30)).date(),
            'event_time': '18:00',
            'event_type': 'wedding',
            'guest_count': 50,
            'venue_address': '123 Wedding Venue Lane, City, State 12345',
            'special_requests': 'No nuts please'
        }
    
    def test_booking_form_valid_data(self):
        """Test booking form with valid data."""
        form = BookingForm(data=self.valid_form_data)
        self.assertTrue(form.is_valid())
    
    def test_booking_form_missing_required_fields(self):
        """Test booking form with missing required fields."""
        form_data = self.valid_form_data.copy()
        del form_data['customer_name']
        
        form = BookingForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn('customer_name', form.errors)
    
    def test_customer_name_validation(self):
        """Test customer name validation."""
        # Test name too short
        form_data = self.valid_form_data.copy()
        form_data['customer_name'] = 'A'
        
        form = BookingForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn('customer_name', form.errors)
    
    def test_customer_phone_validation(self):
        """Test customer phone validation."""
        # Test invalid phone format
        form_data = self.valid_form_data.copy()
        form_data['customer_phone'] = 'abc123'
        
        form = BookingForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn('customer_phone', form.errors)
        
        # Test phone too short
        form_data['customer_phone'] = '123'
        form = BookingForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn('customer_phone', form.errors)
    
    def test_event_date_validation(self):
        """Test event date validation."""
        # Test past date
        form_data = self.valid_form_data.copy()
        form_data['event_date'] = (timezone.now() - timedelta(days=1)).date()
        
        form = BookingForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn('event_date', form.errors)
        
        # Test date too far in future (more than 2 years)
        form_data['event_date'] = (timezone.now() + timedelta(days=800)).date()
        form = BookingForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn('event_date', form.errors)
    
    def test_guest_count_validation(self):
        """Test guest count validation."""
        # Test zero guests
        form_data = self.valid_form_data.copy()
        form_data['guest_count'] = 0
        
        form = BookingForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn('guest_count', form.errors)
        
        # Test too many guests
        form_data['guest_count'] = 1001
        form = BookingForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn('guest_count', form.errors)
    
    def test_venue_address_validation(self):
        """Test venue address validation."""
        # Test address too short
        form_data = self.valid_form_data.copy()
        form_data['venue_address'] = 'Short'
        
        form = BookingForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn('venue_address', form.errors)
    
    def test_special_requests_optional(self):
        """Test special requests field is optional."""
        form_data = self.valid_form_data.copy()
        del form_data['special_requests']
        
        form = BookingForm(data=form_data)
        self.assertTrue(form.is_valid())


class PaymentFormTest(TestCase):
    """Test cases for PaymentForm."""
    
    def setUp(self):
        self.valid_form_data = {
            'payment_amount': '500.00',
            'payment_type': 'deposit',
            'payment_notes': 'Initial deposit payment'
        }
    
    def test_payment_form_valid_data(self):
        """Test payment form with valid data."""
        form = PaymentForm(data=self.valid_form_data)
        self.assertTrue(form.is_valid())
    
    def test_payment_form_missing_required_fields(self):
        """Test payment form with missing required fields."""
        form_data = self.valid_form_data.copy()
        del form_data['payment_amount']
        
        form = PaymentForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn('payment_amount', form.errors)
    
    def test_payment_amount_validation(self):
        """Test payment amount validation."""
        # Test zero amount
        form_data = self.valid_form_data.copy()
        form_data['payment_amount'] = '0.00'
        
        form = PaymentForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn('payment_amount', form.errors)
        
        # Test negative amount
        form_data['payment_amount'] = '-100.00'
        form = PaymentForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn('payment_amount', form.errors)
        
        # Test amount too large
        form_data['payment_amount'] = '50001.00'
        form = PaymentForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn('payment_amount', form.errors)
    
    def test_payment_notes_optional(self):
        """Test payment notes field is optional."""
        form_data = self.valid_form_data.copy()
        del form_data['payment_notes']
        
        form = PaymentForm(data=form_data)
        self.assertTrue(form.is_valid())
    
    def test_payment_type_choices(self):
        """Test payment type choices are valid."""
        form_data = self.valid_form_data.copy()
        
        # Test valid payment types
        valid_types = ['deposit', 'balance', 'full']
        for payment_type in valid_types:
            form_data['payment_type'] = payment_type
            form = PaymentForm(data=form_data)
            self.assertTrue(form.is_valid(), f"Payment type '{payment_type}' should be valid")
        
        # Test invalid payment type
        form_data['payment_type'] = 'invalid_type'
        form = PaymentForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn('payment_type', form.errors)


class MenuSelectionFormTest(TestCase):
    """Test cases for MenuSelectionForm."""
    
    def test_menu_selection_form_empty(self):
        """Test menu selection form with no items."""
        form = MenuSelectionForm(data={'selected_items': ''})
        self.assertTrue(form.is_valid())
        self.assertEqual(form.cleaned_data['selected_items'], [])
    
    def test_menu_selection_form_valid_items(self):
        """Test menu selection form with valid item IDs."""
        form = MenuSelectionForm(data={'selected_items': '1,2,3'})
        self.assertTrue(form.is_valid())
        self.assertEqual(form.cleaned_data['selected_items'], [1, 2, 3])
    
    def test_menu_selection_form_invalid_items(self):
        """Test menu selection form with invalid item IDs."""
        form = MenuSelectionForm(data={'selected_items': 'abc,def'})
        self.assertFalse(form.is_valid())
        self.assertIn('selected_items', form.errors)
    
    def test_menu_selection_form_mixed_items(self):
        """Test menu selection form with mixed valid/invalid items."""
        form = MenuSelectionForm(data={'selected_items': '1,abc,3'})
        self.assertFalse(form.is_valid())
        self.assertIn('selected_items', form.errors)
    
    def test_menu_selection_form_whitespace_handling(self):
        """Test menu selection form handles whitespace properly."""
        form = MenuSelectionForm(data={'selected_items': ' 1 , 2 , 3 '})
        self.assertTrue(form.is_valid())
        self.assertEqual(form.cleaned_data['selected_items'], [1, 2, 3])


class FormWidgetTest(TestCase):
    """Test cases for form widgets and CSS classes."""
    
    def test_booking_form_widgets(self):
        """Test booking form widgets have correct CSS classes."""
        form = BookingForm()
        
        # Test that form fields have expected CSS classes
        name_widget = form.fields['customer_name'].widget
        self.assertIn('w-full', name_widget.attrs.get('class', ''))
        self.assertIn('border-sage-green', name_widget.attrs.get('class', ''))
        
        email_widget = form.fields['customer_email'].widget
        self.assertIn('focus:ring-burnt-orange', email_widget.attrs.get('class', ''))
    
    def test_payment_form_widgets(self):
        """Test payment form widgets have correct CSS classes."""
        form = PaymentForm()
        
        amount_widget = form.fields['payment_amount'].widget
        self.assertIn('border-gray-300', amount_widget.attrs.get('class', ''))
        
        # Test step attribute for decimal input
        self.assertEqual(amount_widget.attrs.get('step'), '0.01')
    
    def test_form_field_attributes(self):
        """Test form fields have correct HTML attributes."""
        form = BookingForm()
        
        # Test required fields
        self.assertTrue(form.fields['customer_name'].required)
        self.assertTrue(form.fields['customer_email'].required)
        self.assertFalse(form.fields['special_requests'].required)
        
        # Test input types
        self.assertEqual(form.fields['event_date'].widget.input_type, 'date')
        self.assertEqual(form.fields['event_time'].widget.input_type, 'time')
        self.assertEqual(form.fields['guest_count'].widget.input_type, 'number')
