{% extends 'management/base.html' %}

{% block title %}Booking Calendar{% endblock %}
{% block page_title %}Booking Calendar{% endblock %}

{% block content %}
<!-- Calendar Header -->
<div class="bg-white shadow rounded-lg mb-6">
    <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <h2 class="text-xl font-semibold text-gray-900">{{ current_month|date:"F Y" }}</h2>
                <div class="flex items-center space-x-2">
                    <a href="?year={{ prev_month.year }}&month={{ prev_month.month }}" 
                       class="p-2 text-gray-400 hover:text-gray-600">
                        <i class="fas fa-chevron-left"></i>
                    </a>
                    <a href="?year={{ next_month.year }}&month={{ next_month.month }}" 
                       class="p-2 text-gray-400 hover:text-gray-600">
                        <i class="fas fa-chevron-right"></i>
                    </a>
                </div>
            </div>
            <div class="flex items-center space-x-4">
                <div class="text-sm text-gray-600">
                    Total Bookings: <span class="font-medium">{{ total_bookings }}</span>
                </div>
                <a href="{% url 'savory_events:management_booking_conflicts' %}" 
                   class="bg-orange-600 hover:bg-orange-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                    <i class="fas fa-exclamation-triangle mr-2"></i>View Conflicts
                </a>
                <a href="{% url 'savory_events:management_bookings' %}" 
                   class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                    <i class="fas fa-list mr-2"></i>List View
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Calendar Grid -->
<div class="bg-white shadow rounded-lg overflow-hidden">
    <!-- Calendar Header -->
    <div class="grid grid-cols-7 bg-gray-50 border-b border-gray-200">
        <div class="px-4 py-3 text-center text-sm font-medium text-gray-700">Sunday</div>
        <div class="px-4 py-3 text-center text-sm font-medium text-gray-700">Monday</div>
        <div class="px-4 py-3 text-center text-sm font-medium text-gray-700">Tuesday</div>
        <div class="px-4 py-3 text-center text-sm font-medium text-gray-700">Wednesday</div>
        <div class="px-4 py-3 text-center text-sm font-medium text-gray-700">Thursday</div>
        <div class="px-4 py-3 text-center text-sm font-medium text-gray-700">Friday</div>
        <div class="px-4 py-3 text-center text-sm font-medium text-gray-700">Saturday</div>
    </div>
    
    <!-- Calendar Body -->
    <div class="grid grid-cols-7">
        {% for week in calendar_data %}
            {% for day_data in week %}
                {% if day_data %}
                <div class="min-h-32 border-r border-b border-gray-200 p-2 
                    {% if day_data.has_conflicts %}bg-red-50{% elif day_data.booking_count > 0 %}bg-blue-50{% endif %}">
                    
                    <!-- Day Number -->
                    <div class="flex items-center justify-between mb-2">
                        <span class="text-sm font-medium text-gray-900">{{ day_data.day }}</span>
                        {% if day_data.booking_count > 0 %}
                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                            {% if day_data.has_conflicts %}bg-red-100 text-red-800
                            {% else %}bg-blue-100 text-blue-800{% endif %}">
                            {{ day_data.booking_count }}
                        </span>
                        {% endif %}
                    </div>
                    
                    <!-- Bookings for this day -->
                    {% if day_data.bookings %}
                    <div class="space-y-1">
                        {% for booking in day_data.bookings %}
                        <div class="text-xs p-1 rounded cursor-pointer hover:bg-white
                            {% if booking.status == 'pending' %}bg-yellow-100 text-yellow-800 border-l-2 border-yellow-400
                            {% elif booking.status == 'confirmed' %}bg-blue-100 text-blue-800 border-l-2 border-blue-400
                            {% elif booking.status == 'partial_paid' %}bg-orange-100 text-orange-800 border-l-2 border-orange-400
                            {% elif booking.status == 'fully_paid' %}bg-green-100 text-green-800 border-l-2 border-green-400
                            {% elif booking.status == 'completed' %}bg-gray-100 text-gray-800 border-l-2 border-gray-400
                            {% else %}bg-red-100 text-red-800 border-l-2 border-red-400{% endif %}"
                            onclick="window.location.href='{% url 'savory_events:management_booking_detail' booking.id %}'">
                            
                            <div class="font-medium truncate">{{ booking.customer.name }}</div>
                            <div class="text-xs opacity-75">{{ booking.event_date|time:"g:i A" }}</div>
                            <div class="text-xs opacity-75">{{ booking.guest_count }} guests</div>
                            {% if day_data.has_conflicts %}
                            <div class="text-xs text-red-600 font-medium">
                                <i class="fas fa-exclamation-triangle mr-1"></i>Conflict
                            </div>
                            {% endif %}
                        </div>
                        {% endfor %}
                    </div>
                    {% endif %}
                </div>
                {% else %}
                <!-- Empty day (previous/next month) -->
                <div class="min-h-32 border-r border-b border-gray-200 bg-gray-50"></div>
                {% endif %}
            {% endfor %}
        {% endfor %}
    </div>
</div>

<!-- Legend -->
<div class="mt-6 bg-white shadow rounded-lg">
    <div class="px-6 py-4">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Legend</h3>
        <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
            <div class="flex items-center">
                <div class="w-4 h-4 bg-yellow-100 border-l-2 border-yellow-400 mr-2"></div>
                <span class="text-sm text-gray-700">Pending</span>
            </div>
            <div class="flex items-center">
                <div class="w-4 h-4 bg-blue-100 border-l-2 border-blue-400 mr-2"></div>
                <span class="text-sm text-gray-700">Confirmed</span>
            </div>
            <div class="flex items-center">
                <div class="w-4 h-4 bg-orange-100 border-l-2 border-orange-400 mr-2"></div>
                <span class="text-sm text-gray-700">Partial Paid</span>
            </div>
            <div class="flex items-center">
                <div class="w-4 h-4 bg-green-100 border-l-2 border-green-400 mr-2"></div>
                <span class="text-sm text-gray-700">Fully Paid</span>
            </div>
            <div class="flex items-center">
                <div class="w-4 h-4 bg-gray-100 border-l-2 border-gray-400 mr-2"></div>
                <span class="text-sm text-gray-700">Completed</span>
            </div>
            <div class="flex items-center">
                <div class="w-4 h-4 bg-red-100 border-l-2 border-red-400 mr-2"></div>
                <span class="text-sm text-gray-700">Cancelled</span>
            </div>
        </div>
        <div class="mt-4 pt-4 border-t border-gray-200">
            <div class="flex items-center space-x-6">
                <div class="flex items-center">
                    <div class="w-4 h-4 bg-blue-50 mr-2"></div>
                    <span class="text-sm text-gray-700">Days with bookings</span>
                </div>
                <div class="flex items-center">
                    <div class="w-4 h-4 bg-red-50 mr-2"></div>
                    <span class="text-sm text-gray-700">Days with conflicts</span>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Stats -->
<div class="mt-6 grid grid-cols-1 md:grid-cols-4 gap-6">
    <div class="bg-white shadow rounded-lg p-6">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <i class="fas fa-calendar-alt text-blue-500 text-2xl"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-500">This Month</p>
                <p class="text-2xl font-semibold text-gray-900">{{ total_bookings }}</p>
            </div>
        </div>
    </div>
    
    <div class="bg-white shadow rounded-lg p-6">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <i class="fas fa-exclamation-triangle text-orange-500 text-2xl"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-500">Conflicts</p>
                <p class="text-2xl font-semibold text-gray-900">
                    {% with conflicts=calendar_data|length %}
                    {{ conflicts|default:0 }}
                    {% endwith %}
                </p>
            </div>
        </div>
    </div>
    
    <div class="bg-white shadow rounded-lg p-6">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <i class="fas fa-clock text-green-500 text-2xl"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-500">Upcoming</p>
                <p class="text-2xl font-semibold text-gray-900">
                    {% with upcoming=bookings_by_date|length %}
                    {{ upcoming|default:0 }}
                    {% endwith %}
                </p>
            </div>
        </div>
    </div>
    
    <div class="bg-white shadow rounded-lg p-6">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <i class="fas fa-users text-purple-500 text-2xl"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-500">Busy Days</p>
                <p class="text-2xl font-semibold text-gray-900">
                    {% with busy_days=calendar_data|length %}
                    {{ busy_days|default:0 }}
                    {% endwith %}
                </p>
            </div>
        </div>
    </div>
</div>

<style>
.min-h-32 {
    min-height: 8rem;
}
</style>
{% endblock %}
