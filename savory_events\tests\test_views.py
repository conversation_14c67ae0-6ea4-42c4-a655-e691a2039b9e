from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.auth.models import User
from django.utils import timezone
from decimal import Decimal
from datetime import timedelta
import json

from savory_events.models import (
    Customer, MenuItem, SoundSystemService, Booking, 
    Payment, BookingMenuItem, BookingSoundSystem
)


class HomeViewTest(TestCase):
    """Test cases for home view."""
    
    def setUp(self):
        self.client = Client()
    
    def test_home_view_status_code(self):
        """Test home view returns 200."""
        response = self.client.get(reverse('savory_events:home'))
        self.assertEqual(response.status_code, 200)
    
    def test_home_view_template(self):
        """Test home view uses correct template."""
        response = self.client.get(reverse('savory_events:home'))
        self.assertTemplateUsed(response, 'home.html')


class BookingViewTest(TestCase):
    """Test cases for booking views."""
    
    def setUp(self):
        self.client = Client()
        
        # Create test menu items
        self.menu_item1 = MenuItem.objects.create(
            name='Test Appetizer',
            description='Test description',
            category='appetizer',
            price_per_person=Decimal('8.50')
        )
        
        self.menu_item2 = MenuItem.objects.create(
            name='Test Main',
            description='Test main dish',
            category='main',
            price_per_person=Decimal('25.00')
        )
        
        # Create test sound system
        self.sound_system = SoundSystemService.objects.create(
            name='Test DJ Package',
            package_type='dj',
            description='Test DJ service',
            price=Decimal('400.00')
        )
    
    def test_booking_view_status_code(self):
        """Test booking view returns 200."""
        response = self.client.get(reverse('savory_events:booking'))
        self.assertEqual(response.status_code, 200)
    
    def test_booking_view_template(self):
        """Test booking view uses correct template."""
        response = self.client.get(reverse('savory_events:booking'))
        self.assertTemplateUsed(response, 'booking/booking_form.html')
    
    def test_booking_wizard_view_status_code(self):
        """Test booking wizard view returns 200."""
        response = self.client.get(reverse('savory_events:booking_wizard'))
        self.assertEqual(response.status_code, 200)
    
    def test_booking_wizard_view_template(self):
        """Test booking wizard view uses correct template."""
        response = self.client.get(reverse('savory_events:booking_wizard'))
        self.assertTemplateUsed(response, 'booking/booking_wizard.html')
    
    def test_booking_wizard_context(self):
        """Test booking wizard view provides menu items in context."""
        response = self.client.get(reverse('savory_events:booking_wizard'))
        self.assertIn('menu_items', response.context)
        
        # Parse JSON to verify content
        menu_items = json.loads(response.context['menu_items'])
        self.assertEqual(len(menu_items), 2)
        self.assertEqual(menu_items[0]['name'], 'Test Appetizer')


class HTMXBookingSubmitTest(TestCase):
    """Test cases for HTMX booking submission."""
    
    def setUp(self):
        self.client = Client()
        
        # Create test menu items
        self.menu_item = MenuItem.objects.create(
            name='Test Menu Item',
            description='Test description',
            category='main',
            price_per_person=Decimal('20.00')
        )
        
        # Create test sound system
        self.sound_system = SoundSystemService.objects.create(
            name='Test Sound System',
            package_type='dj',
            description='Test sound system',
            price=Decimal('300.00')
        )
        
        self.valid_booking_data = {
            'customer_name': 'John Doe',
            'customer_email': '<EMAIL>',
            'customer_phone': '(*************',
            'event_type': 'wedding',
            'event_date': (timezone.now() + timedelta(days=30)).date(),
            'event_time': '18:00',
            'guest_count': 50,
            'venue_address': '123 Wedding Venue Lane',
            'sound_system': 'dj',
            'special_requests': 'No nuts please',
            'selected_menu_items': [str(self.menu_item.id)]
        }
    
    def test_htmx_booking_submit_success(self):
        """Test successful HTMX booking submission."""
        response = self.client.post(
            reverse('savory_events:htmx_booking_submit'),
            data=self.valid_booking_data,
            HTTP_HX_REQUEST='true'
        )
        
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'booking/booking_success.html')
        
        # Verify booking was created
        self.assertEqual(Booking.objects.count(), 1)
        booking = Booking.objects.first()
        self.assertEqual(booking.customer.name, 'John Doe')
        self.assertEqual(booking.guest_count, 50)
        
        # Verify customer was created
        self.assertEqual(Customer.objects.count(), 1)
        customer = Customer.objects.first()
        self.assertEqual(customer.email, '<EMAIL>')
        
        # Verify menu item was linked
        self.assertEqual(BookingMenuItem.objects.count(), 1)
        booking_item = BookingMenuItem.objects.first()
        self.assertEqual(booking_item.menu_item, self.menu_item)
        
        # Verify sound system was linked
        self.assertEqual(BookingSoundSystem.objects.count(), 1)
        booking_sound = BookingSoundSystem.objects.first()
        self.assertEqual(booking_sound.sound_system, self.sound_system)
    
    def test_htmx_booking_submit_invalid_data(self):
        """Test HTMX booking submission with invalid data."""
        invalid_data = self.valid_booking_data.copy()
        invalid_data['customer_email'] = 'invalid-email'
        
        response = self.client.post(
            reverse('savory_events:htmx_booking_submit'),
            data=invalid_data,
            HTTP_HX_REQUEST='true'
        )
        
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'booking/booking_form_errors.html')
        
        # Verify no booking was created
        self.assertEqual(Booking.objects.count(), 0)
    
    def test_htmx_booking_submit_without_htmx_header(self):
        """Test booking submission without HTMX header."""
        response = self.client.post(
            reverse('savory_events:htmx_booking_submit'),
            data=self.valid_booking_data
        )
        
        self.assertEqual(response.status_code, 400)
    
    def test_booking_with_existing_customer(self):
        """Test booking with existing customer email."""
        # Create existing customer
        existing_customer = Customer.objects.create(
            name='Existing Customer',
            email='<EMAIL>',
            phone='(*************'
        )
        
        response = self.client.post(
            reverse('savory_events:htmx_booking_submit'),
            data=self.valid_booking_data,
            HTTP_HX_REQUEST='true'
        )
        
        self.assertEqual(response.status_code, 200)
        
        # Verify only one customer exists but with updated info
        self.assertEqual(Customer.objects.count(), 1)
        customer = Customer.objects.first()
        self.assertEqual(customer.name, 'John Doe')  # Updated name
        self.assertEqual(customer.phone, '(*************')  # Updated phone


class MenuViewTest(TestCase):
    """Test cases for menu views."""
    
    def setUp(self):
        self.client = Client()
        
        # Create test menu items
        self.menu_item1 = MenuItem.objects.create(
            name='Appetizer 1',
            description='Test appetizer',
            category='appetizer',
            price_per_person=Decimal('8.50')
        )
        
        self.menu_item2 = MenuItem.objects.create(
            name='Main Dish 1',
            description='Test main dish',
            category='main',
            price_per_person=Decimal('25.00'),
            is_available=False  # This should not appear
        )
    
    def test_menu_view_status_code(self):
        """Test menu view returns 200."""
        response = self.client.get(reverse('savory_events:menu'))
        self.assertEqual(response.status_code, 200)
    
    def test_menu_view_template(self):
        """Test menu view uses correct template."""
        response = self.client.get(reverse('savory_events:menu'))
        self.assertTemplateUsed(response, 'menu/menu_list.html')
    
    def test_menu_view_context(self):
        """Test menu view provides available menu items only."""
        response = self.client.get(reverse('savory_events:menu'))
        self.assertIn('menu_items', response.context)
        
        # Parse JSON to verify only available items are included
        menu_items = json.loads(response.context['menu_items'])
        self.assertEqual(len(menu_items), 1)  # Only available item
        self.assertEqual(menu_items[0]['name'], 'Appetizer 1')


class HTMXMenuUpdateTest(TestCase):
    """Test cases for HTMX menu update."""
    
    def setUp(self):
        self.client = Client()
        
        self.menu_item1 = MenuItem.objects.create(
            name='Test Item 1',
            description='Test description',
            category='main',
            price_per_person=Decimal('20.00')
        )
        
        self.menu_item2 = MenuItem.objects.create(
            name='Test Item 2',
            description='Test description',
            category='main',
            price_per_person=Decimal('30.00')
        )
    
    def test_htmx_menu_update_success(self):
        """Test HTMX menu update with selected items."""
        response = self.client.post(
            reverse('savory_events:htmx_menu_update'),
            data={
                'selected_items': [str(self.menu_item1.id), str(self.menu_item2.id)],
                'guest_count': 10
            },
            HTTP_HX_REQUEST='true'
        )
        
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'menu/menu_summary.html')
        
        # Check context data
        self.assertIn('selected_items', response.context)
        self.assertIn('total', response.context)
        self.assertEqual(response.context['guest_count'], 10)
        
        # Verify total calculation: (20 + 30) * 10 = 500
        self.assertEqual(response.context['total'], 500)
    
    def test_htmx_menu_update_no_items(self):
        """Test HTMX menu update with no selected items."""
        response = self.client.post(
            reverse('savory_events:htmx_menu_update'),
            data={
                'selected_items': [],
                'guest_count': 10
            },
            HTTP_HX_REQUEST='true'
        )
        
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.context['total'], 0)
    
    def test_htmx_menu_update_without_htmx_header(self):
        """Test menu update without HTMX header."""
        response = self.client.post(
            reverse('savory_events:htmx_menu_update'),
            data={'selected_items': []}
        )
        
        self.assertEqual(response.status_code, 400)


class AdminDashboardTest(TestCase):
    """Test cases for admin dashboard."""
    
    def setUp(self):
        self.client = Client()
        
        # Create admin user
        self.admin_user = User.objects.create_user(
            username='admin',
            email='<EMAIL>',
            password='testpass123',
            is_staff=True
        )
        
        # Create regular user
        self.regular_user = User.objects.create_user(
            username='regular',
            email='<EMAIL>',
            password='testpass123'
        )
        
        # Create test data
        self.customer = Customer.objects.create(
            name='Test Customer',
            email='<EMAIL>',
            phone='1234567890'
        )
        
        self.booking = Booking.objects.create(
            customer=self.customer,
            event_date=timezone.now() + timedelta(days=30),
            event_type='wedding',
            guest_count=50,
            venue_address='123 Test Venue',
            status='pending'
        )
    
    def test_admin_dashboard_requires_staff(self):
        """Test admin dashboard requires staff permission."""
        # Test unauthenticated user
        response = self.client.get(reverse('savory_events:admin_dashboard'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'admin/login.html')
        
        # Test regular user
        self.client.login(username='regular', password='testpass123')
        response = self.client.get(reverse('savory_events:admin_dashboard'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'admin/login.html')
    
    def test_admin_dashboard_staff_access(self):
        """Test admin dashboard allows staff access."""
        self.client.login(username='admin', password='testpass123')
        response = self.client.get(reverse('savory_events:admin_dashboard'))
        
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'admin/dashboard.html')
    
    def test_admin_dashboard_context(self):
        """Test admin dashboard provides correct context."""
        self.client.login(username='admin', password='testpass123')
        response = self.client.get(reverse('savory_events:admin_dashboard'))
        
        self.assertIn('total_bookings', response.context)
        self.assertIn('pending_payments', response.context)
        self.assertIn('recent_bookings', response.context)
        
        self.assertEqual(response.context['total_bookings'], 1)
        self.assertEqual(response.context['pending_payments'], 1)


class HTMXAdminModalTest(TestCase):
    """Test cases for HTMX admin modal."""
    
    def setUp(self):
        self.client = Client()
        
        # Create admin user
        self.admin_user = User.objects.create_user(
            username='admin',
            email='<EMAIL>',
            password='testpass123',
            is_staff=True
        )
        
        # Create test booking
        self.customer = Customer.objects.create(
            name='Test Customer',
            email='<EMAIL>',
            phone='1234567890'
        )
        
        self.booking = Booking.objects.create(
            customer=self.customer,
            event_date=timezone.now() + timedelta(days=30),
            event_type='wedding',
            guest_count=50,
            venue_address='123 Test Venue',
            total_amount=Decimal('1000.00')
        )
    
    def test_htmx_admin_modal_requires_staff(self):
        """Test HTMX admin modal requires staff permission."""
        response = self.client.post(
            reverse('savory_events:htmx_admin_modal'),
            data={'booking_id': self.booking.id},
            HTTP_HX_REQUEST='true'
        )
        
        self.assertEqual(response.status_code, 403)
    
    def test_htmx_admin_modal_success(self):
        """Test successful HTMX admin modal payment processing."""
        self.client.login(username='admin', password='testpass123')
        
        response = self.client.post(
            reverse('savory_events:htmx_admin_modal'),
            data={
                'booking_id': self.booking.id,
                'payment_amount': '500.00',
                'payment_type': 'deposit',
                'payment_notes': 'Test payment'
            },
            HTTP_HX_REQUEST='true'
        )
        
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'admin/payment_success.html')
        
        # Verify payment was created
        self.assertEqual(Payment.objects.count(), 1)
        payment = Payment.objects.first()
        self.assertEqual(payment.amount, Decimal('500.00'))
        self.assertEqual(payment.payment_type, 'deposit')
    
    def test_htmx_admin_modal_invalid_data(self):
        """Test HTMX admin modal with invalid payment data."""
        self.client.login(username='admin', password='testpass123')
        
        response = self.client.post(
            reverse('savory_events:htmx_admin_modal'),
            data={
                'booking_id': self.booking.id,
                'payment_amount': 'invalid',
                'payment_type': 'deposit'
            },
            HTTP_HX_REQUEST='true'
        )
        
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'admin/modal_response.html')
        
        # Verify no payment was created
        self.assertEqual(Payment.objects.count(), 0)


class ServiceViewTest(TestCase):
    """Test cases for service views."""
    
    def setUp(self):
        self.client = Client()
    
    def test_services_view_status_code(self):
        """Test services view returns 200."""
        response = self.client.get(reverse('savory_events:services'))
        self.assertEqual(response.status_code, 200)
    
    def test_services_view_template(self):
        """Test services view uses correct template."""
        response = self.client.get(reverse('savory_events:services'))
        self.assertTemplateUsed(response, 'services.html')


class ContactViewTest(TestCase):
    """Test cases for contact view."""
    
    def setUp(self):
        self.client = Client()
    
    def test_contact_view_status_code(self):
        """Test contact view returns 200."""
        response = self.client.get(reverse('savory_events:contact'))
        self.assertEqual(response.status_code, 200)
    
    def test_contact_view_template(self):
        """Test contact view uses correct template."""
        response = self.client.get(reverse('savory_events:contact'))
        self.assertTemplateUsed(response, 'contact.html')
