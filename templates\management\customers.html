{% extends 'management/base.html' %}

{% block title %}Customer Management{% endblock %}
{% block page_title %}Customer Management{% endblock %}

{% block content %}
<!-- Search and Filter -->
<div class="bg-white shadow rounded-lg mb-6">
    <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900">Search & Filter</h3>
    </div>
    <div class="px-6 py-4">
        <form method="get" class="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-4">
            
            <!-- Search -->
            <div class="lg:col-span-2">
                <label for="search" class="block text-sm font-medium text-gray-700 mb-1">Search Customers</label>
                <input type="text" 
                       name="search" 
                       id="search"
                       value="{{ search_query }}"
                       placeholder="Name, email, or phone..."
                       class="block w-full border-gray-300 rounded-md shadow-sm focus:ring-deep-olive focus:border-deep-olive">
            </div>
            
            <!-- Sort By -->
            <div>
                <label for="sort" class="block text-sm font-medium text-gray-700 mb-1">Sort By</label>
                <select name="sort" 
                        id="sort"
                        class="block w-full border-gray-300 rounded-md shadow-sm focus:ring-deep-olive focus:border-deep-olive">
                    <option value="created_at" {% if sort_by == 'created_at' %}selected{% endif %}>Newest First</option>
                    <option value="name" {% if sort_by == 'name' %}selected{% endif %}>Name</option>
                    <option value="email" {% if sort_by == 'email' %}selected{% endif %}>Email</option>
                    <option value="total_bookings" {% if sort_by == 'total_bookings' %}selected{% endif %}>Most Bookings</option>
                    <option value="total_spent" {% if sort_by == 'total_spent' %}selected{% endif %}>Highest Spender</option>
                </select>
            </div>
            
            <!-- Filter Actions -->
            <div class="flex items-end space-x-2">
                <button type="submit" 
                        class="flex-1 bg-deep-olive text-white px-4 py-2 rounded-md hover:bg-burnt-orange transition-colors duration-200">
                    <i class="fas fa-search mr-2"></i>
                    Search
                </button>
                <a href="{% url 'savory_events:management_customers' %}" 
                   class="px-3 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50">
                    <i class="fas fa-times"></i>
                </a>
            </div>
        </form>
    </div>
</div>

<!-- Customer Statistics -->
<div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
    <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="h-8 w-8 bg-blue-500 rounded-md flex items-center justify-center">
                        <i class="fas fa-users text-white"></i>
                    </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Total Customers</dt>
                        <dd class="text-lg font-medium text-gray-900">{{ customers.paginator.count }}</dd>
                    </dl>
                </div>
            </div>
        </div>
    </div>
    
    <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="h-8 w-8 bg-green-500 rounded-md flex items-center justify-center">
                        <i class="fas fa-repeat text-white"></i>
                    </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Repeat Customers</dt>
                        <dd class="text-lg font-medium text-gray-900">
                            {% for customer in customers %}
                                {% if customer.total_bookings > 1 %}{{ forloop.counter0|add:1 }}{% endif %}
                            {% empty %}0{% endfor %}
                        </dd>
                    </dl>
                </div>
            </div>
        </div>
    </div>
    
    <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="h-8 w-8 bg-purple-500 rounded-md flex items-center justify-center">
                        <i class="fas fa-calendar-check text-white"></i>
                    </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Active This Month</dt>
                        <dd class="text-lg font-medium text-gray-900">-</dd>
                    </dl>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Customers List -->
<div class="bg-white shadow rounded-lg">
    <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex items-center justify-between">
            <h3 class="text-lg font-medium text-gray-900">
                Customers ({{ customers.paginator.count }} total)
            </h3>
            <a href="{% url 'savory_events:booking_wizard' %}" 
               class="bg-deep-olive text-white px-4 py-2 rounded-md hover:bg-burnt-orange transition-colors duration-200">
                <i class="fas fa-plus mr-2"></i>
                New Booking
            </a>
        </div>
    </div>
    
    <div class="overflow-hidden">
        <div class="overflow-x-auto">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 p-6">
                {% for customer in customers %}
                <div class="bg-gray-50 rounded-lg p-6 hover:shadow-lg transition-shadow duration-200">
                    <div class="flex items-center space-x-4">
                        <div class="h-12 w-12 rounded-full bg-deep-olive flex items-center justify-center">
                            <span class="text-white font-semibold text-lg">
                                {{ customer.name|first|upper }}
                            </span>
                        </div>
                        <div class="flex-1 min-w-0">
                            <h4 class="text-lg font-medium text-gray-900 truncate">{{ customer.name }}</h4>
                            <p class="text-sm text-gray-500 truncate">{{ customer.email }}</p>
                            {% if customer.phone %}
                            <p class="text-sm text-gray-500">{{ customer.phone }}</p>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="mt-4 grid grid-cols-2 gap-4">
                        <div>
                            <div class="text-2xl font-bold text-gray-900">{{ customer.total_bookings|default:0 }}</div>
                            <div class="text-sm text-gray-500">Bookings</div>
                        </div>
                        <div>
                            <div class="text-2xl font-bold text-gray-900">
                                ${{ customer.total_spent|default:0|floatformat:0 }}
                            </div>
                            <div class="text-sm text-gray-500">Total Spent</div>
                        </div>
                    </div>
                    
                    <div class="mt-4 text-sm text-gray-500">
                        <div class="flex items-center">
                            <i class="fas fa-calendar mr-2"></i>
                            Customer since {{ customer.created_at|date:"M Y" }}
                        </div>
                        {% if customer.last_booking %}
                        <div class="flex items-center mt-1">
                            <i class="fas fa-clock mr-2"></i>
                            Last booking {{ customer.last_booking|date:"M d, Y" }}
                        </div>
                        {% endif %}
                    </div>
                    
                    <div class="mt-6 flex space-x-3">
                        <a href="{% url 'savory_events:management_customer_detail' customer.id %}" 
                           class="flex-1 text-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 transition-colors duration-200">
                            <i class="fas fa-eye mr-2"></i>
                            View Details
                        </a>
                        <a href="mailto:{{ customer.email }}" 
                           class="px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 transition-colors duration-200">
                            <i class="fas fa-envelope"></i>
                        </a>
                        <a href="tel:{{ customer.phone }}" 
                           class="px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 transition-colors duration-200">
                            <i class="fas fa-phone"></i>
                        </a>
                    </div>
                </div>
                {% empty %}
                <div class="col-span-full text-center py-12">
                    <div class="text-gray-500">
                        <i class="fas fa-users text-6xl mb-4 text-gray-300"></i>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">No customers found</h3>
                        <p class="text-sm">Try adjusting your search criteria or create a new booking to add customers.</p>
                        <a href="{% url 'savory_events:booking_wizard' %}" 
                           class="inline-flex items-center mt-4 px-4 py-2 bg-deep-olive text-white rounded-md hover:bg-burnt-orange transition-colors duration-200">
                            <i class="fas fa-plus mr-2"></i>
                            Create New Booking
                        </a>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>

    <!-- Pagination -->
    {% if customers.has_other_pages %}
    <div class="bg-white px-4 py-3 border-t border-gray-200 sm:px-6">
        <div class="flex-1 flex justify-between sm:hidden">
            {% if customers.has_previous %}
                <a href="?page={{ customers.previous_page_number }}&{{ request.GET.urlencode }}" 
                   class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    Previous
                </a>
            {% endif %}
            
            {% if customers.has_next %}
                <a href="?page={{ customers.next_page_number }}&{{ request.GET.urlencode }}" 
                   class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    Next
                </a>
            {% endif %}
        </div>
        
        <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
            <div>
                <p class="text-sm text-gray-700">
                    Showing
                    <span class="font-medium">{{ customers.start_index }}</span>
                    to
                    <span class="font-medium">{{ customers.end_index }}</span>
                    of
                    <span class="font-medium">{{ customers.paginator.count }}</span>
                    results
                </p>
            </div>
            
            <div>
                <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                    {% if customers.has_previous %}
                        <a href="?page={{ customers.previous_page_number }}&{{ request.GET.urlencode }}" 
                           class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                            <span class="sr-only">Previous</span>
                            <i class="fas fa-chevron-left"></i>
                        </a>
                    {% endif %}
                    
                    {% for num in customers.paginator.page_range %}
                        {% if customers.number == num %}
                            <span class="bg-deep-olive border-deep-olive text-white relative inline-flex items-center px-4 py-2 border text-sm font-medium">
                                {{ num }}
                            </span>
                        {% else %}
                            <a href="?page={{ num }}&{{ request.GET.urlencode }}" 
                               class="bg-white border-gray-300 text-gray-500 hover:bg-gray-50 relative inline-flex items-center px-4 py-2 border text-sm font-medium">
                                {{ num }}
                            </a>
                        {% endif %}
                    {% endfor %}
                    
                    {% if customers.has_next %}
                        <a href="?page={{ customers.next_page_number }}&{{ request.GET.urlencode }}" 
                           class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                            <span class="sr-only">Next</span>
                            <i class="fas fa-chevron-right"></i>
                        </a>
                    {% endif %}
                </nav>
            </div>
        </div>
    </div>
    {% endif %}
</div>

{% csrf_token %}
{% endblock %}
