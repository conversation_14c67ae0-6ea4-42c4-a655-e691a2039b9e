{% extends 'customer/base.html' %}

{% block title %}My Profile - BonAppetit Catering{% endblock %}

{% block page_title %}My Profile{% endblock %}

{% block page_subtitle %}Manage your account information and preferences{% endblock %}

{% block content %}
<div class="py-8">
            <!-- Header -->
            <div class="mb-8">
                <h1 class="text-3xl font-bold text-charcoal mb-2">My Profile</h1>
                <p class="text-gray-600">Manage your account information and preferences.</p>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                <!-- Profile form -->
                <div class="lg:col-span-2">
                    <div class="bg-white rounded-lg shadow-md p-6">
                        <h2 class="text-xl font-semibold text-charcoal mb-6">Personal Information</h2>
                        
                        <form method="post" class="space-y-6">
                            {% csrf_token %}
                            
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label for="first_name" class="block text-sm font-medium text-charcoal mb-2">
                                        First Name
                                    </label>
                                    <input type="text" 
                                           id="first_name" 
                                           name="first_name" 
                                           value="{{ user.first_name }}"
                                           class="w-full px-3 py-2 border border-sage-green rounded-md focus:outline-none focus:ring-2 focus:ring-burnt-orange transition-colors"
                                           required>
                                </div>
                                
                                <div>
                                    <label for="last_name" class="block text-sm font-medium text-charcoal mb-2">
                                        Last Name
                                    </label>
                                    <input type="text" 
                                           id="last_name" 
                                           name="last_name" 
                                           value="{{ user.last_name }}"
                                           class="w-full px-3 py-2 border border-sage-green rounded-md focus:outline-none focus:ring-2 focus:ring-burnt-orange transition-colors"
                                           required>
                                </div>
                            </div>

                            <div>
                                <label for="email" class="block text-sm font-medium text-charcoal mb-2">
                                    Email Address
                                </label>
                                <input type="email" 
                                       id="email" 
                                       name="email" 
                                       value="{{ user.email }}"
                                       class="w-full px-3 py-2 border border-sage-green rounded-md focus:outline-none focus:ring-2 focus:ring-burnt-orange transition-colors"
                                       required>
                            </div>

                            <div>
                                <label for="phone" class="block text-sm font-medium text-charcoal mb-2">
                                    Phone Number
                                </label>
                                <input type="tel" 
                                       id="phone" 
                                       name="phone" 
                                       value="{{ customer.phone }}"
                                       class="w-full px-3 py-2 border border-sage-green rounded-md focus:outline-none focus:ring-2 focus:ring-burnt-orange transition-colors"
                                       placeholder="(*************">
                            </div>

                            <div class="pt-4">
                                <button type="submit" 
                                        class="w-full md:w-auto px-6 py-2 bg-burnt-orange text-white font-medium rounded-md hover:bg-opacity-80 focus:outline-none focus:ring-2 focus:ring-burnt-orange transition-colors">
                                    Update Profile
                                </button>
                            </div>
                        </form>
                    </div>

                    <!-- Account Security -->
                    <div class="bg-white rounded-lg shadow-md p-6 mt-6">
                        <h2 class="text-xl font-semibold text-charcoal mb-6">Account Security</h2>
                        
                        <div class="space-y-4">
                            <div class="flex items-center justify-between py-3 border-b border-gray-200">
                                <div>
                                    <h3 class="text-sm font-medium text-charcoal">Password</h3>
                                    <p class="text-sm text-gray-500">Last updated: Never</p>
                                </div>
                                <button class="text-burnt-orange hover:text-opacity-80 text-sm font-medium transition-colors">
                                    Change Password
                                </button>
                            </div>
                            
                            <div class="flex items-center justify-between py-3">
                                <div>
                                    <h3 class="text-sm font-medium text-charcoal">Two-Factor Authentication</h3>
                                    <p class="text-sm text-gray-500">Add an extra layer of security</p>
                                </div>
                                <button class="text-sage-green hover:text-opacity-80 text-sm font-medium transition-colors">
                                    Enable
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Profile sidebar -->
                <div class="space-y-6">
                    <!-- Profile summary -->
                    <div class="bg-white rounded-lg shadow-md p-6">
                        <div class="text-center">
                            <div class="h-20 w-20 rounded-full bg-burnt-orange mx-auto mb-4 flex items-center justify-center">
                                <span class="text-white font-bold text-2xl">
                                    {{ user.first_name.0|default:user.username.0 }}{{ user.last_name.0|default:"" }}
                                </span>
                            </div>
                            <h3 class="text-lg font-medium text-charcoal">{{ user.get_full_name|default:user.username }}</h3>
                            <p class="text-sm text-gray-500">{{ user.email }}</p>
                            <p class="text-xs text-gray-400 mt-2">Member since {{ user.date_joined|date:"M Y" }}</p>
                        </div>
                    </div>

                    <!-- Account stats -->
                    <div class="bg-white rounded-lg shadow-md p-6">
                        <h3 class="text-lg font-semibold text-charcoal mb-4">Account Activity</h3>
                        <div class="space-y-4">
                            <div class="flex items-center justify-between">
                                <span class="text-sm text-gray-600">Total Bookings</span>
                                <span class="text-sm font-medium text-charcoal">{{ total_bookings|default:0 }}</span>
                            </div>
                            <div class="flex items-center justify-between">
                                <span class="text-sm text-gray-600">Account Status</span>
                                <span class="text-sm font-medium text-green-600">Active</span>
                            </div>
                            <div class="flex items-center justify-between">
                                <span class="text-sm text-gray-600">Last Login</span>
                                <span class="text-sm font-medium text-charcoal">{{ user.last_login|date:"M d, Y"|default:"Never" }}</span>
                            </div>
                        </div>
                    </div>

                    <!-- Quick actions -->
                    <div class="bg-white rounded-lg shadow-md p-6">
                        <h3 class="text-lg font-semibold text-charcoal mb-4">Quick Actions</h3>
                        <div class="space-y-3">
                            <a href="{% url 'savory_events:booking' %}" 
                               class="block w-full text-center px-3 py-2 border border-burnt-orange text-burnt-orange rounded-md hover:bg-burnt-orange hover:text-white transition-colors">
                                New Booking
                            </a>
                            <a href="{% url 'savory_events:customer_bookings' %}" 
                               class="block w-full text-center px-3 py-2 border border-sage-green text-sage-green rounded-md hover:bg-sage-green hover:text-white transition-colors">
                                View Bookings
                            </a>
                            <a href="{% url 'savory_events:contact' %}" 
                               class="block w-full text-center px-3 py-2 border border-deep-olive text-deep-olive rounded-md hover:bg-deep-olive hover:text-white transition-colors">
                                Contact Support
                            </a>
                        </div>
                    </div>

                    <!-- Preferences -->
                    <div class="bg-white rounded-lg shadow-md p-6">
                        <h3 class="text-lg font-semibold text-charcoal mb-4">Preferences</h3>
                        <div class="space-y-3">
                            <label class="flex items-center">
                                <input type="checkbox" class="rounded border-sage-green text-burnt-orange focus:ring-burnt-orange" checked>
                                <span class="ml-2 text-sm text-charcoal">Email notifications</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" class="rounded border-sage-green text-burnt-orange focus:ring-burnt-orange">
                                <span class="ml-2 text-sm text-charcoal">SMS notifications</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" class="rounded border-sage-green text-burnt-orange focus:ring-burnt-orange" checked>
                                <span class="ml-2 text-sm text-charcoal">Marketing emails</span>
                            </label>
                        </div>
                    </div>
                </div>
            </div>
</div>
{% endblock %}
