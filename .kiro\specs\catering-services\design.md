# Design Document

## Overview

The catering services web application is built on Django with a mobile-first responsive design using Tailwind CSS. The system integrates modern frontend technologies (HTMX, Unpoly, Alpine.js) with Django's robust backend to create a seamless booking and management experience. The application follows a modular architecture with clear separation between customer-facing interfaces and admin management tools.

## Architecture

### System Architecture

```mermaid
graph TB
    A[Client Browser] --> B[Django Frontend Views]
    B --> C[HTMX/Unpoly Layer]
    C --> D[Django Backend Views]
    D --> E[Django Models]
    E --> F[SQLite Database]
    
    G[Admin Dashboard] --> H[Django Admin/Custom Views]
    H --> D
    
    I[Static Assets] --> J[Tailwind CSS]
    I --> K[Alpine.js]
    I --> L[HTMX/Unpoly]
```

### Technology Stack

**Frontend:**
- Tailwind CSS: Utility-first CSS framework for responsive design
- HTMX: Dynamic content updates without full page reloads
- Unpoly: Progressive enhancement and smooth transitions
- Alpine.js: Lightweight JavaScript framework for interactivity

**Backend:**
- Django 5.2.4: Web framework
- SQLite: Database (existing setup)
- Django Forms: Form handling with HTMX integration
- Django Admin: Enhanced for custom dashboard

### Application Flow

1. **Customer Journey:**
   - Landing page → Booking form → Menu selection → Payment notification → Confirmation
   
2. **Admin Journey:**
   - Login → Dashboard → Manage bookings → Process payments → Update status

## Components and Interfaces

### Core Models

#### Customer Model
```python
class Customer(models.Model):
    name = models.CharField(max_length=100)
    email = models.EmailField()
    phone = models.CharField(max_length=20)
    created_at = models.DateTimeField(auto_now_add=True)
```

#### Booking Model
```python
class Booking(models.Model):
    BOOKING_STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('confirmed', 'Confirmed'),
        ('partial_paid', 'Partially Paid'),
        ('fully_paid', 'Fully Paid'),
        ('completed', 'Completed'),
        ('cancelled', 'Cancelled'),
    ]
    
    EVENT_TYPE_CHOICES = [
        ('wedding', 'Wedding'),
        ('corporate', 'Corporate Event'),
        ('birthday', 'Birthday Party'),
        ('anniversary', 'Anniversary'),
        ('other', 'Other'),
    ]
    
    customer = models.ForeignKey(Customer, on_delete=models.CASCADE)
    event_date = models.DateTimeField()
    event_type = models.CharField(max_length=20, choices=EVENT_TYPE_CHOICES)
    guest_count = models.PositiveIntegerField()
    venue_address = models.TextField()
    special_requests = models.TextField(blank=True)
    status = models.CharField(max_length=20, choices=BOOKING_STATUS_CHOICES, default='pending')
    total_amount = models.DecimalField(max_digits=10, decimal_places=2)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
```

#### MenuItem Model
```python
class MenuItem(models.Model):
    CATEGORY_CHOICES = [
        ('appetizer', 'Appetizers'),
        ('main', 'Main Dishes'),
        ('dessert', 'Desserts'),
        ('drink', 'Drinks'),
    ]
    
    name = models.CharField(max_length=100)
    description = models.TextField()
    category = models.CharField(max_length=20, choices=CATEGORY_CHOICES)
    price_per_person = models.DecimalField(max_digits=8, decimal_places=2)
    image = models.ImageField(upload_to='menu_items/', blank=True)
    is_available = models.BooleanField(default=True)
```

#### BookingMenuItem Model (Many-to-Many relationship)
```python
class BookingMenuItem(models.Model):
    booking = models.ForeignKey(Booking, on_delete=models.CASCADE)
    menu_item = models.ForeignKey(MenuItem, on_delete=models.CASCADE)
    quantity = models.PositiveIntegerField(default=1)
```

#### Payment Model
```python
class Payment(models.Model):
    PAYMENT_TYPE_CHOICES = [
        ('deposit', 'Deposit (50%)'),
        ('balance', 'Balance (50%)'),
        ('full', 'Full Payment'),
    ]
    
    booking = models.ForeignKey(Booking, on_delete=models.CASCADE)
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    payment_type = models.CharField(max_length=20, choices=PAYMENT_TYPE_CHOICES)
    payment_date = models.DateTimeField(auto_now_add=True)
    notes = models.TextField(blank=True)
```

#### SoundSystemService Model
```python
class SoundSystemService(models.Model):
    PACKAGE_CHOICES = [
        ('dj', 'DJ Package'),
        ('microphones', 'Microphones Only'),
        ('full_audio', 'Full Audio Set'),
    ]
    
    name = models.CharField(max_length=50)
    package_type = models.CharField(max_length=20, choices=PACKAGE_CHOICES)
    description = models.TextField()
    price = models.DecimalField(max_digits=8, decimal_places=2)
    is_available = models.BooleanField(default=True)
```

#### BookingSoundSystem Model
```python
class BookingSoundSystem(models.Model):
    booking = models.OneToOneField(Booking, on_delete=models.CASCADE)
    sound_system = models.ForeignKey(SoundSystemService, on_delete=models.CASCADE)
```

### Frontend Components

#### Mobile-First Layout Structure
```html
<!-- Base template with Tailwind CSS -->
<div class="min-h-screen bg-cream">
    <!-- Mobile Navigation -->
    <nav class="bg-deep-olive text-white p-4 lg:hidden">
        <!-- Mobile menu toggle -->
    </nav>
    
    <!-- Desktop Navigation -->
    <nav class="hidden lg:block bg-deep-olive text-white">
        <!-- Desktop menu -->
    </nav>
    
    <!-- Main Content -->
    <main class="container mx-auto px-4 py-8">
        <!-- Page content -->
    </main>
</div>
```

#### Booking Form Component
- Multi-step form with progress indicator
- Real-time validation using Alpine.js
- HTMX for dynamic content loading
- Mobile-optimized input fields

#### Menu Selection Component
- Grid layout responsive to screen size
- Image lazy loading
- Real-time price calculation
- Category filtering with smooth transitions

#### Admin Dashboard Component
- Collapsible sidebar using Alpine.js
- HTMX-powered data tables
- Modal forms for quick actions
- Mobile-responsive design

## Data Models

### Database Schema Relationships

```mermaid
erDiagram
    Customer ||--o{ Booking : has
    Booking ||--o{ BookingMenuItem : contains
    MenuItem ||--o{ BookingMenuItem : selected_in
    Booking ||--o{ Payment : receives
    Booking ||--o| BookingSoundSystem : may_have
    SoundSystemService ||--o{ BookingSoundSystem : provides
    
    Customer {
        int id PK
        string name
        string email
        string phone
        datetime created_at
    }
    
    Booking {
        int id PK
        int customer_id FK
        datetime event_date
        string event_type
        int guest_count
        text venue_address
        text special_requests
        string status
        decimal total_amount
        datetime created_at
        datetime updated_at
    }
    
    MenuItem {
        int id PK
        string name
        text description
        string category
        decimal price_per_person
        string image
        boolean is_available
    }
    
    Payment {
        int id PK
        int booking_id FK
        decimal amount
        string payment_type
        datetime payment_date
        text notes
    }
    
    SoundSystemService {
        int id PK
        string name
        string package_type
        text description
        decimal price
        boolean is_available
    }
```

### Data Validation Rules

- **Customer:** Email validation, phone number format validation
- **Booking:** Event date must be in the future, guest count minimum 1
- **Payment:** Amount must be positive, cannot exceed booking total
- **Menu Items:** Price must be positive, image file type validation

## Error Handling

### Frontend Error Handling

1. **Form Validation Errors:**
   - Real-time validation using Alpine.js
   - HTMX error responses displayed inline
   - User-friendly error messages

2. **Network Errors:**
   - HTMX timeout handling
   - Retry mechanisms for failed requests
   - Offline state detection

3. **User Experience Errors:**
   - Loading states during HTMX requests
   - Graceful degradation when JavaScript is disabled
   - Accessible error announcements

### Backend Error Handling

1. **Model Validation:**
   - Django model field validation
   - Custom validation methods
   - Database constraint handling

2. **View Error Handling:**
   - Try-catch blocks for database operations
   - HTTP error responses (400, 404, 500)
   - Logging for debugging

3. **Payment Processing Errors:**
   - Validation of payment amounts
   - Duplicate payment prevention
   - Status consistency checks

## Testing Strategy

### Unit Testing

1. **Model Tests:**
   - Test model creation and validation
   - Test model methods and properties
   - Test model relationships

2. **View Tests:**
   - Test GET and POST requests
   - Test form validation
   - Test HTMX responses

3. **Form Tests:**
   - Test form validation
   - Test form rendering
   - Test form submission

### Integration Testing

1. **End-to-End Booking Flow:**
   - Customer registration → Booking creation → Menu selection → Payment notification

2. **Admin Workflow:**
   - Login → View bookings → Process payment → Update status

3. **HTMX Integration:**
   - Test dynamic content updates
   - Test form submissions via HTMX
   - Test error handling

### Frontend Testing

1. **Responsive Design:**
   - Test on multiple screen sizes
   - Test mobile navigation
   - Test touch interactions

2. **JavaScript Functionality:**
   - Test Alpine.js components
   - Test HTMX interactions
   - Test form validation

### Performance Testing

1. **Page Load Times:**
   - Optimize image loading
   - Minimize CSS/JS bundle sizes
   - Test on slow connections

2. **Database Performance:**
   - Query optimization
   - Index creation for frequently accessed fields
   - Pagination for large datasets

## Security Considerations

### Authentication & Authorization
- Django's built-in authentication for admin users
- Session-based authentication
- CSRF protection for all forms

### Data Protection
- Input sanitization and validation
- SQL injection prevention through Django ORM
- XSS protection through template escaping

### Privacy
- Customer data encryption at rest
- Secure handling of payment information
- GDPR compliance considerations

## Deployment Considerations

### Static Files
- Tailwind CSS compilation
- Static file collection and serving
- Media file handling for menu images

### Database
- Migration strategy
- Backup procedures
- Performance monitoring

### Monitoring
- Error logging and tracking
- Performance metrics
- User analytics (privacy-compliant)