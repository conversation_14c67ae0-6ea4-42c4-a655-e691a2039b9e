{% extends 'management/base.html' %}

{% block title %}Bookings Management{% endblock %}
{% block page_title %}Bookings Management{% endblock %}

{% block content %}
<!-- Filters and Search -->
<div class="bg-white shadow rounded-lg mb-6">
    <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900">Filters & Search</h3>
    </div>
    <div class="px-6 py-4">
        <form method="get" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
            
            <!-- Search -->
            <div class="lg:col-span-2">
                <label for="search" class="block text-sm font-medium text-gray-700 mb-1">Search</label>
                <input type="text" 
                       name="search" 
                       id="search"
                       value="{{ filters.search }}"
                       placeholder="Customer name, email, venue..."
                       class="block w-full border-gray-300 rounded-md shadow-sm focus:ring-deep-olive focus:border-deep-olive">
            </div>
            
            <!-- Status Filter -->
            <div>
                <label for="status" class="block text-sm font-medium text-gray-700 mb-1">Status</label>
                <select name="status" 
                        id="status"
                        class="block w-full border-gray-300 rounded-md shadow-sm focus:ring-deep-olive focus:border-deep-olive">
                    <option value="">All Statuses</option>
                    {% for value, label in status_choices %}
                    <option value="{{ value }}" {% if filters.status == value %}selected{% endif %}>{{ label }}</option>
                    {% endfor %}
                </select>
            </div>
            
            <!-- Event Type Filter -->
            <div>
                <label for="event_type" class="block text-sm font-medium text-gray-700 mb-1">Event Type</label>
                <select name="event_type" 
                        id="event_type"
                        class="block w-full border-gray-300 rounded-md shadow-sm focus:ring-deep-olive focus:border-deep-olive">
                    <option value="">All Types</option>
                    {% for value, label in event_type_choices %}
                    <option value="{{ value }}" {% if filters.event_type == value %}selected{% endif %}>{{ label }}</option>
                    {% endfor %}
                </select>
            </div>
            
            <!-- Filter Actions -->
            <div class="flex items-end space-x-2">
                <button type="submit" 
                        class="flex-1 bg-deep-olive text-white px-4 py-2 rounded-md hover:bg-burnt-orange transition-colors duration-200">
                    <i class="fas fa-search mr-2"></i>
                    Filter
                </button>
                <a href="{% url 'savory_events:management_bookings' %}" 
                   class="px-3 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50">
                    <i class="fas fa-times"></i>
                </a>
            </div>
        </form>
    </div>
</div>

<!-- Date Range Filter -->
<div class="bg-white shadow rounded-lg mb-6">
    <div class="px-6 py-4">
        <form method="get" class="flex flex-wrap items-end gap-4">
            {% for key, value in filters.items %}
                {% if key != 'date_from' and key != 'date_to' %}
                    <input type="hidden" name="{{ key }}" value="{{ value }}">
                {% endif %}
            {% endfor %}
            
            <div>
                <label for="date_from" class="block text-sm font-medium text-gray-700 mb-1">From Date</label>
                <input type="date" 
                       name="date_from" 
                       id="date_from"
                       value="{{ filters.date_from }}"
                       class="block w-full border-gray-300 rounded-md shadow-sm focus:ring-deep-olive focus:border-deep-olive">
            </div>
            
            <div>
                <label for="date_to" class="block text-sm font-medium text-gray-700 mb-1">To Date</label>
                <input type="date" 
                       name="date_to" 
                       id="date_to"
                       value="{{ filters.date_to }}"
                       class="block w-full border-gray-300 rounded-md shadow-sm focus:ring-deep-olive focus:border-deep-olive">
            </div>
            
            <button type="submit" 
                    class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors duration-200">
                Apply Date Filter
            </button>
        </form>
    </div>
</div>

<!-- Enhanced Booking Management -->
<div class="bg-white shadow rounded-lg mb-6">
    <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex items-center justify-between">
            <h3 class="text-lg font-medium text-gray-900">Quick Actions</h3>
            <div class="flex items-center space-x-3">
                <a href="{% url 'savory_events:management_booking_calendar' %}"
                   class="bg-blue-600 hover:bg-blue-700 text-white px-3 py-2 rounded-md text-sm">
                    <i class="fas fa-calendar-week mr-2"></i>Calendar View
                </a>
                <a href="{% url 'savory_events:management_booking_conflicts' %}"
                   class="bg-orange-600 hover:bg-orange-700 text-white px-3 py-2 rounded-md text-sm">
                    <i class="fas fa-exclamation-triangle mr-2"></i>Conflicts
                </a>
                <a href="{% url 'savory_events:management_booking_reports' %}"
                   class="bg-purple-600 hover:bg-purple-700 text-white px-3 py-2 rounded-md text-sm">
                    <i class="fas fa-chart-bar mr-2"></i>Reports
                </a>
            </div>
        </div>
    </div>

    <!-- Bulk Actions -->
    <div class="px-6 py-4" x-data="bulkActions()">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <span class="text-sm text-gray-600">Bulk Actions:</span>
                <select x-model="selectedAction" class="border-gray-300 rounded-md text-sm">
                    <option value="">Select Action</option>
                    <option value="update_status">Update Status</option>
                    <option value="update_priority">Update Priority</option>
                    <option value="assign_coordinator">Assign Coordinator</option>
                </select>

                <!-- Status Update Options -->
                <div x-show="selectedAction === 'update_status'" class="flex items-center space-x-2">
                    <select x-model="newStatus" class="border-gray-300 rounded-md text-sm">
                        <option value="">Select Status</option>
                        {% for value, label in status_choices %}
                        <option value="{{ value }}">{{ label }}</option>
                        {% endfor %}
                    </select>
                </div>

                <!-- Priority Update Options -->
                <div x-show="selectedAction === 'update_priority'" class="flex items-center space-x-2">
                    <select x-model="newPriority" class="border-gray-300 rounded-md text-sm">
                        <option value="">Select Priority</option>
                        <option value="low">Low Priority</option>
                        <option value="normal">Normal Priority</option>
                        <option value="high">High Priority</option>
                        <option value="urgent">Urgent</option>
                    </select>
                </div>

                <button @click="executeBulkAction()"
                        :disabled="selectedBookings.length === 0 || !selectedAction"
                        class="bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white px-4 py-2 rounded-md text-sm">
                    Apply to Selected (<span x-text="selectedBookings.length"></span>)
                </button>
            </div>

            <div class="flex items-center space-x-2">
                <button @click="selectAll()" class="text-blue-600 hover:text-blue-800 text-sm">
                    Select All
                </button>
                <button @click="clearSelection()" class="text-gray-600 hover:text-gray-800 text-sm">
                    Clear
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Bookings List -->
<div class="bg-white shadow rounded-lg" x-data="bulkActions()">
    <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex items-center justify-between">
            <h3 class="text-lg font-medium text-gray-900">
                Bookings ({{ bookings.paginator.count }} total)
            </h3>
            <a href="{% url 'savory_events:booking_wizard' %}"
               class="bg-deep-olive text-white px-4 py-2 rounded-md hover:bg-burnt-orange transition-colors duration-200">
                <i class="fas fa-plus mr-2"></i>
                New Booking
            </a>
        </div>
    </div>
    
    <div class="overflow-hidden">
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            <input type="checkbox" @change="toggleAll($event)"
                                   class="rounded border-gray-300 text-blue-600 shadow-sm">
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Event Details</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date & Time</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    {% for booking in bookings %}
                    <tr class="hover:bg-gray-50" x-data="{ showActions: false }">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <input type="checkbox" :value="{{ booking.id }}"
                                   @change="toggleBooking({{ booking.id }}, $event.target.checked)"
                                   class="rounded border-gray-300 text-blue-600 shadow-sm">
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center">
                                <div class="h-10 w-10 flex-shrink-0">
                                    <div class="h-10 w-10 rounded-full bg-deep-olive flex items-center justify-center">
                                        <span class="text-white font-medium text-sm">
                                            {{ booking.customer.name|first|upper }}
                                        </span>
                                    </div>
                                </div>
                                <div class="ml-4">
                                    <div class="text-sm font-medium text-gray-900">{{ booking.customer.name }}</div>
                                    <div class="text-sm text-gray-500">{{ booking.customer.email }}</div>
                                    <div class="text-sm text-gray-500">{{ booking.customer.phone }}</div>
                                </div>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-gray-900">{{ booking.get_event_type_display }}</div>
                            <div class="text-sm text-gray-500">{{ booking.guest_count }} guests</div>
                            <div class="text-sm text-gray-500 truncate max-w-xs" title="{{ booking.venue_address }}">
                                {{ booking.venue_address|truncatechars:40 }}
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-gray-900">{{ booking.event_date|date:"M d, Y" }}</div>
                            <div class="text-sm text-gray-500">{{ booking.event_date|time:"h:i A" }}</div>
                            <div class="text-sm text-gray-400">Created: {{ booking.created_at|date:"M d" }}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full 
                                {% if booking.status == 'pending' %}bg-yellow-100 text-yellow-800
                                {% elif booking.status == 'confirmed' %}bg-blue-100 text-blue-800
                                {% elif booking.status == 'partial_paid' %}bg-orange-100 text-orange-800
                                {% elif booking.status == 'fully_paid' %}bg-green-100 text-green-800
                                {% elif booking.status == 'completed' %}bg-gray-100 text-gray-800
                                {% else %}bg-red-100 text-red-800{% endif %}">
                                {{ booking.get_status_display }}
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm font-medium text-gray-900">${{ booking.total_amount|floatformat:2 }}</div>
                            <div class="text-sm text-gray-500">
                                Paid: ${{ booking.get_total_paid|floatformat:2 }}
                            </div>
                            {% if booking.get_remaining_balance > 0 %}
                            <div class="text-sm text-red-600">
                                Due: ${{ booking.get_remaining_balance|floatformat:2 }}
                            </div>
                            {% endif %}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                            <div class="relative" x-data="{ open: false }">
                                <button @click="open = !open" 
                                        class="text-deep-olive hover:text-burnt-orange">
                                    <i class="fas fa-ellipsis-v"></i>
                                </button>
                                
                                <div x-show="open" 
                                     x-transition:enter="transition ease-out duration-100"
                                     x-transition:enter-start="transform opacity-0 scale-95"
                                     x-transition:enter-end="transform opacity-100 scale-100"
                                     x-transition:leave="transition ease-in duration-75"
                                     x-transition:leave-start="transform opacity-100 scale-100"
                                     x-transition:leave-end="transform opacity-0 scale-95"
                                     @click.away="open = false"
                                     class="origin-top-right absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 z-10">
                                    <div class="py-1">
                                        <a href="{% url 'savory_events:management_booking_detail' booking.id %}" 
                                           class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                            <i class="fas fa-eye mr-2"></i>View Details
                                        </a>
                                        <a href="{% url 'savory_events:management_customer_detail' booking.customer.id %}" 
                                           class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                            <i class="fas fa-user mr-2"></i>View Customer
                                        </a>
                                        {% if booking.get_remaining_balance > 0 %}
                                        <button onclick="addPayment({{ booking.id }})" 
                                                class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                            <i class="fas fa-credit-card mr-2"></i>Add Payment
                                        </button>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="6" class="px-6 py-8 text-center">
                            <div class="text-gray-500">
                                <i class="fas fa-calendar-alt text-4xl mb-4 text-gray-300"></i>
                                <p class="text-lg font-medium">No bookings found</p>
                                <p class="text-sm">Try adjusting your filters or create a new booking.</p>
                                <a href="{% url 'savory_events:booking_wizard' %}" 
                                   class="inline-flex items-center mt-4 px-4 py-2 bg-deep-olive text-white rounded-md hover:bg-burnt-orange transition-colors duration-200">
                                    <i class="fas fa-plus mr-2"></i>
                                    Create New Booking
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>

    <!-- Pagination -->
    {% if bookings.has_other_pages %}
    <div class="bg-white px-4 py-3 border-t border-gray-200 sm:px-6">
        <div class="flex-1 flex justify-between sm:hidden">
            {% if bookings.has_previous %}
                <a href="?page={{ bookings.previous_page_number }}&{{ request.GET.urlencode }}" 
                   class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    Previous
                </a>
            {% endif %}
            
            {% if bookings.has_next %}
                <a href="?page={{ bookings.next_page_number }}&{{ request.GET.urlencode }}" 
                   class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    Next
                </a>
            {% endif %}
        </div>
        
        <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
            <div>
                <p class="text-sm text-gray-700">
                    Showing
                    <span class="font-medium">{{ bookings.start_index }}</span>
                    to
                    <span class="font-medium">{{ bookings.end_index }}</span>
                    of
                    <span class="font-medium">{{ bookings.paginator.count }}</span>
                    results
                </p>
            </div>
            
            <div>
                <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                    {% if bookings.has_previous %}
                        <a href="?page={{ bookings.previous_page_number }}&{{ request.GET.urlencode }}" 
                           class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                            <span class="sr-only">Previous</span>
                            <i class="fas fa-chevron-left"></i>
                        </a>
                    {% endif %}
                    
                    {% for num in bookings.paginator.page_range %}
                        {% if bookings.number == num %}
                            <span class="bg-deep-olive border-deep-olive text-white relative inline-flex items-center px-4 py-2 border text-sm font-medium">
                                {{ num }}
                            </span>
                        {% else %}
                            <a href="?page={{ num }}&{{ request.GET.urlencode }}" 
                               class="bg-white border-gray-300 text-gray-500 hover:bg-gray-50 relative inline-flex items-center px-4 py-2 border text-sm font-medium">
                                {{ num }}
                            </a>
                        {% endif %}
                    {% endfor %}
                    
                    {% if bookings.has_next %}
                        <a href="?page={{ bookings.next_page_number }}&{{ request.GET.urlencode }}" 
                           class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                            <span class="sr-only">Next</span>
                            <i class="fas fa-chevron-right"></i>
                        </a>
                    {% endif %}
                </nav>
            </div>
        </div>
    </div>
    {% endif %}
</div>

{% csrf_token %}
{% endblock %}

{% block extra_scripts %}
<script src="https://unpkg.com/htmx.org@1.9.10"></script>
<script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>

<script>
function addPayment(bookingId) {
    // This would open a modal for adding payment
    // For now, redirect to booking detail page
    window.location.href = `/management/bookings/${bookingId}/`;
}

function bulkActions() {
    return {
        selectedBookings: [],
        selectedAction: '',
        newStatus: '',
        newPriority: '',

        toggleBooking(bookingId, checked) {
            if (checked) {
                if (!this.selectedBookings.includes(bookingId)) {
                    this.selectedBookings.push(bookingId);
                }
            } else {
                this.selectedBookings = this.selectedBookings.filter(id => id !== bookingId);
            }
        },

        toggleAll(event) {
            const checkboxes = document.querySelectorAll('tbody input[type="checkbox"]');
            checkboxes.forEach(checkbox => {
                checkbox.checked = event.target.checked;
                const bookingId = parseInt(checkbox.value);
                if (event.target.checked) {
                    if (!this.selectedBookings.includes(bookingId)) {
                        this.selectedBookings.push(bookingId);
                    }
                } else {
                    this.selectedBookings = this.selectedBookings.filter(id => id !== bookingId);
                }
            });
        },

        selectAll() {
            const checkboxes = document.querySelectorAll('tbody input[type="checkbox"]');
            checkboxes.forEach(checkbox => {
                checkbox.checked = true;
                const bookingId = parseInt(checkbox.value);
                if (!this.selectedBookings.includes(bookingId)) {
                    this.selectedBookings.push(bookingId);
                }
            });
        },

        clearSelection() {
            const checkboxes = document.querySelectorAll('input[type="checkbox"]');
            checkboxes.forEach(checkbox => checkbox.checked = false);
            this.selectedBookings = [];
        },

        async executeBulkAction() {
            if (this.selectedBookings.length === 0 || !this.selectedAction) {
                alert('Please select bookings and an action');
                return;
            }

            const formData = new FormData();
            formData.append('csrfmiddlewaretoken', document.querySelector('[name=csrfmiddlewaretoken]').value);
            formData.append('action', this.selectedAction);

            this.selectedBookings.forEach(id => {
                formData.append('booking_ids[]', id);
            });

            if (this.selectedAction === 'update_status' && this.newStatus) {
                formData.append('new_status', this.newStatus);
            } else if (this.selectedAction === 'update_priority' && this.newPriority) {
                formData.append('new_priority', this.newPriority);
            }

            try {
                const response = await fetch('{% url "savory_events:management_booking_bulk_action" %}', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                });

                const result = await response.json();

                if (result.success) {
                    alert(result.message);
                    window.location.reload();
                } else {
                    alert('Error: ' + result.error);
                }
            } catch (error) {
                console.error('Error:', error);
                alert('An error occurred while performing the bulk action');
            }
        }
    }
}
</script>
{% endblock %}
