from django.db import models
from django.contrib.auth.models import User
from django.core.validators import MinValueValidator, MaxValueValidator, EmailValidator
from django.core.exceptions import ValidationError
from django.utils import timezone
from decimal import Decimal
from django.db.models.signals import post_save, post_delete
from django.dispatch import receiver
import re


class Customer(models.Model):
    """Customer model for storing customer information."""
    name = models.CharField(max_length=100, help_text="Customer's full name")
    email = models.EmailField(validators=[EmailValidator()], help_text="Customer's email address")
    phone = models.CharField(max_length=20, help_text="Customer's phone number")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        verbose_name = "Customer"
        verbose_name_plural = "Customers"
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.name} ({self.email})"
    
    def clean(self):
        """Custom validation for Customer model."""
        super().clean()
        
        # Validate name length
        if self.name and len(self.name.strip()) < 2:
            raise ValidationError({'name': 'Name must be at least 2 characters long.'})
        
        # Validate phone number format
        if self.phone:
            phone_digits = re.sub(r'\D', '', self.phone)
            if len(phone_digits) < 10:
                raise ValidationError({'phone': 'Phone number must be at least 10 digits.'})
            if not re.match(r'^[\d\s\-\+\(\)]+$', self.phone):
                raise ValidationError({'phone': 'Please enter a valid phone number.'})
    
    def save(self, *args, **kwargs):
        self.full_clean()
        super().save(*args, **kwargs)


class MenuItem(models.Model):
    """Menu item model for catering services."""
    CATEGORY_CHOICES = [
        ('appetizer', 'Appetizers'),
        ('main', 'Main Dishes'),
        ('dessert', 'Desserts'),
        ('drink', 'Beverages'),
    ]
    
    name = models.CharField(max_length=100, help_text="Menu item name")
    description = models.TextField(help_text="Description of the menu item")
    category = models.CharField(max_length=20, choices=CATEGORY_CHOICES, help_text="Menu category")
    price_per_person = models.DecimalField(
        max_digits=8, 
        decimal_places=2, 
        validators=[MinValueValidator(Decimal('0.01'))],
        help_text="Price per person in dollars"
    )
    image = models.ImageField(upload_to='menu_items/', blank=True, null=True, help_text="Menu item image")
    is_available = models.BooleanField(default=True, help_text="Is this item currently available?")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        verbose_name = "Menu Item"
        verbose_name_plural = "Menu Items"
        ordering = ['category', 'name']
    
    def __str__(self):
        return f"{self.name} ({self.get_category_display()}) - ${self.price_per_person}"
    
    def clean(self):
        """Custom validation for MenuItem model."""
        super().clean()
        
        if self.price_per_person and self.price_per_person <= 0:
            raise ValidationError({'price_per_person': 'Price must be greater than 0.'})
    
    def calculate_total_price(self, guest_count):
        """Calculate total price for given number of guests."""
        return self.price_per_person * guest_count


class SoundSystemService(models.Model):
    """Sound system service model."""
    PACKAGE_CHOICES = [
        ('dj', 'DJ Package'),
        ('microphones', 'Microphones Only'),
        ('full_audio', 'Full Audio Set'),
    ]
    
    name = models.CharField(max_length=100, help_text="Service name")
    package_type = models.CharField(max_length=20, choices=PACKAGE_CHOICES, help_text="Package type")
    description = models.TextField(help_text="Service description")
    price = models.DecimalField(
        max_digits=8, 
        decimal_places=2, 
        validators=[MinValueValidator(Decimal('0.01'))],
        help_text="Service price"
    )
    is_available = models.BooleanField(default=True, help_text="Is this service currently available?")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        verbose_name = "Sound System Service"
        verbose_name_plural = "Sound System Services"
        ordering = ['package_type', 'name']
    
    def __str__(self):
        return f"{self.name} ({self.get_package_type_display()}) - ${self.price}"
    
    def clean(self):
        """Custom validation for SoundSystemService model."""
        super().clean()
        
        if self.price and self.price <= 0:
            raise ValidationError({'price': 'Price must be greater than 0.'})


class Booking(models.Model):
    """Booking model for catering events."""
    BOOKING_STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('confirmed', 'Confirmed'),
        ('partial_paid', 'Partially Paid'),
        ('fully_paid', 'Fully Paid'),
        ('completed', 'Completed'),
        ('cancelled', 'Cancelled'),
    ]
    
    EVENT_TYPE_CHOICES = [
        ('wedding', 'Wedding'),
        ('corporate', 'Corporate Event'),
        ('birthday', 'Birthday Party'),
        ('anniversary', 'Anniversary'),
        ('other', 'Other'),
    ]

    PRIORITY_CHOICES = [
        ('low', 'Low Priority'),
        ('normal', 'Normal Priority'),
        ('high', 'High Priority'),
        ('urgent', 'Urgent'),
    ]
    
    # Customer information
    customer = models.ForeignKey(Customer, on_delete=models.CASCADE, related_name='bookings')
    
    # Event details
    event_date = models.DateTimeField(help_text="Date and time of the event")
    event_type = models.CharField(max_length=20, choices=EVENT_TYPE_CHOICES, help_text="Type of event")
    guest_count = models.PositiveIntegerField(
        validators=[MinValueValidator(1), MaxValueValidator(1000)],
        help_text="Number of guests"
    )
    venue_address = models.TextField(help_text="Venue address")
    special_requests = models.TextField(blank=True, help_text="Special requests or dietary restrictions")
    
    # Booking status and pricing
    status = models.CharField(max_length=20, choices=BOOKING_STATUS_CHOICES, default='pending')
    priority = models.CharField(max_length=10, choices=PRIORITY_CHOICES, default='normal')
    total_amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=Decimal('0.00'),
        help_text="Total booking amount"
    )
    discount_amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=Decimal('0.00'),
        help_text="Discount applied to booking"
    )

    # Admin fields
    assigned_coordinator = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='coordinated_bookings',
        help_text="Staff member assigned as coordinator"
    )
    internal_notes = models.TextField(
        blank=True,
        help_text="Internal notes visible only to staff"
    )
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        verbose_name = "Booking"
        verbose_name_plural = "Bookings"
        ordering = ['-event_date']
    
    def __str__(self):
        return f"{self.customer.name} - {self.get_event_type_display()} on {self.event_date.strftime('%Y-%m-%d')}"
    
    def clean(self):
        """Custom validation for Booking model."""
        super().clean()
        
        # Validate event date is in the future
        if self.event_date and self.event_date <= timezone.now():
            raise ValidationError({'event_date': 'Event date must be in the future.'})
        
        # Validate guest count
        if self.guest_count and (self.guest_count < 1 or self.guest_count > 1000):
            raise ValidationError({'guest_count': 'Guest count must be between 1 and 1000.'})
    
    def calculate_total_amount(self):
        """Calculate total booking amount including menu items and sound system."""
        total = Decimal('0.00')
        
        # Calculate menu items total
        for booking_item in self.menu_items.all():
            total += booking_item.menu_item.price_per_person * self.guest_count * booking_item.quantity
        
        # Add sound system cost
        if hasattr(self, 'sound_system_booking') and self.sound_system_booking:
            total += self.sound_system_booking.sound_system.price
        
        return total
    
    def update_total_amount(self):
        """Update and save the total amount."""
        self.total_amount = self.calculate_total_amount()
        self.save(update_fields=['total_amount'])
    
    def get_total_paid(self):
        """Get total amount paid for this booking."""
        return self.payments.aggregate(
            total=models.Sum('amount')
        )['total'] or Decimal('0.00')
    
    def get_remaining_balance(self):
        """Get remaining balance for this booking."""
        return self.total_amount - self.get_total_paid()
    
    def get_deposit_amount(self):
        """Get required deposit amount (50% of total)."""
        return self.total_amount * Decimal('0.5')
    
    def is_deposit_paid(self):
        """Check if deposit is paid."""
        return self.get_total_paid() >= self.get_deposit_amount()
    
    def is_fully_paid(self):
        """Check if booking is fully paid."""
        return self.get_remaining_balance() <= Decimal('0.00')

    def get_final_amount(self):
        """Get final amount after discount."""
        return self.total_amount - self.discount_amount

    def get_priority_display_class(self):
        """Get CSS class for priority display."""
        priority_classes = {
            'low': 'text-gray-600',
            'normal': 'text-blue-600',
            'high': 'text-orange-600',
            'urgent': 'text-red-600',
        }
        return priority_classes.get(self.priority, 'text-gray-600')

    def get_status_display_class(self):
        """Get CSS class for status display."""
        status_classes = {
            'pending': 'bg-yellow-100 text-yellow-800',
            'confirmed': 'bg-blue-100 text-blue-800',
            'partial_paid': 'bg-orange-100 text-orange-800',
            'fully_paid': 'bg-green-100 text-green-800',
            'completed': 'bg-gray-100 text-gray-800',
            'cancelled': 'bg-red-100 text-red-800',
        }
        return status_classes.get(self.status, 'bg-gray-100 text-gray-800')

    def can_be_cancelled(self):
        """Check if booking can be cancelled."""
        return self.status not in ['completed', 'cancelled']

    def can_be_modified(self):
        """Check if booking can be modified."""
        return self.status in ['pending', 'confirmed']

    def get_days_until_event(self):
        """Get number of days until the event."""
        if self.event_date:
            delta = self.event_date.date() - timezone.now().date()
            return delta.days
        return None

    def is_upcoming(self):
        """Check if event is within next 7 days."""
        days_until = self.get_days_until_event()
        return days_until is not None and 0 <= days_until <= 7

    def get_completion_percentage(self):
        """Get booking completion percentage based on status."""
        completion_map = {
            'pending': 10,
            'confirmed': 30,
            'partial_paid': 60,
            'fully_paid': 80,
            'completed': 100,
            'cancelled': 0,
        }
        return completion_map.get(self.status, 0)


class BookingMenuItem(models.Model):
    """Many-to-many relationship between Booking and MenuItem."""
    booking = models.ForeignKey(Booking, on_delete=models.CASCADE, related_name='menu_items')
    menu_item = models.ForeignKey(MenuItem, on_delete=models.CASCADE, related_name='bookings')
    quantity = models.PositiveIntegerField(
        default=1,
        validators=[MinValueValidator(1)],
        help_text="Quantity of this menu item"
    )
    
    class Meta:
        verbose_name = "Booking Menu Item"
        verbose_name_plural = "Booking Menu Items"
        unique_together = ['booking', 'menu_item']
    
    def __str__(self):
        return f"{self.booking.customer.name} - {self.menu_item.name} (x{self.quantity})"
    
    def get_total_price(self):
        """Get total price for this menu item in this booking."""
        return self.menu_item.price_per_person * self.booking.guest_count * self.quantity


class BookingSoundSystem(models.Model):
    """One-to-one relationship between Booking and SoundSystemService."""
    booking = models.OneToOneField(
        Booking, 
        on_delete=models.CASCADE, 
        related_name='sound_system_booking'
    )
    sound_system = models.ForeignKey(
        SoundSystemService, 
        on_delete=models.CASCADE, 
        related_name='bookings'
    )
    
    class Meta:
        verbose_name = "Booking Sound System"
        verbose_name_plural = "Booking Sound Systems"
    
    def __str__(self):
        return f"{self.booking.customer.name} - {self.sound_system.name}"


class Payment(models.Model):
    """Payment model for tracking booking payments."""
    PAYMENT_TYPE_CHOICES = [
        ('deposit', 'Deposit (50%)'),
        ('balance', 'Balance (50%)'),
        ('full', 'Full Payment'),
    ]
    
    booking = models.ForeignKey(Booking, on_delete=models.CASCADE, related_name='payments')
    amount = models.DecimalField(
        max_digits=10, 
        decimal_places=2, 
        validators=[MinValueValidator(Decimal('0.01'))],
        help_text="Payment amount"
    )
    payment_type = models.CharField(max_length=20, choices=PAYMENT_TYPE_CHOICES, help_text="Type of payment")
    payment_date = models.DateTimeField(auto_now_add=True, help_text="Date payment was received")
    notes = models.TextField(blank=True, help_text="Payment notes (method, reference, etc.)")
    
    class Meta:
        verbose_name = "Payment"
        verbose_name_plural = "Payments"
        ordering = ['-payment_date']
    
    def __str__(self):
        return f"{self.booking.customer.name} - ${self.amount} ({self.get_payment_type_display()})"
    
    def clean(self):
        """Custom validation for Payment model."""
        super().clean()
        
        if self.amount and self.amount <= 0:
            raise ValidationError({'amount': 'Payment amount must be greater than 0.'})
        
        if self.booking and self.amount and self.amount > self.booking.total_amount:
            raise ValidationError({'amount': 'Payment amount cannot exceed booking total.'})
    
    def save(self, *args, **kwargs):
        self.full_clean()
        super().save(*args, **kwargs)
        
        # Update booking status based on payment
        if self.booking:
            total_paid = self.booking.get_total_paid()
            if self.booking.is_fully_paid():
                self.booking.status = 'fully_paid'
            elif self.booking.is_deposit_paid():
                self.booking.status = 'partial_paid'
            else:
                self.booking.status = 'confirmed'
            self.booking.save(update_fields=['status'])


# Signal handlers to update booking totals when menu items change
class ChatConversation(models.Model):
    """Model for storing chat conversations."""
    session_id = models.CharField(max_length=100, unique=True, help_text="Unique session identifier")
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        help_text="Associated user (null for anonymous users)"
    )
    customer = models.ForeignKey(
        Customer,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        help_text="Associated customer if identified"
    )
    is_active = models.BooleanField(default=True, help_text="Whether the conversation is active")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Chat Conversation"
        verbose_name_plural = "Chat Conversations"
        ordering = ['-updated_at']

    def __str__(self):
        user_info = self.user.username if self.user else f"Anonymous ({self.session_id[:8]})"
        return f"Chat with {user_info} - {self.created_at.strftime('%Y-%m-%d %H:%M')}"


class ChatMessage(models.Model):
    """Model for storing individual chat messages."""
    MESSAGE_TYPE_CHOICES = [
        ('user', 'User Message'),
        ('bot', 'Bot Response'),
        ('system', 'System Message'),
        ('escalation', 'Escalated to Human'),
    ]

    conversation = models.ForeignKey(
        ChatConversation,
        on_delete=models.CASCADE,
        related_name='messages',
        help_text="Associated conversation"
    )
    message_type = models.CharField(
        max_length=20,
        choices=MESSAGE_TYPE_CHOICES,
        help_text="Type of message"
    )
    content = models.TextField(help_text="Message content")
    metadata = models.TextField(
        blank=True,
        default='{}',
        help_text="Additional metadata as JSON string (e.g., API response data, context)"
    )
    is_visible = models.BooleanField(default=True, help_text="Whether message is visible to user")
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name = "Chat Message"
        verbose_name_plural = "Chat Messages"
        ordering = ['created_at']

    def __str__(self):
        return f"{self.get_message_type_display()}: {self.content[:50]}..."


class ChatEscalation(models.Model):
    """Model for tracking escalated chat conversations."""
    ESCALATION_STATUS_CHOICES = [
        ('pending', 'Pending Review'),
        ('assigned', 'Assigned to Staff'),
        ('in_progress', 'In Progress'),
        ('resolved', 'Resolved'),
        ('closed', 'Closed'),
    ]

    conversation = models.OneToOneField(
        ChatConversation,
        on_delete=models.CASCADE,
        related_name='escalation',
        help_text="Associated conversation"
    )
    reason = models.TextField(help_text="Reason for escalation")
    assigned_staff = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='assigned_escalations',
        help_text="Staff member assigned to handle escalation"
    )
    status = models.CharField(
        max_length=20,
        choices=ESCALATION_STATUS_CHOICES,
        default='pending',
        help_text="Current status of escalation"
    )
    priority = models.CharField(
        max_length=10,
        choices=Booking.PRIORITY_CHOICES,
        default='normal',
        help_text="Priority level"
    )
    resolution_notes = models.TextField(
        blank=True,
        help_text="Notes about resolution"
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    resolved_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        verbose_name = "Chat Escalation"
        verbose_name_plural = "Chat Escalations"
        ordering = ['-created_at']

    def __str__(self):
        return f"Escalation for {self.conversation} - {self.get_status_display()}"


# Signal handlers to update booking totals when menu items change
from django.db.models.signals import post_save, post_delete
from django.dispatch import receiver

@receiver(post_save, sender=BookingMenuItem)
@receiver(post_delete, sender=BookingMenuItem)
@receiver(post_save, sender=BookingSoundSystem)
@receiver(post_delete, sender=BookingSoundSystem)
def update_booking_total(sender, instance, **kwargs):
    """Update booking total when menu items or sound system changes."""
    if hasattr(instance, 'booking'):
        instance.booking.update_total_amount()


class BookingNote(models.Model):
    """Internal notes and comments for bookings."""
    PRIORITY_CHOICES = [
        ('low', 'Low'),
        ('normal', 'Normal'),
        ('high', 'High'),
        ('urgent', 'Urgent'),
    ]

    booking = models.ForeignKey(Booking, on_delete=models.CASCADE, related_name='notes')
    staff_user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='booking_notes')
    note = models.TextField(help_text="Internal note or comment")
    priority = models.CharField(max_length=10, choices=PRIORITY_CHOICES, default='normal')
    is_internal = models.BooleanField(default=True, help_text="Internal note (not visible to customer)")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-created_at']

    def __str__(self):
        return f"Note for Booking #{self.booking.id} by {self.staff_user.username}"


class BookingStatusHistory(models.Model):
    """Track booking status changes for audit trail."""
    booking = models.ForeignKey(Booking, on_delete=models.CASCADE, related_name='status_history')
    old_status = models.CharField(max_length=20, choices=Booking.BOOKING_STATUS_CHOICES)
    new_status = models.CharField(max_length=20, choices=Booking.BOOKING_STATUS_CHOICES)
    changed_by = models.ForeignKey(User, on_delete=models.CASCADE, related_name='status_changes')
    reason = models.TextField(blank=True, help_text="Reason for status change")
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['-created_at']
        verbose_name_plural = "Booking status histories"

    def __str__(self):
        return f"Booking #{self.booking.id}: {self.old_status} → {self.new_status}"


class BookingReminder(models.Model):
    """Reminders and follow-up tasks for bookings."""
    REMINDER_TYPE_CHOICES = [
        ('follow_up', 'Follow Up'),
        ('confirmation', 'Confirmation Call'),
        ('payment', 'Payment Reminder'),
        ('preparation', 'Event Preparation'),
        ('delivery', 'Delivery/Setup'),
        ('custom', 'Custom Task'),
    ]

    booking = models.ForeignKey(Booking, on_delete=models.CASCADE, related_name='reminders')
    assigned_to = models.ForeignKey(User, on_delete=models.CASCADE, related_name='assigned_reminders')
    reminder_type = models.CharField(max_length=20, choices=REMINDER_TYPE_CHOICES)
    title = models.CharField(max_length=200, help_text="Reminder title")
    description = models.TextField(blank=True, help_text="Detailed description")
    due_date = models.DateTimeField(help_text="When this reminder is due")
    is_completed = models.BooleanField(default=False)
    completed_at = models.DateTimeField(null=True, blank=True)
    completed_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='completed_reminders'
    )
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['due_date']

    def __str__(self):
        return f"{self.title} - Booking #{self.booking.id}"

    def is_overdue(self):
        """Check if reminder is overdue."""
        return not self.is_completed and self.due_date < timezone.now()


class BookingStaffAssignment(models.Model):
    """Staff assignments for bookings."""
    ROLE_CHOICES = [
        ('coordinator', 'Event Coordinator'),
        ('chef', 'Head Chef'),
        ('server', 'Server'),
        ('setup', 'Setup Crew'),
        ('manager', 'Event Manager'),
    ]

    booking = models.ForeignKey(Booking, on_delete=models.CASCADE, related_name='staff_assignments')
    staff_user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='booking_assignments')
    role = models.CharField(max_length=20, choices=ROLE_CHOICES)
    notes = models.TextField(blank=True, help_text="Special instructions for this assignment")
    assigned_at = models.DateTimeField(auto_now_add=True)
    assigned_by = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='staff_assignments_made'
    )

    class Meta:
        unique_together = ['booking', 'staff_user', 'role']

    def __str__(self):
        return f"{self.staff_user.get_full_name()} - {self.get_role_display()} for Booking #{self.booking.id}"
