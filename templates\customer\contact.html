{% extends 'customer/base.html' %}

{% block title %}Contact & Help - BonAppetit Catering{% endblock %}

{% block page_title %}Contact & Help{% endblock %}

{% block page_subtitle %}Get in touch with our team or find answers to your questions{% endblock %}

{% block content %}
<div class="py-8 space-y-8">
    
    <!-- Quick Actions -->
    <div class="bg-white rounded-lg shadow-md p-6">
        <div class="flex flex-col sm:flex-row items-center justify-between gap-4">
            <div>
                <h2 class="text-xl font-semibold text-charcoal">Need Help?</h2>
                <p class="text-gray-600">We're here to assist you with your catering needs</p>
            </div>
            <div class="flex gap-3">
                <a href="{% url 'savory_events:booking_wizard' %}" 
                   class="bg-burnt-orange hover:bg-opacity-80 text-white px-4 py-2 rounded-md transition-colors">
                    <i class="fas fa-magic mr-2"></i>Start Booking
                </a>
                <a href="{% url 'savory_events:menu' %}" 
                   class="border border-burnt-orange text-burnt-orange hover:bg-burnt-orange hover:text-white px-4 py-2 rounded-md transition-colors">
                    <i class="fas fa-utensils mr-2"></i>View Menu
                </a>
            </div>
        </div>
    </div>
    
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        
        <!-- Contact Information -->
        <div class="space-y-6">
            
            <!-- Contact Details -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <h3 class="text-xl font-semibold text-charcoal mb-6">Get in Touch</h3>
                
                <div class="space-y-4">
                    <div class="flex items-center">
                        <div class="w-10 h-10 bg-burnt-orange rounded-full flex items-center justify-center mr-4">
                            <i class="fas fa-phone text-white"></i>
                        </div>
                        <div>
                            <p class="font-medium text-charcoal">Phone</p>
                            <p class="text-gray-600">(*************</p>
                        </div>
                    </div>
                    
                    <div class="flex items-center">
                        <div class="w-10 h-10 bg-sage-green rounded-full flex items-center justify-center mr-4">
                            <i class="fas fa-envelope text-white"></i>
                        </div>
                        <div>
                            <p class="font-medium text-charcoal">Email</p>
                            <p class="text-gray-600"><EMAIL></p>
                        </div>
                    </div>
                    
                    <div class="flex items-center">
                        <div class="w-10 h-10 bg-deep-olive rounded-full flex items-center justify-center mr-4">
                            <i class="fas fa-map-marker-alt text-white"></i>
                        </div>
                        <div>
                            <p class="font-medium text-charcoal">Address</p>
                            <p class="text-gray-600">123 Culinary Street<br>Foodie City, FC 12345</p>
                        </div>
                    </div>
                    
                    <div class="flex items-center">
                        <div class="w-10 h-10 bg-burnt-orange rounded-full flex items-center justify-center mr-4">
                            <i class="fas fa-clock text-white"></i>
                        </div>
                        <div>
                            <p class="font-medium text-charcoal">Business Hours</p>
                            <p class="text-gray-600">Mon-Fri: 8:00 AM - 6:00 PM<br>Sat-Sun: 9:00 AM - 4:00 PM</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Emergency Contact -->
            <div class="bg-red-50 border border-red-200 rounded-lg p-6">
                <h3 class="text-lg font-semibold text-red-800 mb-3">
                    <i class="fas fa-exclamation-triangle mr-2"></i>Event Day Emergency
                </h3>
                <p class="text-red-700 mb-3">
                    For urgent matters on your event day, please call our emergency line:
                </p>
                <p class="text-xl font-bold text-red-800">(555) 999-HELP</p>
                <p class="text-sm text-red-600 mt-2">Available 24/7 during event periods</p>
            </div>
        </div>
        
        <!-- FAQ and Support -->
        <div class="space-y-6">
            
            <!-- Frequently Asked Questions -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <h3 class="text-xl font-semibold text-charcoal mb-6">Frequently Asked Questions</h3>
                
                <div x-data="{ openFaq: null }" class="space-y-4">
                    
                    <!-- FAQ 1 -->
                    <div class="border border-gray-200 rounded-lg">
                        <button @click="openFaq = openFaq === 1 ? null : 1" 
                                class="w-full px-4 py-3 text-left flex items-center justify-between hover:bg-gray-50">
                            <span class="font-medium text-charcoal">How far in advance should I book?</span>
                            <i :class="openFaq === 1 ? 'fas fa-chevron-up' : 'fas fa-chevron-down'" 
                               class="text-gray-400"></i>
                        </button>
                        <div x-show="openFaq === 1" x-transition class="px-4 pb-3">
                            <p class="text-gray-600">We recommend booking at least 2-3 weeks in advance for most events. For large events or during peak seasons (holidays, wedding season), we suggest booking 4-6 weeks ahead to ensure availability.</p>
                        </div>
                    </div>
                    
                    <!-- FAQ 2 -->
                    <div class="border border-gray-200 rounded-lg">
                        <button @click="openFaq = openFaq === 2 ? null : 2" 
                                class="w-full px-4 py-3 text-left flex items-center justify-between hover:bg-gray-50">
                            <span class="font-medium text-charcoal">Can you accommodate dietary restrictions?</span>
                            <i :class="openFaq === 2 ? 'fas fa-chevron-up' : 'fas fa-chevron-down'" 
                               class="text-gray-400"></i>
                        </button>
                        <div x-show="openFaq === 2" x-transition class="px-4 pb-3">
                            <p class="text-gray-600">Absolutely! We can accommodate various dietary restrictions including vegetarian, vegan, gluten-free, kosher, and specific allergies. Please let us know about any dietary needs when booking.</p>
                        </div>
                    </div>
                    
                    <!-- FAQ 3 -->
                    <div class="border border-gray-200 rounded-lg">
                        <button @click="openFaq = openFaq === 3 ? null : 3" 
                                class="w-full px-4 py-3 text-left flex items-center justify-between hover:bg-gray-50">
                            <span class="font-medium text-charcoal">What's included in your service?</span>
                            <i :class="openFaq === 3 ? 'fas fa-chevron-up' : 'fas fa-chevron-down'" 
                               class="text-gray-400"></i>
                        </button>
                        <div x-show="openFaq === 3" x-transition class="px-4 pb-3">
                            <p class="text-gray-600">Our full-service catering includes menu planning, food preparation, professional staff, setup, service during the event, and cleanup. Additional services like sound systems and special decorations are available upon request.</p>
                        </div>
                    </div>
                    
                    <!-- FAQ 4 -->
                    <div class="border border-gray-200 rounded-lg">
                        <button @click="openFaq = openFaq === 4 ? null : 4" 
                                class="w-full px-4 py-3 text-left flex items-center justify-between hover:bg-gray-50">
                            <span class="font-medium text-charcoal">How do I modify or cancel my booking?</span>
                            <i :class="openFaq === 4 ? 'fas fa-chevron-up' : 'fas fa-chevron-down'" 
                               class="text-gray-400"></i>
                        </button>
                        <div x-show="openFaq === 4" x-transition class="px-4 pb-3">
                            <p class="text-gray-600">You can modify your booking through your customer dashboard or by contacting us directly. Cancellations made 72 hours or more before the event receive a full refund. Please see your booking details for specific terms.</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Support Options -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <h3 class="text-xl font-semibold text-charcoal mb-6">Support Options</h3>
                
                <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    <a href="{% url 'savory_events:customer_bookings' %}" 
                       class="flex items-center p-4 border border-sage-green rounded-lg hover:bg-sage-green hover:text-white transition-colors group">
                        <i class="fas fa-calendar-alt text-2xl mr-4 text-sage-green group-hover:text-white"></i>
                        <div>
                            <p class="font-medium">My Bookings</p>
                            <p class="text-sm opacity-75">View and manage your events</p>
                        </div>
                    </a>
                    
                    <a href="{% url 'savory_events:customer_profile' %}" 
                       class="flex items-center p-4 border border-burnt-orange rounded-lg hover:bg-burnt-orange hover:text-white transition-colors group">
                        <i class="fas fa-user text-2xl mr-4 text-burnt-orange group-hover:text-white"></i>
                        <div>
                            <p class="font-medium">My Profile</p>
                            <p class="text-sm opacity-75">Update account information</p>
                        </div>
                    </a>
                    
                    <a href="{% url 'savory_events:menu' %}" 
                       class="flex items-center p-4 border border-deep-olive rounded-lg hover:bg-deep-olive hover:text-white transition-colors group">
                        <i class="fas fa-utensils text-2xl mr-4 text-deep-olive group-hover:text-white"></i>
                        <div>
                            <p class="font-medium">Browse Menu</p>
                            <p class="text-sm opacity-75">Explore our offerings</p>
                        </div>
                    </a>
                    
                    <a href="{% url 'savory_events:services' %}" 
                       class="flex items-center p-4 border border-sage-green rounded-lg hover:bg-sage-green hover:text-white transition-colors group">
                        <i class="fas fa-concierge-bell text-2xl mr-4 text-sage-green group-hover:text-white"></i>
                        <div>
                            <p class="font-medium">Our Services</p>
                            <p class="text-sm opacity-75">Learn about our offerings</p>
                        </div>
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Live Chat or Contact Form -->
    <div class="bg-gradient-to-r from-burnt-orange to-deep-olive rounded-lg p-8 text-center text-white">
        <h2 class="text-2xl font-bold mb-4">Still Need Help?</h2>
        <p class="text-lg mb-6">Our customer service team is ready to assist you with any questions or concerns.</p>
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <a href="tel:+15551234567" 
               class="bg-white text-burnt-orange hover:bg-gray-100 px-6 py-3 rounded-md font-semibold transition-colors">
                <i class="fas fa-phone mr-2"></i>Call Us Now
            </a>
            <a href="mailto:<EMAIL>" 
               class="border-2 border-white text-white hover:bg-white hover:text-burnt-orange px-6 py-3 rounded-md font-semibold transition-colors">
                <i class="fas fa-envelope mr-2"></i>Send Email
            </a>
        </div>
    </div>
</div>
{% endblock %}
