from django.shortcuts import render, get_object_or_404, redirect
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from django.contrib import messages
from django.contrib.auth import login, authenticate, logout
from django.contrib.auth.decorators import login_required
from django.db import transaction
from django.db import models
from .forms import BookingForm, PaymentForm, MenuSelectionForm, CustomUserCreationForm, CustomAuthenticationForm, ContactForm
from .models import Customer, MenuItem, SoundSystemService, Booking, Payment, BookingMenuItem, BookingSoundSystem
import json

def home(request):
    """Home page view with featured menu items."""
    # Get featured menu items - 2 from each category
    featured_appetizers = MenuItem.objects.filter(category='appetizer', is_available=True)[:2]
    featured_mains = MenuItem.objects.filter(category='main', is_available=True)[:2]
    featured_desserts = MenuItem.objects.filter(category='dessert', is_available=True)[:2]
    
    context = {
        'featured_appetizers': featured_appetizers,
        'featured_mains': featured_mains,
        'featured_desserts': featured_desserts,
    }
    return render(request, 'home.html', context)

def booking(request):
    """Booking form view."""
    # Get available menu items and sound systems
    menu_items = MenuItem.objects.filter(is_available=True).order_by('category', 'name')
    sound_systems = SoundSystemService.objects.filter(is_available=True).order_by('package_type', 'price')

    # Use customer portal layout for authenticated non-staff users
    if request.user.is_authenticated and not request.user.is_staff:
        # Get or create customer record
        try:
            customer = Customer.objects.get(email=request.user.email)
        except Customer.DoesNotExist:
            customer = None

        context = {
            'is_customer': True,
            'customer': customer,
            'menu_items': menu_items,
            'sound_systems': sound_systems
        }
        return render(request, 'customer/booking_form.html', context)
    else:
        context = {
            'menu_items': menu_items,
            'sound_systems': sound_systems
        }
        return render(request, 'booking/booking_form.html', context)

def booking_wizard(request):
    """Step-by-step booking wizard view."""
    menu_items = MenuItem.objects.filter(is_available=True).order_by('category', 'name')

    # Convert to JSON for JavaScript
    menu_items_json = []
    for item in menu_items:
        menu_items_json.append({
            'id': item.id,
            'name': item.name,
            'description': item.description,
            'category': item.category,
            'price': float(item.price_per_person),
            'image': item.image.url if item.image else None
        })

    context = {
        'menu_items': json.dumps(menu_items_json),
    }

    # Use customer portal layout for authenticated non-staff users
    if request.user.is_authenticated and not request.user.is_staff:
        # Get or create customer record
        try:
            customer = Customer.objects.get(email=request.user.email)
        except Customer.DoesNotExist:
            customer = None

        context['is_customer'] = True
        context['customer'] = customer
        return render(request, 'customer/booking_wizard.html', context)
    else:
        return render(request, 'booking/booking_wizard.html', context)

def menu(request):
    """Menu display view."""
    menu_items = MenuItem.objects.filter(is_available=True).order_by('category', 'name')

    # Convert to JSON for JavaScript
    menu_items_json = []
    for item in menu_items:
        menu_items_json.append({
            'id': item.id,
            'name': item.name,
            'description': item.description,
            'category': item.category,
            'price': float(item.price_per_person),
            'image': item.image.url if item.image else None
        })

    context = {
        'menu_items': json.dumps(menu_items_json if menu_items_json else []),
        'categories': MenuItem.CATEGORY_CHOICES,
    }

    # Use customer portal layout for authenticated non-staff users
    if request.user.is_authenticated and not request.user.is_staff:
        context['is_customer'] = True
        return render(request, 'customer/menu.html', context)
    else:
        return render(request, 'menu/menu_list.html', context)

def services(request):
    """Services information view."""
    # Use customer portal layout for authenticated non-staff users
    if request.user.is_authenticated and not request.user.is_staff:
        context = {'is_customer': True}
        return render(request, 'customer/services.html', context)
    else:
        return render(request, 'services.html')

def contact(request):
    """Contact page view with form handling."""
    # Use customer portal layout for authenticated non-staff users
    if request.user.is_authenticated and not request.user.is_staff:
        context = {'is_customer': True}
        return render(request, 'customer/contact.html', context)
    else:
        return render(request, 'contact.html')

@login_required
def admin_dashboard(request):
    """Admin dashboard view."""
    if not request.user.is_staff:
        return redirect('savory_events:login')
    
    # Get comprehensive dashboard statistics
    total_bookings = Booking.objects.count()
    pending_bookings = Booking.objects.filter(status__in=['pending', 'confirmed']).count()
    completed_bookings = Booking.objects.filter(status='completed').count()
    pending_payments = Booking.objects.filter(status__in=['pending', 'confirmed', 'partial_paid']).count()
    
    # Calculate revenue (this month)
    from django.utils import timezone
    from datetime import datetime
    current_month = timezone.now().month
    current_year = timezone.now().year
    monthly_revenue = Booking.objects.filter(
        created_at__month=current_month,
        created_at__year=current_year,
        status__in=['confirmed', 'completed', 'partial_paid']
    ).aggregate(total=models.Sum('total_amount'))['total'] or 0
    
    # Get recent bookings with all details
    recent_bookings = Booking.objects.select_related('customer').order_by('-created_at')[:10]
    
    # Get all bookings for the bookings section
    all_bookings = Booking.objects.select_related('customer').order_by('-created_at')
    
    # Active customers count
    active_customers = Customer.objects.filter(
        bookings__created_at__month=current_month,
        bookings__created_at__year=current_year
    ).distinct().count()
    
    context = {
        'total_bookings': total_bookings,
        'pending_bookings': pending_bookings,
        'completed_bookings': completed_bookings,
        'pending_payments': pending_payments,
        'monthly_revenue': monthly_revenue,
        'active_customers': active_customers,
        'recent_bookings': recent_bookings,
        'all_bookings': all_bookings,
    }
    return render(request, 'admin/dashboard.html', context)

@require_http_methods(["POST"])
def htmx_booking_submit(request):
    """HTMX endpoint for booking form submission."""
    if request.headers.get('HX-Request'):
        form = BookingForm(request.POST)
        if form.is_valid():
            try:
                with transaction.atomic():
                    # Create or get customer
                    customer, created = Customer.objects.get_or_create(
                        email=form.cleaned_data['customer_email'],
                        defaults={
                            'name': form.cleaned_data['customer_name'],
                            'phone': form.cleaned_data['customer_phone'],
                        }
                    )
                    
                    # Update customer info if not created
                    if not created:
                        customer.name = form.cleaned_data['customer_name']
                        customer.phone = form.cleaned_data['customer_phone']
                        customer.save()
                    
                    # Combine date and time for event_date
                    from datetime import datetime
                    from django.utils import timezone
                    event_datetime = datetime.combine(
                        form.cleaned_data['event_date'],
                        form.cleaned_data['event_time']
                    )
                    # Make timezone aware
                    event_datetime = timezone.make_aware(event_datetime)
                    
                    # Create booking
                    booking = Booking.objects.create(
                        customer=customer,
                        event_date=event_datetime,
                        event_type=form.cleaned_data['event_type'],
                        guest_count=form.cleaned_data['guest_count'],
                        venue_address=form.cleaned_data['venue_address'],
                        special_requests=form.cleaned_data.get('special_requests', ''),
                    )
                    
                    # Handle menu item selections
                    selected_menu_items = request.POST.getlist('selected_menu_items')
                    if selected_menu_items:
                        for item_id in selected_menu_items:
                            try:
                                menu_item = MenuItem.objects.get(id=item_id, is_available=True)
                                BookingMenuItem.objects.create(
                                    booking=booking,
                                    menu_item=menu_item,
                                    quantity=1
                                )
                            except MenuItem.DoesNotExist:
                                pass
                    
                    # Handle sound system selection if provided
                    sound_system_type = request.POST.get('sound_system')
                    if sound_system_type and sound_system_type != 'none' and sound_system_type != '':
                        try:
                            sound_system = SoundSystemService.objects.get(
                                package_type=sound_system_type,
                                is_available=True
                            )
                            BookingSoundSystem.objects.create(
                                booking=booking,
                                sound_system=sound_system
                            )
                        except SoundSystemService.DoesNotExist:
                            pass
                    
                    # Update booking total
                    booking.update_total_amount()

                    # Use customer portal template for authenticated non-staff users
                    if request.user.is_authenticated and not request.user.is_staff:
                        template = 'customer/booking_success.html'
                    else:
                        template = 'booking/booking_success.html'

                    return render(request, template, {
                        'booking': booking,
                        'customer': customer
                    })
                    
            except Exception as e:
                # Use customer portal template for authenticated non-staff users
                if request.user.is_authenticated and not request.user.is_staff:
                    template = 'customer/booking_form_errors.html'
                else:
                    template = 'booking/booking_form_errors.html'

                return render(request, template, {
                    'form': form,
                    'errors': {'general': [f'An error occurred: {str(e)}']}
                })
        else:
            # Return form with errors
            # Use customer portal template for authenticated non-staff users
            if request.user.is_authenticated and not request.user.is_staff:
                template = 'customer/booking_form_errors.html'
            else:
                template = 'booking/booking_form_errors.html'

            return render(request, template, {
                'form': form,
                'errors': form.errors
            })
    return JsonResponse({'error': 'Invalid request'}, status=400)

@require_http_methods(["POST"])  
def htmx_menu_update(request):
    """HTMX endpoint for menu selection updates."""
    if request.headers.get('HX-Request'):
        selected_item_ids = request.POST.getlist('selected_items')
        guest_count = int(request.POST.get('guest_count', 1))
        
        if selected_item_ids:
            # Get actual menu items
            selected_items = MenuItem.objects.filter(
                id__in=selected_item_ids,
                is_available=True
            )
            
            # Calculate total
            total = sum(item.price_per_person * guest_count for item in selected_items)
            
            return render(request, 'menu/menu_summary.html', {
                'selected_items': selected_items,
                'total': total,
                'guest_count': guest_count
            })
        else:
            return render(request, 'menu/menu_summary.html', {
                'selected_items': [],
                'total': 0,
                'guest_count': guest_count
            })
    return JsonResponse({'error': 'Invalid request'}, status=400)

@require_http_methods(["POST", "GET"])
def htmx_admin_modal(request):
    """HTMX endpoint for admin modal actions."""
    if request.headers.get('HX-Request') and request.user.is_staff:
        action = request.POST.get('action') or request.GET.get('action', 'payment')
        booking_id = request.POST.get('booking_id') or request.GET.get('booking_id')
        
        if action == 'payment':
            # Payment processing
            if request.method == 'POST':
                form = PaymentForm(request.POST)
                if form.is_valid() and booking_id:
                    try:
                        booking = get_object_or_404(Booking, id=booking_id)
                        
                        # Create payment
                        payment = Payment.objects.create(
                            booking=booking,
                            amount=form.cleaned_data['payment_amount'],
                            payment_type=form.cleaned_data['payment_type'],
                            notes=form.cleaned_data.get('payment_notes', '')
                        )
                        
                        return render(request, 'admin/payment_success.html', {
                            'payment': payment,
                            'booking': booking
                        })
                    except Exception as e:
                        return render(request, 'admin/modal_response.html', {
                            'form': form,
                            'errors': {'general': [f'Error processing payment: {str(e)}']}
                        })
                else:
                    return render(request, 'admin/modal_response.html', {
                        'form': form,
                        'errors': form.errors
                    })
            else:
                # Show payment form
                if booking_id:
                    booking = get_object_or_404(Booking, id=booking_id)
                    form = PaymentForm()
                    return render(request, 'admin/payment_form.html', {
                        'form': form,
                        'booking': booking
                    })
                    
        elif action == 'booking_edit':
            # Booking editing
            if booking_id:
                booking = get_object_or_404(Booking, id=booking_id)
                if request.method == 'POST' and request.POST.get('submit'):
                    # Process booking update
                    booking.status = request.POST.get('status', booking.status)
                    booking.special_requests = request.POST.get('special_requests', booking.special_requests)
                    booking.save()
                    
                    return render(request, 'admin/booking_edit_success.html', {
                        'booking': booking
                    })
                else:
                    # Show booking edit form
                    return render(request, 'admin/booking_edit_form.html', {
                        'booking': booking
                    })
                    
        elif action == 'menu_list':
            # Menu management
            menu_items = MenuItem.objects.all().order_by('category', 'name')
            return render(request, 'admin/menu_list.html', {
                'menu_items': menu_items
            })
            
        elif action == 'payment_overview':
            # Payment overview
            recent_payments = Payment.objects.select_related('booking__customer').order_by('-payment_date')[:10]
            pending_bookings = Booking.objects.filter(status__in=['pending', 'confirmed', 'partial_paid']).select_related('customer')
            
            # Calculate payment statistics
            from django.utils import timezone
            current_month = timezone.now().month
            current_year = timezone.now().year
            
            monthly_payments = Payment.objects.filter(
                payment_date__month=current_month,
                payment_date__year=current_year
            ).aggregate(total=models.Sum('amount'))['total'] or 0
            
            total_outstanding = 0
            for booking in pending_bookings:
                paid_amount = Payment.objects.filter(booking=booking).aggregate(total=models.Sum('amount'))['total'] or 0
                total_outstanding += booking.total_amount - paid_amount
                
            return render(request, 'admin/payment_overview.html', {
                'recent_payments': recent_payments,
                'pending_bookings': pending_bookings,
                'monthly_payments': monthly_payments,
                'total_outstanding': total_outstanding
            })
            
        elif action == 'analytics':
            # Analytics and reports
            from django.utils import timezone
            from datetime import datetime, timedelta
            
            # Calculate various metrics
            total_bookings = Booking.objects.count()
            this_month_bookings = Booking.objects.filter(
                created_at__month=timezone.now().month,
                created_at__year=timezone.now().year
            ).count()
            
            total_revenue = Payment.objects.aggregate(total=models.Sum('amount'))['total'] or 0
            
            # Popular menu items
            popular_items = BookingMenuItem.objects.values('menu_item__name').annotate(
                total_orders=models.Count('menu_item')
            ).order_by('-total_orders')[:5]
            
            # Event type distribution
            event_types = Booking.objects.values('event_type').annotate(
                count=models.Count('event_type')
            ).order_by('-count')
            
            return render(request, 'admin/analytics.html', {
                'total_bookings': total_bookings,
                'this_month_bookings': this_month_bookings,
                'total_revenue': total_revenue,
                'popular_items': popular_items,
                'event_types': event_types
            })
            
    return JsonResponse({'error': 'Unauthorized'}, status=403)


@login_required 
def admin_customers(request):
    """Admin customer management view."""
    if not request.user.is_staff:
        return redirect('savory_events:login')
    
    # Get all customers with booking statistics
    customers = Customer.objects.all().annotate(
        total_bookings=models.Count('bookings'),
        total_spent=models.Sum('bookings__total_amount')
    ).order_by('-created_at')
    
    # Filter functionality
    search = request.GET.get('search', '')
    if search:
        customers = customers.filter(
            models.Q(name__icontains=search) |
            models.Q(email__icontains=search) |
            models.Q(phone__icontains=search)
        )
    
    context = {
        'customers': customers,
        'search': search,
    }
    return render(request, 'admin/customers.html', context)


@login_required
def admin_bookings(request):
    """Admin booking management view."""
    if not request.user.is_staff:
        return redirect('savory_events:login')
    
    # Get all bookings with related data
    bookings = Booking.objects.select_related('customer').order_by('-created_at')
    
    # Filter functionality
    status = request.GET.get('status', '')
    search = request.GET.get('search', '')
    
    if status:
        bookings = bookings.filter(status=status)
    
    if search:
        bookings = bookings.filter(
            models.Q(customer__name__icontains=search) |
            models.Q(customer__email__icontains=search) |
            models.Q(event_type__icontains=search)
        )
    
    context = {
        'bookings': bookings,
        'status': status,
        'search': search,
        'status_choices': Booking.STATUS_CHOICES,
    }
    return render(request, 'admin/bookings.html', context)


@login_required
def admin_booking_detail(request, booking_id):
    """Admin booking detail view."""
    if not request.user.is_staff:
        return redirect('savory_events:login')
    
    booking = get_object_or_404(Booking, id=booking_id)
    
    # Get related data
    menu_items = BookingMenuItem.objects.filter(booking=booking).select_related('menu_item')
    sound_systems = BookingSoundSystem.objects.filter(booking=booking).select_related('sound_system')
    payments = Payment.objects.filter(booking=booking).order_by('-created_at')
    
    # Calculate payment summary
    total_paid = payments.aggregate(total=models.Sum('amount'))['total'] or 0
    balance_due = booking.total_amount - total_paid
    
    context = {
        'booking': booking,
        'menu_items': menu_items,
        'sound_systems': sound_systems,
        'payments': payments,
        'total_paid': total_paid,
        'balance_due': balance_due,
    }
    
    return render(request, 'admin/booking_detail.html', context)


@login_required
def admin_customer_detail(request, customer_id):
    """Admin customer detail view."""
    if not request.user.is_staff:
        return redirect('savory_events:login')
    
    customer = get_object_or_404(Customer, id=customer_id)
    
    # Get customer's bookings
    bookings = Booking.objects.filter(customer=customer).order_by('-created_at')
    
    # Calculate statistics
    total_bookings = bookings.count()
    total_spent = bookings.aggregate(total=models.Sum('total_amount'))['total'] or 0
    
    context = {
        'customer': customer,
        'bookings': bookings,
        'total_bookings': total_bookings,
        'total_spent': total_spent,
    }
    
    return render(request, 'admin/customer_detail.html', context)


def admin_dashboard_redirect(request):
    """Redirect old admin-dashboard URL to new management URL."""
    return redirect('savory_events:admin_dashboard')


# Authentication Views
def register_view(request):
    """User registration view."""
    if request.user.is_authenticated:
        return redirect('savory_events:customer_dashboard')
    
    if request.method == 'POST':
        form = CustomUserCreationForm(request.POST)
        if form.is_valid():
            user = form.save()
            username = form.cleaned_data.get('username')
            messages.success(request, f'Account created for {username}! You can now log in.')
            return redirect('savory_events:login')
    else:
        form = CustomUserCreationForm()
    
    return render(request, 'auth/register.html', {'form': form})


def login_view(request):
    """User login view."""
    if request.user.is_authenticated:
        if request.user.is_staff:
            return redirect('savory_events:admin_dashboard')
        else:
            return redirect('savory_events:customer_dashboard')
    
    if request.method == 'POST':
        form = CustomAuthenticationForm(request, data=request.POST)
        if form.is_valid():
            username = form.cleaned_data.get('username')
            password = form.cleaned_data.get('password')
            user = authenticate(username=username, password=password)
            if user is not None:
                login(request, user)
                if user.is_staff:
                    return redirect('savory_events:admin_dashboard')
                else:
                    return redirect('savory_events:customer_dashboard')
    else:
        form = CustomAuthenticationForm()
    
    return render(request, 'auth/login.html', {'form': form})


def logout_view(request):
    """User logout view."""
    logout(request)
    messages.success(request, 'You have been successfully logged out.')
    return redirect('savory_events:home')


@login_required
def customer_dashboard(request):
    """Customer dashboard view."""
    if request.user.is_staff:
        return redirect('savory_events:admin_dashboard')

    # Get customer record
    try:
        customer = Customer.objects.get(email=request.user.email)
    except Customer.DoesNotExist:
        # Create customer record if it doesn't exist
        customer = Customer.objects.create(
            name=f"{request.user.first_name} {request.user.last_name}",
            email=request.user.email,
            phone=""
        )

    # Get customer's bookings
    customer_bookings = Booking.objects.filter(customer=customer).order_by('-created_at')

    # Calculate statistics
    total_bookings = customer_bookings.count()
    pending_bookings = customer_bookings.filter(status__in=['pending', 'confirmed']).count()
    completed_bookings = customer_bookings.filter(status='completed').count()

    # Calculate financial statistics
    total_spent = customer_bookings.filter(status__in=['completed', 'fully_paid']).aggregate(
        total=models.Sum('total_amount'))['total'] or 0
    avg_booking_value = customer_bookings.aggregate(
        avg=models.Avg('total_amount'))['avg'] or 0

    # Get upcoming events (future events that are confirmed or pending)
    from django.utils import timezone
    upcoming_events = customer_bookings.filter(
        event_date__gt=timezone.now().date(),
        status__in=['pending', 'confirmed', 'partial_paid', 'fully_paid']
    ).order_by('event_date')[:5]
    upcoming_events_count = upcoming_events.count()

    # Customer insights
    favorite_event_type = None
    last_booking_date = None

    if customer_bookings.exists():
        # Most frequent event type
        event_type_counts = customer_bookings.values('event_type').annotate(
            count=models.Count('id')).order_by('-count').first()
        if event_type_counts:
            favorite_event_type = event_type_counts['event_type']

        # Last booking date
        last_booking = customer_bookings.first()
        if last_booking:
            last_booking_date = last_booking.event_date

    # Set is_customer to True for non-staff authenticated users
    is_customer = not request.user.is_staff
    context = {
        'customer': customer,
        'bookings': customer_bookings[:5],  # Show latest 5 bookings
        'total_bookings': total_bookings,
        'pending_bookings': pending_bookings,
        'completed_bookings': completed_bookings,
        'total_spent': total_spent,
        'avg_booking_value': avg_booking_value,
        'upcoming_events': upcoming_events,
        'upcoming_events_count': upcoming_events_count,
        'favorite_event_type': favorite_event_type,
        'last_booking_date': last_booking_date,
        'is_customer': is_customer,
    }

    return render(request, 'customer/dashboard.html', context)


@login_required
def customer_bookings(request):
    """Customer bookings list view."""
    if request.user.is_staff:
        return redirect('savory_events:admin_dashboard')
    
    try:
        customer = Customer.objects.get(email=request.user.email)
        bookings = Booking.objects.filter(customer=customer).order_by('-created_at')
    except Customer.DoesNotExist:
        bookings = []

    context = {
        'bookings': bookings,
        'is_customer': True,
    }
    return render(request, 'customer/bookings.html', context)


@login_required
def customer_booking_detail(request, booking_id):
    """Customer booking detail view."""
    if request.user.is_staff:
        return redirect('savory_events:admin_dashboard')
    
    try:
        customer = Customer.objects.get(email=request.user.email)
        booking = get_object_or_404(Booking, id=booking_id, customer=customer)
        
        # Get related data
        menu_items = BookingMenuItem.objects.filter(booking=booking).select_related('menu_item')
        sound_systems = BookingSoundSystem.objects.filter(booking=booking).select_related('sound_system')
        payments = Payment.objects.filter(booking=booking).order_by('-created_at')
        
        context = {
            'booking': booking,
            'menu_items': menu_items,
            'sound_systems': sound_systems,
            'payments': payments,
            'is_customer': True,
        }
        
        return render(request, 'customer/booking_detail.html', context)
        
    except Customer.DoesNotExist:
        messages.error(request, 'Customer profile not found.')
        return redirect('savory_events:customer_dashboard')


@login_required
def customer_profile(request):
    """Customer profile view and edit."""
    if request.user.is_staff:
        return redirect('savory_events:admin_dashboard')
    
    try:
        customer = Customer.objects.get(email=request.user.email)
    except Customer.DoesNotExist:
        # Create customer record if it doesn't exist
        customer = Customer.objects.create(
            name=f"{request.user.first_name} {request.user.last_name}",
            email=request.user.email,
            phone=""
        )
    
    if request.method == 'POST':
        # Update user info
        request.user.first_name = request.POST.get('first_name', '')
        request.user.last_name = request.POST.get('last_name', '')
        request.user.email = request.POST.get('email', '')
        request.user.save()
        
        # Update customer info
        customer.name = f"{request.user.first_name} {request.user.last_name}"
        customer.email = request.user.email
        customer.phone = request.POST.get('phone', '')
        customer.save()
        
        messages.success(request, 'Profile updated successfully!')
        return redirect('savory_events:customer_profile')
    
    context = {
        'customer': customer,
        'is_customer': True,
    }
    return render(request, 'customer/profile.html', context)
