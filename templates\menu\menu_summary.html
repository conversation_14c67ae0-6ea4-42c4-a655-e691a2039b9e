<h3 class="text-xl font-semibold text-charcoal mb-4">Your Selection Updated</h3>
<div class="space-y-2 mb-4">
    <!-- This would be populated with selected items from the server -->
    <div class="flex justify-between items-center text-sm">
        <span>Bruschetta Trio</span>
        <div class="flex items-center space-x-2">
            <span>$8.50</span>
            <button class="text-red-500 hover:text-red-700">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>
    </div>
    <div class="flex justify-between items-center text-sm">
        <span>Grilled Salmon</span>
        <div class="flex items-center space-x-2">
            <span>$28.00</span>
            <button class="text-red-500 hover:text-red-700">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>
    </div>
</div>
<div class="border-t border-sage-green pt-4">
    <div class="flex justify-between items-center font-semibold text-lg">
        <span>Total per person:</span>
        <span>$36.50</span>
    </div>
    <p class="text-sm text-gray-600 mt-2">
        Final pricing may vary based on guest count and customizations.
    </p>
</div>
<div class="mt-4 text-center">
    <a href="/booking/" 
       class="inline-block bg-burnt-orange hover:bg-opacity-80 text-white font-bold py-2 px-6 rounded-lg transition-colors">
        Proceed to Booking
    </a>
</div>
