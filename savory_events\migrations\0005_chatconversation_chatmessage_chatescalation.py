# Generated by Django 4.2.17 on 2025-08-03 15:58

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('savory_events', '0004_add_admin_booking_features'),
    ]

    operations = [
        migrations.CreateModel(
            name='ChatConversation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('session_id', models.CharField(help_text='Unique session identifier', max_length=100, unique=True)),
                ('is_active', models.<PERSON>oleanField(default=True, help_text='Whether the conversation is active')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('customer', models.ForeignKey(blank=True, help_text='Associated customer if identified', null=True, on_delete=django.db.models.deletion.SET_NULL, to='savory_events.customer')),
                ('user', models.ForeignKey(blank=True, help_text='Associated user (null for anonymous users)', null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Chat Conversation',
                'verbose_name_plural': 'Chat Conversations',
                'ordering': ['-updated_at'],
            },
        ),
        migrations.CreateModel(
            name='ChatMessage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('message_type', models.CharField(choices=[('user', 'User Message'), ('bot', 'Bot Response'), ('system', 'System Message'), ('escalation', 'Escalated to Human')], help_text='Type of message', max_length=20)),
                ('content', models.TextField(help_text='Message content')),
                ('metadata', models.TextField(blank=True, default='{}', help_text='Additional metadata as JSON string (e.g., API response data, context)')),
                ('is_visible', models.BooleanField(default=True, help_text='Whether message is visible to user')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('conversation', models.ForeignKey(help_text='Associated conversation', on_delete=django.db.models.deletion.CASCADE, related_name='messages', to='savory_events.chatconversation')),
            ],
            options={
                'verbose_name': 'Chat Message',
                'verbose_name_plural': 'Chat Messages',
                'ordering': ['created_at'],
            },
        ),
        migrations.CreateModel(
            name='ChatEscalation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('reason', models.TextField(help_text='Reason for escalation')),
                ('status', models.CharField(choices=[('pending', 'Pending Review'), ('assigned', 'Assigned to Staff'), ('in_progress', 'In Progress'), ('resolved', 'Resolved'), ('closed', 'Closed')], default='pending', help_text='Current status of escalation', max_length=20)),
                ('priority', models.CharField(choices=[('low', 'Low Priority'), ('normal', 'Normal Priority'), ('high', 'High Priority'), ('urgent', 'Urgent')], default='normal', help_text='Priority level', max_length=10)),
                ('resolution_notes', models.TextField(blank=True, help_text='Notes about resolution')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('resolved_at', models.DateTimeField(blank=True, null=True)),
                ('assigned_staff', models.ForeignKey(blank=True, help_text='Staff member assigned to handle escalation', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='assigned_escalations', to=settings.AUTH_USER_MODEL)),
                ('conversation', models.OneToOneField(help_text='Associated conversation', on_delete=django.db.models.deletion.CASCADE, related_name='escalation', to='savory_events.chatconversation')),
            ],
            options={
                'verbose_name': 'Chat Escalation',
                'verbose_name_plural': 'Chat Escalations',
                'ordering': ['-created_at'],
            },
        ),
    ]
