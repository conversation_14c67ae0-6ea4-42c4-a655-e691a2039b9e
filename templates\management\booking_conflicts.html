{% extends 'management/base.html' %}

{% block title %}Booking Conflicts{% endblock %}
{% block page_title %}Booking Conflicts{% endblock %}

{% block content %}
<!-- Conflicts Header -->
<div class="bg-white shadow rounded-lg mb-6">
    <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex items-center justify-between">
            <div>
                <h2 class="text-xl font-semibold text-gray-900">Booking Conflicts</h2>
                <p class="text-sm text-gray-500 mt-1">
                    Potential scheduling conflicts and overlapping bookings
                </p>
            </div>
            <div class="flex items-center space-x-4">
                <div class="text-sm text-gray-600">
                    Conflict Dates: <span class="font-medium text-red-600">{{ total_conflict_dates }}</span>
                </div>
                <a href="{% url 'savory_events:management_booking_calendar' %}" 
                   class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                    <i class="fas fa-calendar mr-2"></i>Calendar View
                </a>
                <a href="{% url 'savory_events:management_bookings' %}" 
                   class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                    <i class="fas fa-list mr-2"></i>All Bookings
                </a>
            </div>
        </div>
    </div>
</div>

{% if conflicts %}
<!-- Conflicts List -->
<div class="space-y-6">
    {% for conflict in conflicts %}
    <div class="bg-white shadow rounded-lg border-l-4 border-red-500">
        <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex items-center justify-between">
                <div>
                    <h3 class="text-lg font-medium text-gray-900">
                        {{ conflict.date|date:"l, F d, Y" }}
                    </h3>
                    <p class="text-sm text-gray-500">
                        {{ conflict.booking_count }} booking{{ conflict.booking_count|pluralize }} on this date
                        {% if conflict.time_conflicts %}
                        • {{ conflict.time_conflicts|length }} time conflict{{ conflict.time_conflicts|length|pluralize }}
                        {% endif %}
                    </p>
                </div>
                <div class="flex items-center space-x-2">
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-red-100 text-red-800">
                        <i class="fas fa-exclamation-triangle mr-1"></i>
                        High Priority
                    </span>
                </div>
            </div>
        </div>
        
        <div class="px-6 py-4">
            <!-- All Bookings for this date -->
            <div class="space-y-4">
                {% for booking in conflict.bookings %}
                <div class="border border-gray-200 rounded-lg p-4 
                    {% if booking.status == 'confirmed' or booking.status == 'fully_paid' %}bg-blue-50
                    {% elif booking.status == 'partial_paid' %}bg-orange-50
                    {% else %}bg-gray-50{% endif %}">
                    
                    <div class="flex items-center justify-between">
                        <div class="flex-1">
                            <div class="flex items-center space-x-4">
                                <div>
                                    <h4 class="font-medium text-gray-900">
                                        <a href="{% url 'savory_events:management_booking_detail' booking.id %}" 
                                           class="text-blue-600 hover:text-blue-800">
                                            {{ booking.customer.name }}
                                        </a>
                                    </h4>
                                    <p class="text-sm text-gray-600">
                                        {{ booking.get_event_type_display }} • {{ booking.guest_count }} guests
                                    </p>
                                </div>
                                <div class="text-center">
                                    <p class="text-lg font-medium text-gray-900">
                                        {{ booking.event_date|time:"g:i A" }}
                                    </p>
                                    <p class="text-xs text-gray-500">Event Time</p>
                                </div>
                                <div class="text-center">
                                    <p class="text-lg font-medium text-gray-900">
                                        ${{ booking.total_amount }}
                                    </p>
                                    <p class="text-xs text-gray-500">Total Value</p>
                                </div>
                            </div>
                            
                            <div class="mt-3 flex items-center space-x-4">
                                <span class="inline-flex px-2 py-1 text-xs font-medium rounded-full {{ booking.get_status_display_class }}">
                                    {{ booking.get_status_display }}
                                </span>
                                <span class="text-sm text-gray-600">
                                    <i class="fas fa-map-marker-alt mr-1"></i>
                                    {{ booking.venue_address|truncatewords:8 }}
                                </span>
                                {% if booking.assigned_coordinator %}
                                <span class="text-sm text-gray-600">
                                    <i class="fas fa-user-tie mr-1"></i>
                                    {{ booking.assigned_coordinator.get_full_name }}
                                </span>
                                {% endif %}
                            </div>
                        </div>
                        
                        <div class="flex flex-col space-y-2">
                            <a href="{% url 'savory_events:management_booking_detail' booking.id %}" 
                               class="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm text-center">
                                View Details
                            </a>
                            {% if booking.can_be_modified %}
                            <button onclick="showEditModal({{ booking.id }})" 
                                    class="bg-orange-600 hover:bg-orange-700 text-white px-3 py-1 rounded text-sm">
                                Reschedule
                            </button>
                            {% endif %}
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
            
            <!-- Time Conflicts Details -->
            {% if conflict.time_conflicts %}
            <div class="mt-6 p-4 bg-red-50 border border-red-200 rounded-lg">
                <h4 class="font-medium text-red-800 mb-3">
                    <i class="fas fa-clock mr-2"></i>Time Conflicts Detected
                </h4>
                <div class="space-y-2">
                    {% for booking1, booking2, time_diff in conflict.time_conflicts %}
                    <div class="text-sm text-red-700">
                        <strong>{{ booking1.customer.name }}</strong> ({{ booking1.event_date|time:"g:i A" }}) 
                        and 
                        <strong>{{ booking2.customer.name }}</strong> ({{ booking2.event_date|time:"g:i A" }})
                        are only <strong>{{ time_diff|floatformat:1 }} hours</strong> apart
                    </div>
                    {% endfor %}
                </div>
                <div class="mt-3 text-sm text-red-600">
                    <i class="fas fa-info-circle mr-1"></i>
                    Events less than 4 hours apart may require additional coordination for setup/cleanup.
                </div>
            </div>
            {% endif %}
            
            <!-- Resolution Actions -->
            <div class="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                <h4 class="font-medium text-yellow-800 mb-3">
                    <i class="fas fa-tools mr-2"></i>Suggested Actions
                </h4>
                <div class="space-y-2 text-sm text-yellow-700">
                    <div>• Contact customers to confirm exact timing and setup requirements</div>
                    <div>• Assign additional staff for overlapping events</div>
                    <div>• Consider venue capacity and resource allocation</div>
                    <div>• Update internal notes with coordination details</div>
                    {% if conflict.time_conflicts %}
                    <div>• Reschedule one event if possible to avoid time conflicts</div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    {% endfor %}
</div>

{% else %}
<!-- No Conflicts -->
<div class="bg-white shadow rounded-lg">
    <div class="px-6 py-12 text-center">
        <div class="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-green-100">
            <i class="fas fa-check text-green-600 text-2xl"></i>
        </div>
        <h3 class="mt-4 text-lg font-medium text-gray-900">No Conflicts Detected</h3>
        <p class="mt-2 text-sm text-gray-500">
            All confirmed bookings are properly spaced with no scheduling conflicts.
        </p>
        <div class="mt-6">
            <a href="{% url 'savory_events:management_booking_calendar' %}" 
               class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                <i class="fas fa-calendar mr-2"></i>View Calendar
            </a>
        </div>
    </div>
</div>
{% endif %}

<!-- Conflict Resolution Tips -->
<div class="mt-6 bg-white shadow rounded-lg">
    <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900">Conflict Resolution Tips</h3>
    </div>
    <div class="px-6 py-4">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
                <h4 class="font-medium text-gray-900 mb-2">
                    <i class="fas fa-lightbulb text-yellow-500 mr-2"></i>Prevention
                </h4>
                <ul class="text-sm text-gray-600 space-y-1">
                    <li>• Maintain 4+ hour buffer between events</li>
                    <li>• Consider setup and cleanup time requirements</li>
                    <li>• Check venue capacity and resource availability</li>
                    <li>• Coordinate with kitchen and service staff</li>
                </ul>
            </div>
            <div>
                <h4 class="font-medium text-gray-900 mb-2">
                    <i class="fas fa-cogs text-blue-500 mr-2"></i>Resolution
                </h4>
                <ul class="text-sm text-gray-600 space-y-1">
                    <li>• Contact customers early to discuss options</li>
                    <li>• Offer alternative dates or times</li>
                    <li>• Assign dedicated staff teams per event</li>
                    <li>• Document all coordination decisions</li>
                </ul>
            </div>
        </div>
    </div>
</div>

<script>
function showEditModal(bookingId) {
    // Redirect to booking detail page for editing
    window.location.href = `/management/bookings/${bookingId}/`;
}
</script>
{% endblock %}
