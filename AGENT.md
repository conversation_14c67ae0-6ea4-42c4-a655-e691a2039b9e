# AGENT.md - BonAppetit Django Project

## Commands
- **Run development server**: `python manage.py runserver`
- **Run single test**: `python manage.py test savory_events.tests.TestModelName.test_method_name`
- **Run all tests**: `python manage.py test`
- **Migrate database**: `python manage.py migrate`
- **Create migrations**: `python manage.py makemigrations`
- **Collect static files**: `python manage.py collectstatic`

## Architecture
- Django project with SQLite database
- Main project: `BonAppetit/` (settings, urls, wsgi)
- Main app: `savory_events/` (models, views, templates)
- Static files: `static/` folder, served at `/static/`
- Media files: `media/` folder, served at `/media/`
- Frontend tech stack: Tailwind CSS, HTMX, Unpoly, Alpine.js CDN

## Code Style
- Use Django conventions and patterns
- Models in `savory_events/models.py`
- Views in `savory_events/views.py`
- Templates in app template directories
- Color palette: <PERSON> Olive (#556B2F), <PERSON><PERSON> (#D35400), <PERSON> (#FFF5E1), <PERSON><PERSON><PERSON><PERSON> (#2C2C2C)
- Mobile-first responsive design with Tailwind utility classes
