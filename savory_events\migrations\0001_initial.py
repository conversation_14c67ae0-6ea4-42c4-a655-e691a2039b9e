# Generated by Django 4.2.17 on 2025-08-02 15:56

from decimal import Decimal
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Booking',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('event_date', models.DateTimeField(help_text='Date and time of the event')),
                ('event_type', models.CharField(choices=[('wedding', 'Wedding'), ('corporate', 'Corporate Event'), ('birthday', 'Birthday Party'), ('anniversary', 'Anniversary'), ('other', 'Other')], help_text='Type of event', max_length=20)),
                ('guest_count', models.PositiveIntegerField(help_text='Number of guests', validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(1000)])),
                ('venue_address', models.TextField(help_text='Venue address')),
                ('special_requests', models.TextField(blank=True, help_text='Special requests or dietary restrictions')),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('confirmed', 'Confirmed'), ('partial_paid', 'Partially Paid'), ('fully_paid', 'Fully Paid'), ('completed', 'Completed'), ('cancelled', 'Cancelled')], default='pending', max_length=20)),
                ('total_amount', models.DecimalField(decimal_places=2, default=Decimal('0.00'), help_text='Total booking amount', max_digits=10)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Booking',
                'verbose_name_plural': 'Bookings',
                'ordering': ['-event_date'],
            },
        ),
        migrations.CreateModel(
            name='Customer',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text="Customer's full name", max_length=100)),
                ('email', models.EmailField(help_text="Customer's email address", max_length=254, validators=[django.core.validators.EmailValidator()])),
                ('phone', models.CharField(help_text="Customer's phone number", max_length=20)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Customer',
                'verbose_name_plural': 'Customers',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='MenuItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text='Menu item name', max_length=100)),
                ('description', models.TextField(help_text='Description of the menu item')),
                ('category', models.CharField(choices=[('appetizer', 'Appetizers'), ('main', 'Main Dishes'), ('dessert', 'Desserts'), ('drink', 'Beverages')], help_text='Menu category', max_length=20)),
                ('price_per_person', models.DecimalField(decimal_places=2, help_text='Price per person in dollars', max_digits=8, validators=[django.core.validators.MinValueValidator(Decimal('0.01'))])),
                ('image', models.ImageField(blank=True, help_text='Menu item image', null=True, upload_to='menu_items/')),
                ('is_available', models.BooleanField(default=True, help_text='Is this item currently available?')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Menu Item',
                'verbose_name_plural': 'Menu Items',
                'ordering': ['category', 'name'],
            },
        ),
        migrations.CreateModel(
            name='SoundSystemService',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text='Service name', max_length=100)),
                ('package_type', models.CharField(choices=[('dj', 'DJ Package'), ('microphones', 'Microphones Only'), ('full_audio', 'Full Audio Set')], help_text='Package type', max_length=20)),
                ('description', models.TextField(help_text='Service description')),
                ('price', models.DecimalField(decimal_places=2, help_text='Service price', max_digits=8, validators=[django.core.validators.MinValueValidator(Decimal('0.01'))])),
                ('is_available', models.BooleanField(default=True, help_text='Is this service currently available?')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Sound System Service',
                'verbose_name_plural': 'Sound System Services',
                'ordering': ['package_type', 'name'],
            },
        ),
        migrations.CreateModel(
            name='Payment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('amount', models.DecimalField(decimal_places=2, help_text='Payment amount', max_digits=10, validators=[django.core.validators.MinValueValidator(Decimal('0.01'))])),
                ('payment_type', models.CharField(choices=[('deposit', 'Deposit (50%)'), ('balance', 'Balance (50%)'), ('full', 'Full Payment')], help_text='Type of payment', max_length=20)),
                ('payment_date', models.DateTimeField(auto_now_add=True, help_text='Date payment was received')),
                ('notes', models.TextField(blank=True, help_text='Payment notes (method, reference, etc.)')),
                ('booking', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='payments', to='savory_events.booking')),
            ],
            options={
                'verbose_name': 'Payment',
                'verbose_name_plural': 'Payments',
                'ordering': ['-payment_date'],
            },
        ),
        migrations.CreateModel(
            name='BookingSoundSystem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('booking', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='sound_system_booking', to='savory_events.booking')),
                ('sound_system', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='bookings', to='savory_events.soundsystemservice')),
            ],
            options={
                'verbose_name': 'Booking Sound System',
                'verbose_name_plural': 'Booking Sound Systems',
            },
        ),
        migrations.AddField(
            model_name='booking',
            name='customer',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='bookings', to='savory_events.customer'),
        ),
        migrations.CreateModel(
            name='BookingMenuItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('quantity', models.PositiveIntegerField(default=1, help_text='Quantity of this menu item', validators=[django.core.validators.MinValueValidator(1)])),
                ('booking', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='menu_items', to='savory_events.booking')),
                ('menu_item', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='bookings', to='savory_events.menuitem')),
            ],
            options={
                'verbose_name': 'Booking Menu Item',
                'verbose_name_plural': 'Booking Menu Items',
                'unique_together': {('booking', 'menu_item')},
            },
        ),
    ]
