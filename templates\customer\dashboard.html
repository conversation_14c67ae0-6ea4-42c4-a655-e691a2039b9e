{% extends 'customer/base.html' %}

{% block title %}Dashboard - BonAppetit Catering{% endblock %}

{% block page_title %}Dashboard{% endblock %}

{% block page_subtitle %}Welcome back, {{ customer.name }}! Manage your catering bookings and account settings.{% endblock %}

{% block content %}
<div class="py-8">

    <!-- Statistics cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">

        <!-- Total Bookings -->
        <div class="stat-card bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="h-8 w-8 bg-blue-500 rounded-md flex items-center justify-center">
                            <i class="fas fa-calendar-alt text-white"></i>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Total Bookings</dt>
                            <dd class="text-lg font-medium text-gray-900">{{ total_bookings }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
            <div class="bg-gray-50 px-5 py-3">
                <div class="text-sm">
                    <span class="text-green-600 font-medium">{{ pending_bookings }}</span>
                    <span class="text-gray-500">pending</span>
                </div>
            </div>
        </div>

        <!-- Total Spent -->
        <div class="stat-card bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="h-8 w-8 bg-green-500 rounded-md flex items-center justify-center">
                            <i class="fas fa-dollar-sign text-white"></i>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Total Spent</dt>
                            <dd class="text-lg font-medium text-gray-900">${{ total_spent|floatformat:2 }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
            <div class="bg-gray-50 px-5 py-3">
                <div class="text-sm">
                    <span class="text-green-600 font-medium">${{ avg_booking_value|floatformat:2 }}</span>
                    <span class="text-gray-500">avg per event</span>
                </div>
            </div>
        </div>

        <!-- Pending Events -->
        <div class="stat-card bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="h-8 w-8 bg-yellow-500 rounded-md flex items-center justify-center">
                            <i class="fas fa-clock text-white"></i>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Pending Events</dt>
                            <dd class="text-lg font-medium text-gray-900">{{ pending_bookings }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
            <div class="bg-gray-50 px-5 py-3">
                <div class="text-sm">
                    <span class="text-blue-600 font-medium">{{ upcoming_events_count }}</span>
                    <span class="text-gray-500">upcoming</span>
                </div>
            </div>
        </div>

        <!-- Completed Events -->
        <div class="stat-card bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="h-8 w-8 bg-purple-500 rounded-md flex items-center justify-center">
                            <i class="fas fa-check-circle text-white"></i>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Completed Events</dt>
                            <dd class="text-lg font-medium text-gray-900">{{ completed_bookings }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
            <div class="bg-gray-50 px-5 py-3">
                <div class="text-sm">
                    {% if total_bookings > 0 %}
                    <span class="text-green-600 font-medium">{% widthratio completed_bookings total_bookings 100 %}%</span>
                    <span class="text-gray-500">completion rate</span>
                    {% else %}
                    <span class="text-gray-500">No events yet</span>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content Grid -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">

        <!-- Recent Bookings -->
        <div class="lg:col-span-2 bg-white shadow rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-medium text-gray-900">Recent Bookings</h3>
                    <a href="{% url 'savory_events:customer_bookings' %}"
                       class="text-sm text-deep-olive hover:text-burnt-orange font-medium">
                        View all
                    </a>
                </div>
            </div>
            <div class="overflow-hidden">
                <div class="overflow-x-auto">
                    {% if bookings %}
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Event</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total</th>
                                    <th class="relative px-6 py-3"><span class="sr-only">Actions</span></th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                {% for booking in bookings %}
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <div class="flex-shrink-0 h-10 w-10">
                                                <div class="h-10 w-10 rounded-full bg-deep-olive flex items-center justify-center">
                                                    <i class="fas fa-utensils text-white text-sm"></i>
                                                </div>
                                            </div>
                                            <div class="ml-4">
                                                <div class="text-sm font-medium text-gray-900">
                                                    {{ booking.get_event_type_display }}
                                                </div>
                                                <div class="text-sm text-gray-500">
                                                    {{ booking.guest_count }} guests
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900">{{ booking.event_date|date:"M d, Y" }}</div>
                                        <div class="text-sm text-gray-500">{{ booking.event_date|date:"g:i A" }}</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full
                                            {% if booking.status == 'confirmed' %}bg-green-100 text-green-800
                                            {% elif booking.status == 'pending' %}bg-yellow-100 text-yellow-800
                                            {% elif booking.status == 'completed' %}bg-blue-100 text-blue-800
                                            {% else %}bg-gray-100 text-gray-800{% endif %}">
                                            {{ booking.get_status_display }}
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">
                                            ${{ booking.total_amount|floatformat:2 }}
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                        <a href="{% url 'savory_events:customer_booking_detail' booking.id %}"
                                           class="text-deep-olive hover:text-burnt-orange">
                                            View
                                        </a>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    {% else %}
                        <div class="px-6 py-12 text-center">
                            <i class="fas fa-calendar-alt mx-auto h-12 w-12 text-gray-400 text-5xl mb-4"></i>
                            <h3 class="mt-2 text-sm font-medium text-gray-900">No bookings yet</h3>
                            <p class="mt-1 text-sm text-gray-500">Get started by creating your first booking.</p>
                            <div class="mt-6">
                                <a href="{% url 'savory_events:booking_wizard' %}"
                                   class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-burnt-orange hover:bg-opacity-80 transition-colors">
                                    <i class="fas fa-plus mr-2"></i>
                                    Create New Booking
                                </a>
                            </div>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Sidebar Content -->
        <div class="space-y-6">

            <!-- Upcoming Events -->
            <div class="bg-white shadow rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Upcoming Events</h3>
                </div>
                <div class="px-6 py-4">
                    <div class="space-y-4">
                        {% for event in upcoming_events %}
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <div class="h-8 w-8 bg-burnt-orange rounded-full flex items-center justify-center">
                                        <i class="fas fa-calendar text-white text-xs"></i>
                                    </div>
                                </div>
                                <div class="ml-3">
                                    <div class="text-sm font-medium text-gray-900">
                                        {{ event.get_event_type_display }}
                                    </div>
                                    <div class="text-sm text-gray-500">
                                        {{ event.event_date|date:"M d, g:i A" }}
                                    </div>
                                </div>
                            </div>
                            <div class="text-right">
                                <div class="text-sm font-medium text-gray-900">
                                    ${{ event.total_amount|floatformat:2 }}
                                </div>
                            </div>
                        </div>
                        {% empty %}
                        <p class="text-sm text-gray-500">No upcoming events.</p>
                        {% endfor %}
                    </div>
                </div>
            </div>

            <!-- Personal Insights -->
            <div class="bg-white shadow rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Your Insights</h3>
                </div>
                <div class="px-6 py-4">
                    <div class="space-y-4">

                        {% if favorite_event_type %}
                        <div class="flex items-center justify-between">
                            <div class="text-sm text-gray-600">Favorite Event Type</div>
                            <div class="text-sm font-medium text-gray-900">
                                {{ favorite_event_type|title }}
                            </div>
                        </div>
                        {% endif %}

                        {% if last_booking_date %}
                        <div class="flex items-center justify-between">
                            <div class="text-sm text-gray-600">Last Event</div>
                            <div class="text-sm font-medium text-gray-900">
                                {{ last_booking_date|date:"M d, Y" }}
                            </div>
                        </div>
                        {% endif %}

                        {% if total_bookings > 0 %}
                        <div class="flex items-center justify-between">
                            <div class="text-sm text-gray-600">Success Rate</div>
                            <div class="text-sm font-medium text-gray-900">
                                {% widthratio completed_bookings total_bookings 100 %}%
                            </div>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="bg-white shadow rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Quick Actions</h3>
                </div>
                <div class="px-6 py-4">
                    <div class="space-y-3">
                        <a href="{% url 'savory_events:booking_wizard' %}"
                           class="w-full flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-deep-olive hover:bg-burnt-orange transition-colors duration-200">
                            <i class="fas fa-plus mr-2"></i>
                            New Booking
                        </a>
                        <a href="{% url 'savory_events:menu' %}"
                           class="w-full flex items-center justify-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                            <i class="fas fa-utensils mr-2"></i>
                            Browse Menu
                        </a>
                        <a href="{% url 'savory_events:customer_profile' %}"
                           class="w-full flex items-center justify-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                            <i class="fas fa-user mr-2"></i>
                            My Profile
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{% csrf_token %}
{% endblock %}

{% block extra_scripts %}
<script>
    // Auto-refresh dashboard every 5 minutes
    setTimeout(() => {
        location.reload();
    }, 300000);
</script>
{% endblock %}
