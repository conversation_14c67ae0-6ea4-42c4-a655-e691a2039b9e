from django import forms
from django.contrib.auth.forms import UserCreationForm, AuthenticationForm
from django.contrib.auth.models import User
from django.core.validators import MinValueValidator, MaxValueValidator
from django.utils import timezone
from .models import Customer, MenuItem, SoundSystemService, Booking, Payment, BookingMenuItem, BookingSoundSystem
from decimal import Decimal
import datetime
import re


class CustomUserCreationForm(UserCreationForm):
    """Custom user registration form with additional fields."""
    first_name = forms.CharField(
        max_length=30,
        required=True,
        widget=forms.TextInput(attrs={
            'class': 'w-full px-3 py-2 border border-sage-green rounded-md focus:outline-none focus:ring-2 focus:ring-burnt-orange transition-colors',
            'placeholder': 'First Name'
        })
    )
    last_name = forms.CharField(
        max_length=30,
        required=True,
        widget=forms.TextInput(attrs={
            'class': 'w-full px-3 py-2 border border-sage-green rounded-md focus:outline-none focus:ring-2 focus:ring-burnt-orange transition-colors',
            'placeholder': 'Last Name'
        })
    )
    email = forms.EmailField(
        required=True,
        widget=forms.EmailInput(attrs={
            'class': 'w-full px-3 py-2 border border-sage-green rounded-md focus:outline-none focus:ring-2 focus:ring-burnt-orange transition-colors',
            'placeholder': 'Email Address'
        })
    )
    phone = forms.CharField(
        max_length=20,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'w-full px-3 py-2 border border-sage-green rounded-md focus:outline-none focus:ring-2 focus:ring-burnt-orange transition-colors',
            'placeholder': 'Phone Number (Optional)'
        })
    )

    class Meta:
        model = User
        fields = ('username', 'first_name', 'last_name', 'email', 'phone', 'password1', 'password2')

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['username'].widget.attrs.update({
            'class': 'w-full px-3 py-2 border border-sage-green rounded-md focus:outline-none focus:ring-2 focus:ring-burnt-orange transition-colors',
            'placeholder': 'Username'
        })
        self.fields['password1'].widget.attrs.update({
            'class': 'w-full px-3 py-2 border border-sage-green rounded-md focus:outline-none focus:ring-2 focus:ring-burnt-orange transition-colors',
            'placeholder': 'Password'
        })
        self.fields['password2'].widget.attrs.update({
            'class': 'w-full px-3 py-2 border border-sage-green rounded-md focus:outline-none focus:ring-2 focus:ring-burnt-orange transition-colors',
            'placeholder': 'Confirm Password'
        })

    def save(self, commit=True):
        user = super().save(commit=False)
        user.email = self.cleaned_data['email']
        user.first_name = self.cleaned_data['first_name']
        user.last_name = self.cleaned_data['last_name']
        
        if commit:
            user.save()
            # Create corresponding Customer record
            Customer.objects.get_or_create(
                email=user.email,
                defaults={
                    'name': f"{user.first_name} {user.last_name}",
                    'phone': self.cleaned_data.get('phone', '')
                }
            )
        return user


class CustomAuthenticationForm(AuthenticationForm):
    """Custom login form with styled widgets."""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['username'].widget.attrs.update({
            'class': 'w-full px-3 py-2 border border-sage-green rounded-md focus:outline-none focus:ring-2 focus:ring-burnt-orange transition-colors',
            'placeholder': 'Username or Email'
        })
        self.fields['password'].widget.attrs.update({
            'class': 'w-full px-3 py-2 border border-sage-green rounded-md focus:outline-none focus:ring-2 focus:ring-burnt-orange transition-colors',
            'placeholder': 'Password'
        })


class ContactForm(forms.Form):
    """Contact form for customer inquiries."""
    name = forms.CharField(
        max_length=100,
        widget=forms.TextInput(attrs={
            'class': 'w-full px-3 py-2 border border-sage-green rounded-md focus:outline-none focus:ring-2 focus:ring-burnt-orange transition-colors',
            'placeholder': 'Your Name'
        })
    )
    email = forms.EmailField(
        widget=forms.EmailInput(attrs={
            'class': 'w-full px-3 py-2 border border-sage-green rounded-md focus:outline-none focus:ring-2 focus:ring-burnt-orange transition-colors',
            'placeholder': 'Your Email'
        })
    )
    message = forms.CharField(
        widget=forms.Textarea(attrs={
            'class': 'w-full px-3 py-2 border border-sage-green rounded-md focus:outline-none focus:ring-2 focus:ring-burnt-orange transition-colors',
            'placeholder': 'Your Message',
            'rows': 4
        })
    )


class BookingForm(forms.Form):
    """Form for creating catering bookings."""
    
    # Customer information
    customer_name = forms.CharField(
        max_length=100,
        label="Full Name",
        widget=forms.TextInput(attrs={
            'class': 'w-full px-3 py-2 border border-sage-green rounded-md focus:outline-none focus:ring-2 focus:ring-burnt-orange',
            'placeholder': 'Enter your full name'
        })
    )
    
    customer_email = forms.EmailField(
        label="Email Address",
        widget=forms.EmailInput(attrs={
            'class': 'w-full px-3 py-2 border border-sage-green rounded-md focus:outline-none focus:ring-2 focus:ring-burnt-orange',
            'placeholder': 'Enter your email address'
        })
    )
    
    customer_phone = forms.CharField(
        max_length=20,
        label="Phone Number",
        widget=forms.TextInput(attrs={
            'class': 'w-full px-3 py-2 border border-sage-green rounded-md focus:outline-none focus:ring-2 focus:ring-burnt-orange',
            'placeholder': 'Enter your phone number'
        })
    )
    
    # Event details
    event_type = forms.ChoiceField(
        choices=[
            ('wedding', 'Wedding'),
            ('corporate', 'Corporate Event'),
            ('birthday', 'Birthday Party'),
            ('anniversary', 'Anniversary'),
            ('graduation', 'Graduation'),
            ('other', 'Other'),
        ],
        label="Event Type",
        widget=forms.Select(attrs={
            'class': 'w-full px-3 py-2 border border-sage-green rounded-md focus:outline-none focus:ring-2 focus:ring-burnt-orange'
        })
    )
    
    event_date = forms.DateField(
        label="Event Date",
        widget=forms.DateInput(attrs={
            'type': 'date',
            'class': 'w-full px-3 py-2 border border-sage-green rounded-md focus:outline-none focus:ring-2 focus:ring-burnt-orange',
            'min': timezone.now().date().isoformat()
        })
    )
    
    event_time = forms.TimeField(
        label="Event Time",
        widget=forms.TimeInput(attrs={
            'type': 'time',
            'class': 'w-full px-3 py-2 border border-sage-green rounded-md focus:outline-none focus:ring-2 focus:ring-burnt-orange'
        })
    )
    
    guest_count = forms.IntegerField(
        label="Number of Guests",
        validators=[MinValueValidator(1), MaxValueValidator(500)],
        widget=forms.NumberInput(attrs={
            'class': 'w-full px-3 py-2 border border-sage-green rounded-md focus:outline-none focus:ring-2 focus:ring-burnt-orange',
            'placeholder': 'Number of guests',
            'min': 1,
            'max': 500
        })
    )
    
    venue_address = forms.CharField(
        max_length=255,
        label="Venue Address",
        widget=forms.TextInput(attrs={
            'class': 'w-full px-3 py-2 border border-sage-green rounded-md focus:outline-none focus:ring-2 focus:ring-burnt-orange',
            'placeholder': 'Enter the event venue address'
        })
    )
    
    # Sound system selection
    sound_system = forms.ChoiceField(
        choices=[
            ('', 'No Sound System'),
            ('microphones', 'Microphones Only'),
            ('dj', 'DJ Package'),
            ('full_audio', 'Full Audio Set'),
        ],
        label="Sound System",
        required=False,
        widget=forms.RadioSelect(attrs={
            'class': 'sound-system-option'
        })
    )
    
    special_requests = forms.CharField(
        required=False,
        label="Special Requests",
        widget=forms.Textarea(attrs={
            'class': 'w-full px-3 py-2 border border-sage-green rounded-md focus:outline-none focus:ring-2 focus:ring-burnt-orange',
            'placeholder': 'Any special dietary requirements, setup instructions, or other requests...',
            'rows': 4
        })
    )
    
    # Menu selections (handled separately via HTMX)
    selected_menu_items = forms.CharField(
        required=False,
        widget=forms.HiddenInput()
    )
    
    def clean_event_date(self):
        event_date = self.cleaned_data.get('event_date')
        if event_date:
            if event_date <= timezone.now().date():
                raise forms.ValidationError("Event date must be in the future.")
            if event_date > timezone.now().date() + datetime.timedelta(days=730):  # 2 years
                raise forms.ValidationError("Event date cannot be more than 2 years in advance.")
        return event_date
    
    def clean_customer_phone(self):
        phone = self.cleaned_data.get('customer_phone')
        if phone:
            # Simple phone validation - remove non-digits and check length
            digits_only = re.sub(r'\D', '', phone)
            if len(digits_only) < 10:
                raise forms.ValidationError("Please enter a valid phone number with at least 10 digits.")
        return phone


class MenuSelectionForm(forms.Form):
    """Form for selecting menu items."""
    selected_items = forms.CharField(
        widget=forms.HiddenInput(),
        required=False
    )
    
    def clean_selected_items(self):
        data = self.cleaned_data.get('selected_items', '')
        if data:
            try:
                import json
                selected = json.loads(data)
                if not isinstance(selected, list):
                    raise forms.ValidationError("Invalid menu selection format.")
                return selected
            except (json.JSONDecodeError, ValueError):
                raise forms.ValidationError("Invalid menu selection data.")
        return []


class PaymentForm(forms.ModelForm):
    """Form for logging payments by admin users."""
    
    class Meta:
        model = Payment
        fields = ['amount', 'payment_type', 'notes']
        widgets = {
            'amount': forms.NumberInput(attrs={
                'class': 'w-full px-3 py-2 border border-sage-green rounded-md focus:outline-none focus:ring-2 focus:ring-burnt-orange',
                'placeholder': 'Payment amount',
                'step': '0.01',
                'min': '0'
            }),
            'payment_type': forms.Select(attrs={
                'class': 'w-full px-3 py-2 border border-sage-green rounded-md focus:outline-none focus:ring-2 focus:ring-burnt-orange'
            }),
            'notes': forms.Textarea(attrs={
                'class': 'w-full px-3 py-2 border border-sage-green rounded-md focus:outline-none focus:ring-2 focus:ring-burnt-orange',
                'placeholder': 'Payment notes (optional)',
                'rows': 3
            })
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['notes'].required = False
