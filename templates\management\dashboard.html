{% extends 'management/base.html' %}

{% block title %}Dashboard{% endblock %}
{% block page_title %}Management Dashboard{% endblock %}

{% block content %}
<!-- Statistics Cards -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
    
    <!-- Total Bookings -->
    <div class="stat-card bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="h-8 w-8 bg-blue-500 rounded-md flex items-center justify-center">
                        <i class="fas fa-calendar-alt text-white"></i>
                    </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Total Bookings</dt>
                        <dd class="text-lg font-medium text-gray-900">{{ stats.total_bookings }}</dd>
                    </dl>
                </div>
            </div>
        </div>
        <div class="bg-gray-50 px-5 py-3">
            <div class="text-sm">
                <span class="text-green-600 font-medium">{{ stats.active_bookings }}</span>
                <span class="text-gray-500">active</span>
            </div>
        </div>
    </div>

    <!-- Revenue -->
    <div class="stat-card bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="h-8 w-8 bg-green-500 rounded-md flex items-center justify-center">
                        <i class="fas fa-dollar-sign text-white"></i>
                    </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Total Revenue</dt>
                        <dd class="text-lg font-medium text-gray-900">${{ revenue.total|floatformat:2 }}</dd>
                    </dl>
                </div>
            </div>
        </div>
        <div class="bg-gray-50 px-5 py-3">
            <div class="text-sm">
                <span class="text-green-600 font-medium">${{ revenue.this_month|floatformat:2 }}</span>
                <span class="text-gray-500">this month</span>
            </div>
        </div>
    </div>

    <!-- Customers -->
    <div class="stat-card bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="h-8 w-8 bg-purple-500 rounded-md flex items-center justify-center">
                        <i class="fas fa-users text-white"></i>
                    </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Total Customers</dt>
                        <dd class="text-lg font-medium text-gray-900">{{ stats.total_customers }}</dd>
                    </dl>
                </div>
            </div>
        </div>
        <div class="bg-gray-50 px-5 py-3">
            <div class="text-sm">
                <span class="text-green-600 font-medium">{{ stats.pending_bookings }}</span>
                <span class="text-gray-500">pending bookings</span>
            </div>
        </div>
    </div>

    <!-- Average Booking Value -->
    <div class="stat-card bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="h-8 w-8 bg-orange-500 rounded-md flex items-center justify-center">
                        <i class="fas fa-chart-line text-white"></i>
                    </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Avg. Booking Value</dt>
                        <dd class="text-lg font-medium text-gray-900">${{ insights.avg_booking_value|floatformat:2 }}</dd>
                    </dl>
                </div>
            </div>
        </div>
        <div class="bg-gray-50 px-5 py-3">
            <div class="text-sm">
                <span class="text-blue-600 font-medium">${{ revenue.pending|floatformat:2 }}</span>
                <span class="text-gray-500">pending</span>
            </div>
        </div>
    </div>
</div>

<!-- Main Content Grid -->
<div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
    
    <!-- Recent Bookings -->
    <div class="lg:col-span-2 bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-medium text-gray-900">Recent Bookings</h3>
                <a href="{% url 'savory_events:management_bookings' %}" 
                   class="text-sm text-deep-olive hover:text-burnt-orange font-medium">
                    View all
                </a>
            </div>
        </div>
        <div class="overflow-hidden">
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Event</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {% for booking in recent_bookings %}
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">{{ booking.customer.name }}</div>
                                <div class="text-sm text-gray-500">{{ booking.customer.email }}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">{{ booking.get_event_type_display }}</div>
                                <div class="text-sm text-gray-500">{{ booking.guest_count }} guests</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {{ booking.event_date|date:"M d, Y" }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full 
                                    {% if booking.status == 'pending' %}bg-yellow-100 text-yellow-800
                                    {% elif booking.status == 'confirmed' %}bg-blue-100 text-blue-800
                                    {% elif booking.status == 'partial_paid' %}bg-orange-100 text-orange-800
                                    {% elif booking.status == 'fully_paid' %}bg-green-100 text-green-800
                                    {% elif booking.status == 'completed' %}bg-gray-100 text-gray-800
                                    {% else %}bg-red-100 text-red-800{% endif %}">
                                    {{ booking.get_status_display }}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                ${{ booking.total_amount|floatformat:2 }}
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="5" class="px-6 py-4 text-center text-sm text-gray-500">
                                No recent bookings found.
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Sidebar Content -->
    <div class="space-y-6">
        
        <!-- Upcoming Events -->
        <div class="bg-white shadow rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Upcoming Events</h3>
            </div>
            <div class="px-6 py-4">
                <div class="space-y-4">
                    {% for event in upcoming_events %}
                    <div class="flex items-center justify-between">
                        <div class="flex-1 min-w-0">
                            <div class="text-sm font-medium text-gray-900 truncate">
                                {{ event.customer.name }}
                            </div>
                            <div class="text-xs text-gray-500">
                                {{ event.get_event_type_display }} • {{ event.guest_count }} guests
                            </div>
                            <div class="text-xs text-gray-400">
                                {{ event.event_date|date:"M d, Y" }}
                            </div>
                        </div>
                        <div class="text-right">
                            <div class="text-sm font-medium text-gray-900">
                                ${{ event.total_amount|floatformat:2 }}
                            </div>
                        </div>
                    </div>
                    {% empty %}
                    <p class="text-sm text-gray-500">No upcoming events.</p>
                    {% endfor %}
                </div>
            </div>
        </div>

        <!-- Quick Insights -->
        <div class="bg-white shadow rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Quick Insights</h3>
            </div>
            <div class="px-6 py-4">
                <div class="space-y-4">
                    
                    {% if insights.popular_event_type %}
                    <div class="flex items-center justify-between">
                        <div class="text-sm text-gray-600">Most Popular Event</div>
                        <div class="text-sm font-medium text-gray-900">
                            {{ insights.popular_event_type.event_type|title }}
                        </div>
                    </div>
                    {% endif %}
                    
                    {% if insights.busiest_month %}
                    <div class="flex items-center justify-between">
                        <div class="text-sm text-gray-600">Busiest Month</div>
                        <div class="text-sm font-medium text-gray-900">
                            Month {{ insights.busiest_month.month }}
                        </div>
                    </div>
                    {% endif %}
                    
                    <div class="flex items-center justify-between">
                        <div class="text-sm text-gray-600">Completion Rate</div>
                        <div class="text-sm font-medium text-gray-900">
                            {% widthratio stats.completed_bookings stats.total_bookings 100 %}%
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="bg-white shadow rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Quick Actions</h3>
            </div>
            <div class="px-6 py-4">
                <div class="space-y-3">
                    <a href="{% url 'savory_events:booking_wizard' %}" 
                       class="w-full flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-deep-olive hover:bg-burnt-orange transition-colors duration-200">
                        <i class="fas fa-plus mr-2"></i>
                        New Booking
                    </a>
                    <a href="{% url 'savory_events:management_payments' %}" 
                       class="w-full flex items-center justify-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                        <i class="fas fa-credit-card mr-2"></i>
                        Process Payment
                    </a>
                    <a href="{% url 'savory_events:management_analytics' %}" 
                       class="w-full flex items-center justify-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                        <i class="fas fa-chart-line mr-2"></i>
                        View Reports
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

{% csrf_token %}
{% endblock %}

{% block extra_scripts %}
<script>
    // Auto-refresh dashboard every 5 minutes
    setTimeout(() => {
        location.reload();
    }, 300000);
</script>
{% endblock %}
