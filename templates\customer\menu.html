{% extends 'customer/base.html' %}

{% block title %}Browse Menu - BonAppetit Catering{% endblock %}

{% block page_title %}Browse Menu{% endblock %}

{% block page_subtitle %}Explore our delicious catering menu options for your event{% endblock %}

{% block content %}
<div class="py-8">
    <div x-data="menuSelection({{ menu_items|safe }})" class="space-y-8">
        
        <!-- Quick Actions -->
        <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex flex-col sm:flex-row items-center justify-between gap-4">
                <div>
                    <h2 class="text-xl font-semibold text-charcoal">Ready to Book?</h2>
                    <p class="text-gray-600">Use our menu selections to create your perfect event</p>
                </div>
                <div class="flex gap-3">
                    <a href="{% url 'savory_events:booking_wizard' %}" 
                       class="bg-burnt-orange hover:bg-opacity-80 text-white px-4 py-2 rounded-md transition-colors">
                        <i class="fas fa-magic mr-2"></i>Booking Wizard
                    </a>
                    <a href="{% url 'savory_events:booking' %}" 
                       class="border border-burnt-orange text-burnt-orange hover:bg-burnt-orange hover:text-white px-4 py-2 rounded-md transition-colors">
                        <i class="fas fa-plus mr-2"></i>Quick Booking
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Category Filter -->
        <div class="bg-white rounded-lg shadow-md p-6">
            <div class="text-center mb-6">
                <h2 class="text-2xl font-bold text-charcoal mb-2">Filter by Category</h2>
                <p class="text-gray-600">Browse our menu by category to find exactly what you're looking for</p>
            </div>
            <div class="flex flex-wrap justify-center gap-3">
                <button @click="setActiveCategory('all')" 
                        :class="activeCategory === 'all' ? 'bg-burnt-orange text-white shadow-lg scale-105' : 'bg-cream text-charcoal hover:bg-sage-green hover:text-white border border-sage-green'"
                        class="px-6 py-3 rounded-lg font-medium transition-all duration-200 transform">
                    <i class="fas fa-utensils mr-2"></i>All Items
                </button>
                <button @click="setActiveCategory('appetizer')" 
                        :class="activeCategory === 'appetizer' ? 'bg-burnt-orange text-white shadow-lg scale-105' : 'bg-cream text-charcoal hover:bg-sage-green hover:text-white border border-sage-green'"
                        class="px-6 py-3 rounded-lg font-medium transition-all duration-200 transform">
                    <i class="fas fa-cheese mr-2"></i>Appetizers
                </button>
                <button @click="setActiveCategory('main')" 
                        :class="activeCategory === 'main' ? 'bg-burnt-orange text-white shadow-lg scale-105' : 'bg-cream text-charcoal hover:bg-sage-green hover:text-white border border-sage-green'"
                        class="px-6 py-3 rounded-lg font-medium transition-all duration-200 transform">
                    <i class="fas fa-drumstick-bite mr-2"></i>Main Dishes
                </button>
                <button @click="setActiveCategory('dessert')" 
                        :class="activeCategory === 'dessert' ? 'bg-burnt-orange text-white shadow-lg scale-105' : 'bg-cream text-charcoal hover:bg-sage-green hover:text-white border border-sage-green'"
                        class="px-6 py-3 rounded-lg font-medium transition-all duration-200 transform">
                    <i class="fas fa-birthday-cake mr-2"></i>Desserts
                </button>
                <button @click="setActiveCategory('drink')" 
                        :class="activeCategory === 'drink' ? 'bg-burnt-orange text-white shadow-lg scale-105' : 'bg-cream text-charcoal hover:bg-sage-green hover:text-white border border-sage-green'"
                        class="px-6 py-3 rounded-lg font-medium transition-all duration-200 transform">
                    <i class="fas fa-wine-glass mr-2"></i>Beverages
                </button>
            </div>
        </div>
        
        <!-- Menu Items Grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <template x-for="item in filteredItems" :key="item.id">
                <div class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-200 border border-sage-green">
                    <!-- Item Image -->
                    <div class="h-48 bg-gradient-to-br from-cream to-sage-green flex items-center justify-center">
                        <div x-show="item.image" class="w-full h-full">
                            <img :src="item.image" :alt="item.name" class="w-full h-full object-cover">
                        </div>
                        <div x-show="!item.image" class="text-center">
                            <i class="fas fa-utensils text-4xl text-sage-green mb-2"></i>
                            <p class="text-sage-green font-medium" x-text="getCategoryName(item.category)"></p>
                        </div>
                    </div>
                    
                    <!-- Item Details -->
                    <div class="p-6">
                        <div class="flex items-start justify-between mb-3">
                            <h3 class="text-lg font-semibold text-charcoal" x-text="item.name"></h3>
                            <span class="bg-burnt-orange text-white text-xs px-2 py-1 rounded-full font-medium" 
                                  x-text="getCategoryName(item.category)"></span>
                        </div>
                        
                        <p class="text-gray-600 text-sm mb-4 leading-relaxed" x-text="item.description"></p>
                        
                        <div class="flex items-center justify-between">
                            <div class="text-burnt-orange font-bold text-lg">
                                $<span x-text="item.price.toFixed(2)"></span>
                                <span class="text-sm text-gray-500 font-normal">per person</span>
                            </div>
                            
                            <div class="flex items-center space-x-2">
                                <button @click="toggleSelection(item)" 
                                        :class="isSelected(item) ? 'bg-sage-green text-white' : 'bg-cream text-charcoal border border-sage-green hover:bg-sage-green hover:text-white'"
                                        class="px-3 py-1 rounded-md text-sm font-medium transition-colors duration-200">
                                    <i :class="isSelected(item) ? 'fas fa-check' : 'fas fa-plus'" class="mr-1"></i>
                                    <span x-text="isSelected(item) ? 'Selected' : 'Select'"></span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </template>
        </div>
        
        <!-- No Items Message -->
        <div x-show="filteredItems.length === 0" class="text-center py-12">
            <i class="fas fa-search text-4xl text-gray-400 mb-4"></i>
            <h3 class="text-lg font-medium text-gray-900 mb-2">No items found</h3>
            <p class="text-gray-500">Try selecting a different category or check back later for new menu items.</p>
        </div>
        
        <!-- Selected Items Summary -->
        <div x-show="selectedItems.length > 0" 
             x-transition:enter="transition ease-out duration-300"
             x-transition:enter-start="opacity-0 transform translate-y-4"
             x-transition:enter-end="opacity-100 transform translate-y-0"
             class="fixed bottom-6 right-6 bg-white rounded-lg shadow-lg border border-sage-green p-4 max-w-sm">
            <div class="flex items-center justify-between mb-3">
                <h4 class="font-semibold text-charcoal">Selected Items</h4>
                <button @click="clearSelection()" class="text-red-500 hover:text-red-700 text-sm">
                    <i class="fas fa-times mr-1"></i>Clear
                </button>
            </div>
            
            <div class="space-y-2 mb-4 max-h-32 overflow-y-auto">
                <template x-for="item in selectedItems" :key="item.id">
                    <div class="flex items-center justify-between text-sm">
                        <span x-text="item.name" class="text-charcoal"></span>
                        <span x-text="'$' + item.price.toFixed(2)" class="text-burnt-orange font-medium"></span>
                    </div>
                </template>
            </div>
            
            <div class="border-t pt-3">
                <div class="flex items-center justify-between mb-3">
                    <span class="font-medium text-charcoal">Total per person:</span>
                    <span class="font-bold text-burnt-orange" x-text="'$' + getTotalPrice().toFixed(2)"></span>
                </div>
                
                <a href="{% url 'savory_events:booking_wizard' %}" 
                   class="block w-full bg-burnt-orange hover:bg-opacity-80 text-white text-center py-2 rounded-md transition-colors">
                    <i class="fas fa-arrow-right mr-2"></i>Continue to Booking
                </a>
            </div>
        </div>
    </div>
</div>

<script>
function menuSelection(menuItemsData) {
    return {
        activeCategory: 'all',
        selectedItems: [],
        menuItems: menuItemsData,
        
        get filteredItems() {
            if (this.activeCategory === 'all') {
                return this.menuItems;
            }
            return this.menuItems.filter(item => item.category === this.activeCategory);
        },
        
        setActiveCategory(category) {
            this.activeCategory = category;
        },
        
        getCategoryName(category) {
            const names = {
                'appetizer': 'Appetizer',
                'main': 'Main Dish',
                'dessert': 'Dessert',
                'drink': 'Beverage'
            };
            return names[category] || category;
        },
        
        toggleSelection(item) {
            const index = this.selectedItems.findIndex(selected => selected.id === item.id);
            if (index > -1) {
                this.selectedItems.splice(index, 1);
            } else {
                this.selectedItems.push(item);
            }
        },
        
        isSelected(item) {
            return this.selectedItems.some(selected => selected.id === item.id);
        },
        
        clearSelection() {
            this.selectedItems = [];
        },
        
        getTotalPrice() {
            return this.selectedItems.reduce((total, item) => total + item.price, 0);
        }
    }
}
</script>
{% endblock %}
