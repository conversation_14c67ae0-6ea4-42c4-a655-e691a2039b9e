{% extends 'customer/base.html' %}

{% block title %}Booking Details - BonAppetit Catering{% endblock %}

{% block page_title %}Booking Details{% endblock %}

{% block page_subtitle %}View detailed information about your booking{% endblock %}

{% block content %}
<div class="py-8">
            <!-- Header -->
            <div class="mb-8">
                <div class="flex items-center justify-between">
                    <div>
                        <a href="{% url 'savory_events:customer_bookings' %}" class="text-burnt-orange hover:text-opacity-80 text-sm font-medium mb-2 inline-flex items-center">
                            <svg class="mr-1 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                            </svg>
                            Back to Bookings
                        </a>
                        <h1 class="text-3xl font-bold text-charcoal mb-2">Booking #{{ booking.id }}</h1>
                        <p class="text-gray-600">{{ booking.get_event_type_display }} • {{ booking.guest_count }} guests</p>
                    </div>
                    <span class="inline-flex px-4 py-2 text-sm font-semibold rounded-full 
                        {% if booking.status == 'confirmed' %}bg-green-100 text-green-800
                        {% elif booking.status == 'pending' %}bg-yellow-100 text-yellow-800
                        {% elif booking.status == 'completed' %}bg-blue-100 text-blue-800
                        {% else %}bg-gray-100 text-gray-800{% endif %}">
                        {{ booking.get_status_display }}
                    </span>
                </div>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                <!-- Main details -->
                <div class="lg:col-span-2 space-y-6">
                    <!-- Event Details -->
                    <div class="bg-white rounded-lg shadow-md p-6">
                        <h2 class="text-xl font-semibold text-charcoal mb-4">Event Details</h2>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <p class="text-sm font-medium text-gray-500">Event Type</p>
                                <p class="text-charcoal">{{ booking.get_event_type_display }}</p>
                            </div>
                            <div>
                                <p class="text-sm font-medium text-gray-500">Date & Time</p>
                                <p class="text-charcoal">{{ booking.event_datetime|date:"M d, Y g:i A" }}</p>
                            </div>
                            <div>
                                <p class="text-sm font-medium text-gray-500">Number of Guests</p>
                                <p class="text-charcoal">{{ booking.guest_count }}</p>
                            </div>
                            <div>
                                <p class="text-sm font-medium text-gray-500">Venue</p>
                                <p class="text-charcoal">{{ booking.venue_address|default:"Not specified" }}</p>
                            </div>
                        </div>
                        {% if booking.special_requests %}
                            <div class="mt-4">
                                <p class="text-sm font-medium text-gray-500">Special Requests</p>
                                <p class="text-charcoal">{{ booking.special_requests }}</p>
                            </div>
                        {% endif %}
                    </div>

                    <!-- Menu Items -->
                    {% if menu_items %}
                        <div class="bg-white rounded-lg shadow-md p-6">
                            <h2 class="text-xl font-semibold text-charcoal mb-4">Selected Menu Items</h2>
                            <div class="space-y-4">
                                {% for item in menu_items %}
                                    <div class="flex items-center justify-between py-3 border-b border-gray-200 last:border-b-0">
                                        <div class="flex-1">
                                            <h3 class="font-medium text-charcoal">{{ item.menu_item.name }}</h3>
                                            <p class="text-sm text-gray-500">{{ item.menu_item.description }}</p>
                                            <p class="text-xs text-gray-400">{{ item.menu_item.get_category_display }}</p>
                                        </div>
                                        <div class="text-right ml-4">
                                            <p class="font-medium text-charcoal">${{ item.menu_item.price_per_person|floatformat:2 }} per person</p>
                                            <p class="text-sm text-gray-500">Total: ${{ item.subtotal|floatformat:2 }}</p>
                                        </div>
                                    </div>
                                {% endfor %}
                            </div>
                        </div>
                    {% endif %}

                    <!-- Sound System -->
                    {% if sound_systems %}
                        <div class="bg-white rounded-lg shadow-md p-6">
                            <h2 class="text-xl font-semibold text-charcoal mb-4">Sound System Services</h2>
                            <div class="space-y-4">
                                {% for system in sound_systems %}
                                    <div class="flex items-center justify-between py-3 border-b border-gray-200 last:border-b-0">
                                        <div class="flex-1">
                                            <h3 class="font-medium text-charcoal">{{ system.sound_system.name }}</h3>
                                            <p class="text-sm text-gray-500">{{ system.sound_system.description }}</p>
                                        </div>
                                        <div class="text-right ml-4">
                                            <p class="font-medium text-charcoal">${{ system.sound_system.price|floatformat:2 }}</p>
                                        </div>
                                    </div>
                                {% endfor %}
                            </div>
                        </div>
                    {% endif %}
                </div>

                <!-- Sidebar -->
                <div class="space-y-6">
                    <!-- Payment Summary -->
                    <div class="bg-white rounded-lg shadow-md p-6">
                        <h2 class="text-xl font-semibold text-charcoal mb-4">Payment Summary</h2>
                        
                        <div class="space-y-3">
                            <div class="flex justify-between">
                                <span class="text-gray-600">Subtotal</span>
                                <span class="text-charcoal">${{ booking.total_amount|floatformat:2 }}</span>
                            </div>
                            <div class="flex justify-between font-bold text-lg border-t pt-3">
                                <span class="text-charcoal">Total Amount</span>
                                <span class="text-charcoal">${{ booking.total_amount|floatformat:2 }}</span>
                            </div>
                        </div>

                        {% if payments %}
                            <div class="mt-6">
                                <h3 class="font-medium text-charcoal mb-3">Payment History</h3>
                                <div class="space-y-2">
                                    {% for payment in payments %}
                                        <div class="flex justify-between text-sm">
                                            <div>
                                                <p class="text-charcoal">{{ payment.get_payment_type_display }}</p>
                                                <p class="text-gray-500">{{ payment.created_at|date:"M d, Y" }}</p>
                                            </div>
                                            <p class="text-green-600 font-medium">${{ payment.amount|floatformat:2 }}</p>
                                        </div>
                                    {% endfor %}
                                </div>
                                
                                {% with booking.total_payments as total_paid %}
                                    {% if total_paid < booking.total_amount %}
                                        <div class="mt-4 p-3 bg-yellow-50 rounded-md">
                                            <p class="text-sm text-yellow-800">
                                                <strong>Remaining Balance:</strong> ${{ booking.remaining_balance|floatformat:2 }}
                                            </p>
                                        </div>
                                    {% else %}
                                        <div class="mt-4 p-3 bg-green-50 rounded-md">
                                            <p class="text-sm text-green-800">
                                                <strong>Fully Paid</strong>
                                            </p>
                                        </div>
                                    {% endif %}
                                {% endwith %}
                            </div>
                        {% else %}
                            <div class="mt-6 p-3 bg-yellow-50 rounded-md">
                                <p class="text-sm text-yellow-800">
                                    <strong>Payment Required:</strong> A 50% deposit is required to confirm your booking.
                                </p>
                            </div>
                        {% endif %}
                    </div>

                    <!-- Contact Information -->
                    <div class="bg-white rounded-lg shadow-md p-6">
                        <h2 class="text-xl font-semibold text-charcoal mb-4">Need Help?</h2>
                        <div class="space-y-3 text-sm">
                            <div class="flex items-center">
                                <svg class="h-4 w-4 mr-2 text-sage-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
                                </svg>
                                <span class="text-charcoal"><EMAIL></span>
                            </div>
                            <div class="flex items-center">
                                <svg class="h-4 w-4 mr-2 text-sage-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"/>
                                </svg>
                                <span class="text-charcoal">(555) 123-4567</span>
                            </div>
                        </div>
                        <div class="mt-4">
                            <a href="{% url 'savory_events:contact' %}" 
                               class="block w-full text-center px-3 py-2 border border-burnt-orange text-burnt-orange rounded-md hover:bg-burnt-orange hover:text-white transition-colors">
                                Contact Support
                            </a>
                        </div>
                    </div>

                    <!-- Booking Actions -->
                    <div class="bg-white rounded-lg shadow-md p-6">
                        <h2 class="text-xl font-semibold text-charcoal mb-4">Actions</h2>
                        <div class="space-y-3">
                            {% if booking.status == 'pending' %}
                                <p class="text-sm text-gray-600">
                                    Your booking is pending confirmation. We will contact you within 24 hours.
                                </p>
                            {% elif booking.status == 'confirmed' %}
                                <p class="text-sm text-green-600">
                                    Your booking is confirmed! We look forward to catering your event.
                                </p>
                            {% elif booking.status == 'completed' %}
                                <p class="text-sm text-blue-600">
                                    Thank you for choosing BonAppetit! We hope your event was a success.
                                </p>
                            {% endif %}
                            
                            <a href="{% url 'savory_events:contact' %}" 
                               class="block w-full text-center px-3 py-2 border border-sage-green text-sage-green rounded-md hover:bg-sage-green hover:text-white transition-colors">
                                Request Changes
                            </a>
                        </div>
                    </div>
                </div>
            </div>
</div>
{% endblock %}
