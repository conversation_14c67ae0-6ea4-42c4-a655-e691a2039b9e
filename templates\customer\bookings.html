{% extends 'customer/base.html' %}

{% block title %}My Bookings - BonAppetit Catering{% endblock %}

{% block page_title %}My Bookings{% endblock %}

{% block page_subtitle %}View and manage all your catering bookings{% endblock %}

{% block content %}
<div class="py-8">
            <!-- Header -->
            <div class="mb-8">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-3xl font-bold text-charcoal mb-2">My Bookings</h1>
                        <p class="text-gray-600">View and manage all your catering bookings.</p>
                    </div>
                    <a href="{% url 'savory_events:booking' %}" 
                       class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-burnt-orange hover:bg-opacity-80 transition-colors">
                        <svg class="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        New Booking
                    </a>
                </div>
            </div>

            <!-- Bookings list -->
            {% if bookings %}
                <div class="space-y-6">
                    {% for booking in bookings %}
                        <div class="bg-white rounded-lg shadow-md overflow-hidden">
                            <div class="px-6 py-4">
                                <div class="flex items-center justify-between mb-4">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0">
                                            {% if booking.status == 'confirmed' %}
                                                <div class="h-10 w-10 rounded-full bg-green-100 flex items-center justify-center">
                                                    <svg class="h-6 w-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                    </svg>
                                                </div>
                                            {% elif booking.status == 'pending' %}
                                                <div class="h-10 w-10 rounded-full bg-yellow-100 flex items-center justify-center">
                                                    <svg class="h-6 w-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                    </svg>
                                                </div>
                                            {% elif booking.status == 'completed' %}
                                                <div class="h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center">
                                                    <svg class="h-6 w-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                    </svg>
                                                </div>
                                            {% else %}
                                                <div class="h-10 w-10 rounded-full bg-gray-100 flex items-center justify-center">
                                                    <svg class="h-6 w-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                                    </svg>
                                                </div>
                                            {% endif %}
                                        </div>
                                        <div class="ml-4">
                                            <h3 class="text-lg font-medium text-charcoal">
                                                {{ booking.get_event_type_display }}
                                            </h3>
                                            <p class="text-sm text-gray-500">
                                                Booking #{{ booking.id }} • {{ booking.guest_count }} guests
                                            </p>
                                        </div>
                                    </div>
                                    <div class="text-right">
                                        <span class="inline-flex px-3 py-1 text-sm font-semibold rounded-full 
                                            {% if booking.status == 'confirmed' %}bg-green-100 text-green-800
                                            {% elif booking.status == 'pending' %}bg-yellow-100 text-yellow-800
                                            {% elif booking.status == 'completed' %}bg-blue-100 text-blue-800
                                            {% else %}bg-gray-100 text-gray-800{% endif %}">
                                            {{ booking.get_status_display }}
                                        </span>
                                    </div>
                                </div>

                                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
                                    <div>
                                        <p class="text-sm font-medium text-gray-500">Event Date</p>
                                        <p class="text-sm text-charcoal">{{ booking.event_datetime|date:"M d, Y" }}</p>
                                    </div>
                                    <div>
                                        <p class="text-sm font-medium text-gray-500">Event Time</p>
                                        <p class="text-sm text-charcoal">{{ booking.event_datetime|time:"g:i A" }}</p>
                                    </div>
                                    <div>
                                        <p class="text-sm font-medium text-gray-500">Total Amount</p>
                                        <p class="text-sm text-charcoal font-bold">${{ booking.total_amount|floatformat:2 }}</p>
                                    </div>
                                    <div>
                                        <p class="text-sm font-medium text-gray-500">Created</p>
                                        <p class="text-sm text-charcoal">{{ booking.created_at|date:"M d, Y" }}</p>
                                    </div>
                                </div>

                                {% if booking.venue_address %}
                                    <div class="mb-4">
                                        <p class="text-sm font-medium text-gray-500">Venue</p>
                                        <p class="text-sm text-charcoal">{{ booking.venue_address }}</p>
                                    </div>
                                {% endif %}

                                <div class="flex items-center justify-between pt-4 border-t border-gray-200">
                                    <div class="text-sm text-gray-500">
                                        {% if booking.special_requests %}
                                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                                Special Requests
                                            </span>
                                        {% endif %}
                                    </div>
                                    <a href="{% url 'savory_events:customer_booking_detail' booking.id %}" 
                                       class="inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded-md text-burnt-orange hover:bg-burnt-orange hover:text-white transition-colors">
                                        View Details
                                        <svg class="ml-1 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                        </svg>
                                    </a>
                                </div>
                            </div>
                        </div>
                    {% endfor %}
                </div>
            {% else %}
                <div class="bg-white rounded-lg shadow-md">
                    <div class="px-6 py-12 text-center">
                        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                        </svg>
                        <h3 class="mt-2 text-sm font-medium text-gray-900">No bookings yet</h3>
                        <p class="mt-1 text-sm text-gray-500">Get started by creating your first catering booking.</p>
                        <div class="mt-6">
                            <a href="{% url 'savory_events:booking' %}" 
                               class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-burnt-orange hover:bg-opacity-80 transition-colors">
                                <svg class="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                </svg>
                                Create New Booking
                            </a>
                        </div>
                    </div>
                </div>
            {% endif %}
</div>
{% endblock %}
