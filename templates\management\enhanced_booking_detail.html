{% extends 'management/base.html' %}

{% block title %}Enhanced Booking Management - {{ booking.customer.name }}{% endblock %}
{% block page_title %}Enhanced Booking Management{% endblock %}

{% block extra_head %}
<script src="https://unpkg.com/htmx.org@1.9.10"></script>
<script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>
{% endblock %}

{% block content %}
<!-- Enhanced Booking Header -->
<div class="bg-white shadow rounded-lg mb-6" x-data="bookingManager()">
    <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex items-center justify-between">
            <div>
                <h2 class="text-2xl font-bold text-gray-900">{{ booking.customer.name }}</h2>
                <p class="text-sm text-gray-500">
                    {{ booking.get_event_type_display }} • {{ booking.event_date|date:"M d, Y" }} at {{ booking.event_date|time:"h:i A" }}
                    • {{ booking.guest_count }} guests
                </p>
                <div class="flex items-center space-x-4 mt-2">
                    <span class="inline-flex px-3 py-1 text-sm font-medium rounded-full {{ booking.get_status_display_class }}">
                        {{ booking.get_status_display }}
                    </span>
                    <span class="inline-flex px-3 py-1 text-sm font-medium rounded-full 
                        {% if booking.priority == 'urgent' %}bg-red-100 text-red-800
                        {% elif booking.priority == 'high' %}bg-orange-100 text-orange-800
                        {% elif booking.priority == 'normal' %}bg-blue-100 text-blue-800
                        {% else %}bg-gray-100 text-gray-800{% endif %}">
                        {{ booking.get_priority_display }}
                    </span>
                    {% if booking.assigned_coordinator %}
                    <span class="inline-flex items-center px-3 py-1 text-sm font-medium rounded-full bg-purple-100 text-purple-800">
                        <i class="fas fa-user-tie mr-1"></i>
                        {{ booking.assigned_coordinator.get_full_name }}
                    </span>
                    {% endif %}
                </div>
            </div>
            <div class="flex space-x-3">
                <button @click="showEditModal = true" 
                        class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                    <i class="fas fa-edit mr-2"></i>Edit Booking
                </button>
                <button @click="showStatusModal = true" 
                        class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                    <i class="fas fa-sync mr-2"></i>Update Status
                </button>
                <div class="relative" x-data="{ open: false }">
                    <button @click="open = !open" 
                            class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                        <i class="fas fa-ellipsis-v mr-2"></i>More Actions
                    </button>
                    <div x-show="open" @click.away="open = false" 
                         class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg z-10">
                        <div class="py-1">
                            <button @click="showNoteModal = true; open = false" 
                                    class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                <i class="fas fa-sticky-note mr-2"></i>Add Note
                            </button>
                            <button @click="showReminderModal = true; open = false" 
                                    class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                <i class="fas fa-bell mr-2"></i>Add Reminder
                            </button>
                            <button @click="showStaffModal = true; open = false" 
                                    class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                <i class="fas fa-users mr-2"></i>Assign Staff
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Progress Bar -->
    <div class="px-6 py-3 bg-gray-50">
        <div class="flex items-center justify-between text-sm text-gray-600 mb-2">
            <span>Booking Progress</span>
            <span>{{ booking.get_completion_percentage }}%</span>
        </div>
        <div class="w-full bg-gray-200 rounded-full h-2">
            <div class="bg-blue-600 h-2 rounded-full" style="width: {{ booking.get_completion_percentage }}%"></div>
        </div>
    </div>
</div>

<!-- Main Content Grid -->
<div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
    <!-- Left Column - Main Details -->
    <div class="lg:col-span-2 space-y-6">
        
        <!-- Event Details Card -->
        <div class="bg-white shadow rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Event Details</h3>
            </div>
            <div class="px-6 py-4">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Event Type</label>
                        <p class="mt-1 text-sm text-gray-900">{{ booking.get_event_type_display }}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Date & Time</label>
                        <p class="mt-1 text-sm text-gray-900">{{ booking.event_date|date:"M d, Y g:i A" }}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Guest Count</label>
                        <p class="mt-1 text-sm text-gray-900">{{ booking.guest_count }} guests</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Days Until Event</label>
                        <p class="mt-1 text-sm text-gray-900">
                            {% if booking.get_days_until_event %}
                                {{ booking.get_days_until_event }} days
                                {% if booking.is_upcoming %}
                                    <span class="text-orange-600 font-medium">(Upcoming!)</span>
                                {% endif %}
                            {% else %}
                                N/A
                            {% endif %}
                        </p>
                    </div>
                </div>
                <div class="mt-4">
                    <label class="block text-sm font-medium text-gray-700">Venue Address</label>
                    <p class="mt-1 text-sm text-gray-900">{{ booking.venue_address }}</p>
                </div>
                {% if booking.special_requests %}
                <div class="mt-4">
                    <label class="block text-sm font-medium text-gray-700">Special Requests</label>
                    <p class="mt-1 text-sm text-gray-900">{{ booking.special_requests }}</p>
                </div>
                {% endif %}
                {% if booking.internal_notes %}
                <div class="mt-4">
                    <label class="block text-sm font-medium text-gray-700">Internal Notes</label>
                    <p class="mt-1 text-sm text-gray-900 bg-yellow-50 p-3 rounded-md">{{ booking.internal_notes }}</p>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Menu Items Card -->
        {% if booking.menu_items.exists %}
        <div class="bg-white shadow rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Selected Menu Items</h3>
            </div>
            <div class="px-6 py-4">
                <div class="space-y-3">
                    {% for booking_item in booking.menu_items.all %}
                    <div class="flex items-center justify-between border-b border-gray-100 pb-3">
                        <div>
                            <h4 class="font-medium text-gray-900">{{ booking_item.menu_item.name }}</h4>
                            <p class="text-sm text-gray-600">{{ booking_item.menu_item.get_category_display }}</p>
                            <p class="text-xs text-gray-500">{{ booking_item.menu_item.description|truncatewords:10 }}</p>
                        </div>
                        <div class="text-right">
                            <p class="font-medium text-gray-900">${{ booking_item.get_total_price }}</p>
                            <p class="text-sm text-gray-600">
                                ${{ booking_item.menu_item.price_per_person }} × {{ booking.guest_count }} guests
                            </p>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Sound System Card -->
        {% if booking.sound_system_booking %}
        <div class="bg-white shadow rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Sound System Service</h3>
            </div>
            <div class="px-6 py-4">
                <div class="flex items-center justify-between">
                    <div>
                        <h4 class="font-medium text-gray-900">{{ booking.sound_system_booking.sound_system.name }}</h4>
                        <p class="text-sm text-gray-600">{{ booking.sound_system_booking.sound_system.get_package_type_display }}</p>
                        <p class="text-xs text-gray-500">{{ booking.sound_system_booking.sound_system.description|truncatewords:15 }}</p>
                    </div>
                    <div class="text-right">
                        <p class="font-medium text-gray-900">${{ booking.sound_system_booking.sound_system.price }}</p>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Notes Section -->
        <div class="bg-white shadow rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
                <h3 class="text-lg font-medium text-gray-900">Notes & Comments</h3>
                <button @click="showNoteModal = true" 
                        class="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm">
                    <i class="fas fa-plus mr-1"></i>Add Note
                </button>
            </div>
            <div class="px-6 py-4">
                {% if booking.notes.exists %}
                    <div class="space-y-4">
                        {% for note in booking.notes.all %}
                        <div class="border-l-4 
                            {% if note.priority == 'urgent' %}border-red-500
                            {% elif note.priority == 'high' %}border-orange-500
                            {% elif note.priority == 'normal' %}border-blue-500
                            {% else %}border-gray-500{% endif %} pl-4">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-2">
                                    <span class="font-medium text-gray-900">{{ note.staff_user.get_full_name }}</span>
                                    <span class="text-sm text-gray-500">{{ note.created_at|date:"M d, Y g:i A" }}</span>
                                    <span class="inline-flex px-2 py-1 text-xs font-medium rounded-full 
                                        {% if note.priority == 'urgent' %}bg-red-100 text-red-800
                                        {% elif note.priority == 'high' %}bg-orange-100 text-orange-800
                                        {% elif note.priority == 'normal' %}bg-blue-100 text-blue-800
                                        {% else %}bg-gray-100 text-gray-800{% endif %}">
                                        {{ note.get_priority_display }}
                                    </span>
                                    {% if note.is_internal %}
                                    <span class="inline-flex px-2 py-1 text-xs font-medium rounded-full bg-yellow-100 text-yellow-800">
                                        Internal
                                    </span>
                                    {% endif %}
                                </div>
                            </div>
                            <p class="mt-2 text-sm text-gray-700">{{ note.note }}</p>
                        </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <p class="text-gray-500 text-sm">No notes added yet.</p>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Right Column - Sidebar -->
    <div class="space-y-6">
        
        <!-- Financial Summary -->
        <div class="bg-white shadow rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Financial Summary</h3>
            </div>
            <div class="px-6 py-4">
                <div class="space-y-3">
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-600">Subtotal:</span>
                        <span class="text-sm font-medium text-gray-900">${{ booking.total_amount }}</span>
                    </div>
                    {% if booking.discount_amount %}
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-600">Discount:</span>
                        <span class="text-sm font-medium text-red-600">-${{ booking.discount_amount }}</span>
                    </div>
                    {% endif %}
                    <div class="flex justify-between border-t border-gray-200 pt-3">
                        <span class="text-base font-medium text-gray-900">Final Total:</span>
                        <span class="text-base font-bold text-gray-900">${{ booking.get_final_amount }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-600">Total Paid:</span>
                        <span class="text-sm font-medium text-green-600">${{ financial_summary.total_paid }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-600">Remaining:</span>
                        <span class="text-sm font-medium text-orange-600">${{ financial_summary.remaining_balance }}</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Customer Information -->
        <div class="bg-white shadow rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Customer Information</h3>
            </div>
            <div class="px-6 py-4">
                <div class="space-y-3">
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Name</label>
                        <p class="mt-1 text-sm text-gray-900">{{ booking.customer.name }}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Email</label>
                        <p class="mt-1 text-sm text-gray-900">
                            <a href="mailto:{{ booking.customer.email }}" class="text-blue-600 hover:text-blue-800">
                                {{ booking.customer.email }}
                            </a>
                        </p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Phone</label>
                        <p class="mt-1 text-sm text-gray-900">
                            <a href="tel:{{ booking.customer.phone }}" class="text-blue-600 hover:text-blue-800">
                                {{ booking.customer.phone }}
                            </a>
                        </p>
                    </div>
                </div>
                <div class="mt-4">
                    <a href="{% url 'savory_events:management_customer_detail' booking.customer.id %}" 
                       class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                        View Customer Profile →
                    </a>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="bg-white shadow rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Quick Actions</h3>
            </div>
            <div class="px-6 py-4">
                <div class="space-y-2">
                    <button @click="showReminderModal = true" 
                            class="w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded">
                        <i class="fas fa-bell mr-2 text-blue-500"></i>Add Reminder
                    </button>
                    <button @click="showStaffModal = true" 
                            class="w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded">
                        <i class="fas fa-users mr-2 text-green-500"></i>Assign Staff
                    </button>
                    <a href="{% url 'savory_events:management_add_payment' booking.id %}" 
                       class="block w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded">
                        <i class="fas fa-credit-card mr-2 text-purple-500"></i>Record Payment
                    </a>
                    <button onclick="window.print()" 
                            class="w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded">
                        <i class="fas fa-print mr-2 text-gray-500"></i>Print Details
                    </button>
                </div>
            </div>
        </div>

        <!-- Status History -->
        {% if booking.status_history.exists %}
        <div class="bg-white shadow rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Status History</h3>
            </div>
            <div class="px-6 py-4">
                <div class="space-y-3">
                    {% for history in booking.status_history.all %}
                    <div class="text-sm">
                        <div class="flex items-center justify-between">
                            <span class="font-medium text-gray-900">
                                {{ history.old_status|title }} → {{ history.new_status|title }}
                            </span>
                            <span class="text-gray-500">{{ history.created_at|date:"M d, g:i A" }}</span>
                        </div>
                        <p class="text-gray-600">by {{ history.changed_by.get_full_name }}</p>
                        {% if history.reason %}
                        <p class="text-gray-500 italic">{{ history.reason }}</p>
                        {% endif %}
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
        {% endif %}
    </div>
</div>

<!-- Edit Booking Modal -->
<div x-show="showEditModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50" x-cloak>
    <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900">Edit Booking Details</h3>
                <button @click="showEditModal = false" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form hx-post="{% url 'savory_events:management_booking_edit' booking.id %}"
                  hx-target="#edit-response"
                  hx-swap="innerHTML">
                {% csrf_token %}
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Event Date</label>
                        <input type="date" name="event_date" value="{{ booking.event_date|date:'Y-m-d' }}"
                               class="mt-1 block w-full border-gray-300 rounded-md shadow-sm">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Event Time</label>
                        <input type="time" name="event_time" value="{{ booking.event_date|time:'H:i' }}"
                               class="mt-1 block w-full border-gray-300 rounded-md shadow-sm">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Guest Count</label>
                        <input type="number" name="guest_count" value="{{ booking.guest_count }}" min="1"
                               class="mt-1 block w-full border-gray-300 rounded-md shadow-sm">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Priority</label>
                        <select name="priority" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm">
                            {% for value, label in booking.PRIORITY_CHOICES %}
                            <option value="{{ value }}" {% if booking.priority == value %}selected{% endif %}>{{ label }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="md:col-span-2">
                        <label class="block text-sm font-medium text-gray-700">Venue Address</label>
                        <textarea name="venue_address" rows="3"
                                  class="mt-1 block w-full border-gray-300 rounded-md shadow-sm">{{ booking.venue_address }}</textarea>
                    </div>
                    <div class="md:col-span-2">
                        <label class="block text-sm font-medium text-gray-700">Discount Amount</label>
                        <input type="number" name="discount_amount" value="{{ booking.discount_amount }}" step="0.01" min="0"
                               class="mt-1 block w-full border-gray-300 rounded-md shadow-sm">
                    </div>
                    <div class="md:col-span-2">
                        <label class="block text-sm font-medium text-gray-700">Internal Notes</label>
                        <textarea name="internal_notes" rows="3"
                                  class="mt-1 block w-full border-gray-300 rounded-md shadow-sm">{{ booking.internal_notes }}</textarea>
                    </div>
                </div>
                <div id="edit-response" class="mt-4"></div>
                <div class="flex justify-end space-x-3 mt-6">
                    <button type="button" @click="showEditModal = false"
                            class="bg-gray-300 hover:bg-gray-400 text-gray-800 px-4 py-2 rounded">
                        Cancel
                    </button>
                    <button type="submit"
                            class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded">
                        Save Changes
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Status Update Modal -->
<div x-show="showStatusModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50" x-cloak>
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900">Update Booking Status</h3>
                <button @click="showStatusModal = false" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form hx-post="{% url 'savory_events:management_update_booking_status' booking.id %}"
                  hx-target="#status-response"
                  hx-swap="innerHTML">
                {% csrf_token %}
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700">New Status</label>
                        <select name="status" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm">
                            {% for value, label in status_choices %}
                            <option value="{{ value }}" {% if booking.status == value %}selected{% endif %}>{{ label }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Reason (Optional)</label>
                        <textarea name="reason" rows="3"
                                  class="mt-1 block w-full border-gray-300 rounded-md shadow-sm"
                                  placeholder="Reason for status change..."></textarea>
                    </div>
                </div>
                <div id="status-response" class="mt-4"></div>
                <div class="flex justify-end space-x-3 mt-6">
                    <button type="button" @click="showStatusModal = false"
                            class="bg-gray-300 hover:bg-gray-400 text-gray-800 px-4 py-2 rounded">
                        Cancel
                    </button>
                    <button type="submit"
                            class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded">
                        Update Status
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Add Note Modal -->
<div x-show="showNoteModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50" x-cloak>
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900">Add Note</h3>
                <button @click="showNoteModal = false" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form hx-post="{% url 'savory_events:management_booking_add_note' booking.id %}"
                  hx-target="#note-response"
                  hx-swap="innerHTML">
                {% csrf_token %}
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Note</label>
                        <textarea name="note" rows="4" required
                                  class="mt-1 block w-full border-gray-300 rounded-md shadow-sm"
                                  placeholder="Enter your note..."></textarea>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Priority</label>
                        <select name="priority" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm">
                            <option value="low">Low</option>
                            <option value="normal" selected>Normal</option>
                            <option value="high">High</option>
                            <option value="urgent">Urgent</option>
                        </select>
                    </div>
                    <div>
                        <label class="flex items-center">
                            <input type="checkbox" name="is_internal" value="true" checked
                                   class="rounded border-gray-300 text-blue-600 shadow-sm">
                            <span class="ml-2 text-sm text-gray-700">Internal note (not visible to customer)</span>
                        </label>
                    </div>
                </div>
                <div id="note-response" class="mt-4"></div>
                <div class="flex justify-end space-x-3 mt-6">
                    <button type="button" @click="showNoteModal = false"
                            class="bg-gray-300 hover:bg-gray-400 text-gray-800 px-4 py-2 rounded">
                        Cancel
                    </button>
                    <button type="submit"
                            class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded">
                        Add Note
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function bookingManager() {
    return {
        showEditModal: false,
        showStatusModal: false,
        showNoteModal: false,
        showReminderModal: false,
        showStaffModal: false,

        closeAllModals() {
            this.showEditModal = false;
            this.showStatusModal = false;
            this.showNoteModal = false;
            this.showReminderModal = false;
            this.showStaffModal = false;
        }
    }
}

// Handle HTMX responses
document.body.addEventListener('htmx:afterRequest', function(event) {
    if (event.detail.successful) {
        // Reload page after successful operations
        setTimeout(() => {
            window.location.reload();
        }, 1500);
    }
});
</script>

<style>
[x-cloak] { display: none !important; }
</style>

{% endblock %}
