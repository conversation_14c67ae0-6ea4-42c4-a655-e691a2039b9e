<!-- Chat Widget Component -->
<div id="chat-widget" x-data="chatWidget()" x-show="isVisible" 
     class="fixed bottom-4 right-4 z-50" 
     style="display: none;"
     x-transition:enter="transition ease-out duration-300"
     x-transition:enter-start="opacity-0 transform translate-y-4"
     x-transition:enter-end="opacity-100 transform translate-y-0"
     x-transition:leave="transition ease-in duration-200"
     x-transition:leave-start="opacity-100 transform translate-y-0"
     x-transition:leave-end="opacity-0 transform translate-y-4">
    
    <!-- Chat Window -->
    <div class="bg-white rounded-lg shadow-2xl w-80 h-96 flex flex-col" 
         x-show="isOpen"
         x-transition:enter="transition ease-out duration-300"
         x-transition:enter-start="opacity-0 transform scale-95"
         x-transition:enter-end="opacity-100 transform scale-100"
         x-transition:leave="transition ease-in duration-200"
         x-transition:leave-start="opacity-100 transform scale-100"
         x-transition:leave-end="opacity-0 transform scale-95">
        
        <!-- Chat Header -->
        <div class="bg-deep-olive text-white px-4 py-3 rounded-t-lg flex items-center justify-between">
            <div class="flex items-center">
                <div class="w-8 h-8 bg-burnt-orange rounded-full flex items-center justify-center mr-2">
                    <i class="fas fa-robot text-white text-sm"></i>
                </div>
                <div>
                    <h3 class="text-sm font-semibold">BonAppetit Support</h3>
                    <p class="text-xs text-sage-green">Online now</p>
                </div>
            </div>
            <button @click="toggleChat()" 
                    class="text-sage-green hover:text-white transition-colors">
                <i class="fas fa-minus"></i>
            </button>
        </div>

        <!-- Chat Messages -->
        <div class="flex-1 overflow-y-auto p-3 bg-gray-50" 
             x-ref="messagesContainer">
            
            <!-- Welcome Message -->
            <div class="mb-3" x-show="messages.length === 0">
                <div class="flex items-start space-x-2">
                    <div class="w-6 h-6 bg-burnt-orange rounded-full flex items-center justify-center flex-shrink-0">
                        <i class="fas fa-robot text-white text-xs"></i>
                    </div>
                    <div class="bg-white rounded-lg p-2 shadow-sm text-xs text-charcoal max-w-48">
                        Hi! I'm here to help with your catering questions. What can I assist you with today?
                    </div>
                </div>
            </div>

            <!-- Messages -->
            <template x-for="message in messages" :key="message.id">
                <div class="mb-3" :class="message.type === 'user' ? 'flex justify-end' : 'flex justify-start'">
                    <div class="flex items-start space-x-2 max-w-48" 
                         :class="message.type === 'user' ? 'flex-row-reverse space-x-reverse' : ''">
                        
                        <!-- Avatar -->
                        <div class="w-6 h-6 rounded-full flex items-center justify-center flex-shrink-0"
                             :class="message.type === 'user' ? 'bg-sage-green' : 'bg-burnt-orange'">
                            <i :class="message.type === 'user' ? 'fas fa-user text-white text-xs' : 'fas fa-robot text-white text-xs'"></i>
                        </div>
                        
                        <!-- Message Bubble -->
                        <div class="rounded-lg p-2 shadow-sm text-xs"
                             :class="message.type === 'user' ? 'bg-sage-green text-white' : 'bg-white text-charcoal'">
                            <p x-text="message.content"></p>
                        </div>
                    </div>
                </div>
            </template>

            <!-- Typing Indicator -->
            <div class="mb-3 flex justify-start" x-show="isTyping">
                <div class="flex items-start space-x-2">
                    <div class="w-6 h-6 bg-burnt-orange rounded-full flex items-center justify-center flex-shrink-0">
                        <i class="fas fa-robot text-white text-xs"></i>
                    </div>
                    <div class="bg-white rounded-lg p-2 shadow-sm">
                        <div class="flex space-x-1">
                            <div class="w-1 h-1 bg-gray-400 rounded-full animate-bounce"></div>
                            <div class="w-1 h-1 bg-gray-400 rounded-full animate-bounce" style="animation-delay: 0.1s"></div>
                            <div class="w-1 h-1 bg-gray-400 rounded-full animate-bounce" style="animation-delay: 0.2s"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions (only show when no messages) -->
        <div class="px-3 py-2 bg-gray-100 border-t border-gray-200" x-show="messages.length === 0">
            <div class="flex flex-wrap gap-1">
                <button @click="sendQuickAction('show_menu')"
                        class="px-2 py-1 bg-sage-green text-white text-xs rounded hover:bg-deep-olive transition-colors"
                        :disabled="isLoading">
                    Menu
                </button>
                <button @click="sendQuickAction('pricing_info')"
                        class="px-2 py-1 bg-sage-green text-white text-xs rounded hover:bg-deep-olive transition-colors"
                        :disabled="isLoading">
                    Pricing
                </button>
                <button @click="sendQuickAction('book_event')"
                        class="px-2 py-1 bg-sage-green text-white text-xs rounded hover:bg-deep-olive transition-colors"
                        :disabled="isLoading">
                    Book Event
                </button>
            </div>
        </div>

        <!-- Message Input -->
        <div class="p-3 border-t border-gray-200 bg-white rounded-b-lg">
            <form @submit.prevent="sendMessage()" class="flex space-x-2">
                <input type="text" 
                       x-model="currentMessage"
                       placeholder="Type a message..."
                       class="flex-1 px-2 py-1 text-sm border border-sage-green rounded focus:outline-none focus:ring-1 focus:ring-burnt-orange"
                       :disabled="isLoading"
                       maxlength="300">
                <button type="submit" 
                        :disabled="isLoading || !currentMessage.trim()"
                        class="px-2 py-1 bg-burnt-orange text-white rounded hover:bg-opacity-80 transition-colors disabled:opacity-50 text-sm">
                    <i class="fas fa-paper-plane" x-show="!isLoading"></i>
                    <i class="fas fa-spinner fa-spin" x-show="isLoading"></i>
                </button>
            </form>
        </div>
    </div>

    <!-- Chat Toggle Button -->
    <button @click="toggleChat()" 
            x-show="!isOpen"
            class="bg-burnt-orange text-white w-14 h-14 rounded-full shadow-lg hover:bg-opacity-80 transition-all duration-300 flex items-center justify-center group"
            x-transition:enter="transition ease-out duration-300"
            x-transition:enter-start="opacity-0 transform scale-95"
            x-transition:enter-end="opacity-100 transform scale-100">
        <i class="fas fa-comments text-xl group-hover:scale-110 transition-transform"></i>
        
        <!-- Notification Badge -->
        <div x-show="hasNewMessages" 
             class="absolute -top-1 -right-1 w-4 h-4 bg-red-500 text-white text-xs rounded-full flex items-center justify-center animate-pulse">
            !
        </div>
    </button>
</div>

<script>
function chatWidget() {
    return {
        isVisible: true,
        isOpen: false,
        messages: [],
        currentMessage: '',
        isLoading: false,
        isTyping: false,
        hasNewMessages: false,
        sessionId: null,

        init() {
            // Initialize session
            this.initializeSession();
            
            // Auto-scroll to bottom when new messages arrive
            this.$watch('messages', () => {
                this.$nextTick(() => {
                    this.scrollToBottom();
                });
            });

            // Show notification for new messages when chat is closed
            this.$watch('messages', (newMessages, oldMessages) => {
                if (!this.isOpen && newMessages.length > oldMessages.length) {
                    this.hasNewMessages = true;
                }
            });
        },

        async initializeSession() {
            try {
                const response = await fetch('/chat/session/', {
                    method: 'POST',
                    headers: {
                        'X-CSRFToken': this.getCsrfToken(),
                    },
                });
                
                if (response.ok) {
                    const data = await response.json();
                    this.sessionId = data.session_id;
                }
            } catch (error) {
                console.error('Error initializing chat session:', error);
            }
        },

        toggleChat() {
            this.isOpen = !this.isOpen;
            if (this.isOpen) {
                this.hasNewMessages = false;
                this.loadMessages();
            }
        },

        async loadMessages() {
            try {
                const response = await fetch('/chat/messages/', {
                    method: 'GET',
                    headers: {
                        'X-CSRFToken': this.getCsrfToken(),
                    },
                });
                
                if (response.ok) {
                    const data = await response.json();
                    this.messages = data.messages;
                }
            } catch (error) {
                console.error('Error loading messages:', error);
            }
        },

        async sendMessage() {
            if (!this.currentMessage.trim() || this.isLoading) return;

            const message = this.currentMessage.trim();
            this.currentMessage = '';
            this.isLoading = true;
            this.isTyping = true;

            try {
                const response = await fetch('/chat/send/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': this.getCsrfToken(),
                    },
                    body: JSON.stringify({
                        message: message
                    }),
                });

                if (response.ok) {
                    const data = await response.json();
                    this.messages.push(data.user_message);
                    
                    setTimeout(() => {
                        this.isTyping = false;
                        this.messages.push(data.bot_response);
                    }, 800);
                } else {
                    throw new Error('Failed to send message');
                }
            } catch (error) {
                console.error('Error sending message:', error);
                this.isTyping = false;
                this.messages.push({
                    id: Date.now(),
                    content: 'Sorry, I encountered an error. Please try again.',
                    type: 'bot',
                    timestamp: new Date().toISOString()
                });
            } finally {
                this.isLoading = false;
            }
        },

        async sendQuickAction(action) {
            if (this.isLoading) return;

            this.isLoading = true;
            this.isTyping = true;

            try {
                const response = await fetch('/chat/send/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': this.getCsrfToken(),
                    },
                    body: JSON.stringify({
                        action: action
                    }),
                });

                if (response.ok) {
                    const data = await response.json();
                    this.messages.push(data.user_message);
                    
                    setTimeout(() => {
                        this.isTyping = false;
                        this.messages.push(data.bot_response);
                    }, 600);
                } else {
                    throw new Error('Failed to send quick action');
                }
            } catch (error) {
                console.error('Error sending quick action:', error);
                this.isTyping = false;
            } finally {
                this.isLoading = false;
            }
        },

        scrollToBottom() {
            const container = this.$refs.messagesContainer;
            if (container) {
                container.scrollTop = container.scrollHeight;
            }
        },

        getCsrfToken() {
            const token = document.querySelector('[name=csrfmiddlewaretoken]');
            return token ? token.value : '';
        }
    }
}
</script>
