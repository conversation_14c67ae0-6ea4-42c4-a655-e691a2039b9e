{% extends 'management/base.html' %}

{% block title %}Booking Details{% endblock %}
{% block page_title %}Booking Details{% endblock %}

{% block content %}
<!-- Booking Header -->
<div class="bg-white shadow rounded-lg mb-6">
    <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex items-center justify-between">
            <div>
                <h2 class="text-xl font-semibold text-gray-900">{{ booking.customer.name }}</h2>
                <p class="text-sm text-gray-500">{{ booking.get_event_type_display }} • {{ booking.event_date|date:"M d, Y" }} at {{ booking.event_date|time:"h:i A" }}</p>
            </div>
            <div class="flex space-x-3">
                <span class="inline-flex px-3 py-1 text-sm font-medium rounded-full 
                    {% if booking.status == 'pending' %}bg-yellow-100 text-yellow-800
                    {% elif booking.status == 'confirmed' %}bg-blue-100 text-blue-800
                    {% elif booking.status == 'partial_paid' %}bg-orange-100 text-orange-800
                    {% elif booking.status == 'fully_paid' %}bg-green-100 text-green-800
                    {% elif booking.status == 'completed' %}bg-gray-100 text-gray-800
                    {% else %}bg-red-100 text-red-800{% endif %}">
                    {{ booking.get_status_display }}
                </span>
            </div>
        </div>
    </div>
</div>

<!-- Main Content Grid -->
<div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
    
    <!-- Main Details -->
    <div class="lg:col-span-2 space-y-6">
        
        <!-- Event Information -->
        <div class="bg-white shadow rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Event Information</h3>
            </div>
            <div class="px-6 py-4">
                <dl class="grid grid-cols-1 gap-4 sm:grid-cols-2">
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Event Type</dt>
                        <dd class="mt-1 text-sm text-gray-900">{{ booking.get_event_type_display }}</dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Guest Count</dt>
                        <dd class="mt-1 text-sm text-gray-900">{{ booking.guest_count }} guests</dd>
                    </div>
                    <div class="sm:col-span-2">
                        <dt class="text-sm font-medium text-gray-500">Venue Address</dt>
                        <dd class="mt-1 text-sm text-gray-900">{{ booking.venue_address }}</dd>
                    </div>
                    {% if booking.special_requests %}
                    <div class="sm:col-span-2">
                        <dt class="text-sm font-medium text-gray-500">Special Requests</dt>
                        <dd class="mt-1 text-sm text-gray-900">{{ booking.special_requests }}</dd>
                    </div>
                    {% endif %}
                </dl>
            </div>
        </div>

        <!-- Customer Information -->
        <div class="bg-white shadow rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Customer Information</h3>
            </div>
            <div class="px-6 py-4">
                <dl class="grid grid-cols-1 gap-4 sm:grid-cols-2">
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Name</dt>
                        <dd class="mt-1 text-sm text-gray-900">{{ booking.customer.name }}</dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Email</dt>
                        <dd class="mt-1 text-sm text-gray-900">
                            <a href="mailto:{{ booking.customer.email }}" class="text-deep-olive hover:text-burnt-orange">
                                {{ booking.customer.email }}
                            </a>
                        </dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Phone</dt>
                        <dd class="mt-1 text-sm text-gray-900">
                            <a href="tel:{{ booking.customer.phone }}" class="text-deep-olive hover:text-burnt-orange">
                                {{ booking.customer.phone }}
                            </a>
                        </dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Customer Since</dt>
                        <dd class="mt-1 text-sm text-gray-900">{{ booking.customer.created_at|date:"M d, Y" }}</dd>
                    </div>
                </dl>
                <div class="mt-4">
                    <a href="{% url 'savory_events:management_customer_detail' booking.customer.id %}" 
                       class="text-sm font-medium text-deep-olive hover:text-burnt-orange">
                        View Customer Profile →
                    </a>
                </div>
            </div>
        </div>

        <!-- Menu Items -->
        {% if booking.menu_items.exists %}
        <div class="bg-white shadow rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Menu Items</h3>
            </div>
            <div class="overflow-hidden">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Price/Person</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Quantity</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {% for item in booking.menu_items.all %}
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">{{ item.menu_item.name }}</div>
                                <div class="text-sm text-gray-500">{{ item.menu_item.description|truncatechars:50 }}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {{ item.menu_item.get_category_display }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                ${{ item.menu_item.price_per_person|floatformat:2 }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {{ item.quantity }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                ${{ item.get_total_price|floatformat:2 }}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
        {% endif %}

        <!-- Sound System -->
        {% if booking.sound_system_booking %}
        <div class="bg-white shadow rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Sound System Service</h3>
            </div>
            <div class="px-6 py-4">
                <div class="flex items-center justify-between">
                    <div>
                        <h4 class="text-sm font-medium text-gray-900">{{ booking.sound_system_booking.sound_system.name }}</h4>
                        <p class="text-sm text-gray-500">{{ booking.sound_system_booking.sound_system.get_package_type_display }}</p>
                        <p class="text-sm text-gray-500">{{ booking.sound_system_booking.sound_system.description }}</p>
                    </div>
                    <div class="text-right">
                        <div class="text-lg font-medium text-gray-900">
                            ${{ booking.sound_system_booking.sound_system.price|floatformat:2 }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Payment History -->
        {% if booking.payments.exists %}
        <div class="bg-white shadow rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Payment History</h3>
            </div>
            <div class="overflow-hidden">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Notes</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {% for payment in booking.payments.all %}
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {{ payment.payment_date|date:"M d, Y" }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {{ payment.get_payment_type_display }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                ${{ payment.amount|floatformat:2 }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {{ payment.notes|default:"-" }}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
        {% endif %}
    </div>

    <!-- Sidebar -->
    <div class="space-y-6">
        
        <!-- Financial Summary -->
        <div class="bg-white shadow rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Financial Summary</h3>
            </div>
            <div class="px-6 py-4">
                <dl class="space-y-4">
                    <div class="flex justify-between">
                        <dt class="text-sm font-medium text-gray-500">Total Amount</dt>
                        <dd class="text-sm font-medium text-gray-900">${{ financial_summary.total_amount|floatformat:2 }}</dd>
                    </div>
                    <div class="flex justify-between">
                        <dt class="text-sm font-medium text-gray-500">Total Paid</dt>
                        <dd class="text-sm font-medium text-green-600">${{ financial_summary.total_paid|floatformat:2 }}</dd>
                    </div>
                    <div class="flex justify-between border-t border-gray-200 pt-4">
                        <dt class="text-sm font-medium text-gray-500">Remaining Balance</dt>
                        <dd class="text-sm font-medium {% if financial_summary.remaining_balance > 0 %}text-red-600{% else %}text-green-600{% endif %}">
                            ${{ financial_summary.remaining_balance|floatformat:2 }}
                        </dd>
                    </div>
                    <div class="flex justify-between">
                        <dt class="text-sm font-medium text-gray-500">Deposit Required</dt>
                        <dd class="text-sm font-medium text-gray-900">${{ financial_summary.deposit_required|floatformat:2 }}</dd>
                    </div>
                    <div class="flex justify-between">
                        <dt class="text-sm font-medium text-gray-500">Deposit Status</dt>
                        <dd class="text-sm font-medium {% if financial_summary.is_deposit_paid %}text-green-600{% else %}text-red-600{% endif %}">
                            {% if financial_summary.is_deposit_paid %}Paid{% else %}Pending{% endif %}
                        </dd>
                    </div>
                </dl>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="bg-white shadow rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Quick Actions</h3>
            </div>
            <div class="px-6 py-4 space-y-3">
                
                <!-- Add Payment -->
                {% if financial_summary.remaining_balance > 0 %}
                <button onclick="showAddPaymentModal()" 
                        class="w-full flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 transition-colors duration-200">
                    <i class="fas fa-credit-card mr-2"></i>
                    Add Payment
                </button>
                {% endif %}
                
                <!-- Update Status -->
                <button onclick="showUpdateStatusModal()" 
                        class="w-full flex items-center justify-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    <i class="fas fa-edit mr-2"></i>
                    Update Status
                </button>
                
                <!-- Send Email -->
                <a href="mailto:{{ booking.customer.email }}?subject=Regarding your {{ booking.get_event_type_display }} booking" 
                   class="w-full flex items-center justify-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    <i class="fas fa-envelope mr-2"></i>
                    Send Email
                </a>
                
                <!-- View Customer -->
                <a href="{% url 'savory_events:management_customer_detail' booking.customer.id %}" 
                   class="w-full flex items-center justify-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    <i class="fas fa-user mr-2"></i>
                    View Customer
                </a>
            </div>
        </div>

        <!-- Booking Timeline -->
        <div class="bg-white shadow rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Timeline</h3>
            </div>
            <div class="px-6 py-4">
                <div class="flow-root">
                    <ul class="-mb-8">
                        <li>
                            <div class="relative pb-8">
                                <div class="relative flex items-center space-x-3">
                                    <div class="h-8 w-8 rounded-full bg-blue-500 flex items-center justify-center ring-8 ring-white">
                                        <i class="fas fa-plus text-white text-xs"></i>
                                    </div>
                                    <div class="min-w-0 flex-1">
                                        <div>
                                            <div class="text-sm">
                                                <span class="font-medium text-gray-900">Booking Created</span>
                                            </div>
                                            <p class="mt-0.5 text-sm text-gray-500">
                                                {{ booking.created_at|date:"M d, Y" }} at {{ booking.created_at|time:"h:i A" }}
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </li>
                        
                        {% for payment in booking.payments.all %}
                        <li>
                            <div class="relative pb-8">
                                <div class="relative flex items-center space-x-3">
                                    <div class="h-8 w-8 rounded-full bg-green-500 flex items-center justify-center ring-8 ring-white">
                                        <i class="fas fa-dollar-sign text-white text-xs"></i>
                                    </div>
                                    <div class="min-w-0 flex-1">
                                        <div>
                                            <div class="text-sm">
                                                <span class="font-medium text-gray-900">Payment Received</span>
                                            </div>
                                            <p class="mt-0.5 text-sm text-gray-500">
                                                ${{ payment.amount|floatformat:2 }} • {{ payment.payment_date|date:"M d, Y" }}
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </li>
                        {% endfor %}
                        
                        <li>
                            <div class="relative">
                                <div class="relative flex items-center space-x-3">
                                    <div class="h-8 w-8 rounded-full bg-gray-300 flex items-center justify-center ring-8 ring-white">
                                        <i class="fas fa-calendar text-white text-xs"></i>
                                    </div>
                                    <div class="min-w-0 flex-1">
                                        <div>
                                            <div class="text-sm">
                                                <span class="font-medium text-gray-900">Event Date</span>
                                            </div>
                                            <p class="mt-0.5 text-sm text-gray-500">
                                                {{ booking.event_date|date:"M d, Y" }} at {{ booking.event_date|time:"h:i A" }}
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

{% csrf_token %}
{% endblock %}

{% block extra_scripts %}
<script>
function showAddPaymentModal() {
    // Simple implementation - could be enhanced with proper modal
    const amount = prompt("Enter payment amount:");
    const type = prompt("Enter payment type (deposit/balance/full):");
    const notes = prompt("Enter payment notes (optional):");
    
    if (amount && type) {
        fetch(`/management/payments/add/{{ booking.id }}/`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
            },
            body: `amount=${amount}&payment_type=${type}&notes=${notes || ''}`
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(data.message);
                location.reload();
            } else {
                alert(data.error);
            }
        })
        .catch(error => {
            alert('Error adding payment: ' + error);
        });
    }
}

function showUpdateStatusModal() {
    const newStatus = prompt("Enter new status (pending/confirmed/partial_paid/fully_paid/completed/cancelled):");
    
    if (newStatus) {
        fetch(`/management/bookings/{{ booking.id }}/status/`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
            },
            body: `status=${newStatus}`
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(data.message);
                location.reload();
            } else {
                alert(data.error);
            }
        })
        .catch(error => {
            alert('Error updating status: ' + error);
        });
    }
}
</script>
{% endblock %}
