{% extends 'base.html' %}

{% block title %}Login - BonAppetit Catering{% endblock %}

{% block content %}
<div class="min-h-screen bg-gradient-to-br from-cream via-white to-sage-green/10 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8" x-data="loginForm()">
    <!-- Background Pattern -->
    <div class="absolute inset-0 opacity-5">
        <div class="absolute inset-0" style="background-image: url('data:image/svg+xml,<svg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"><g fill="none" fill-rule="evenodd"><g fill="%23556B2F" fill-opacity="0.1"><circle cx="30" cy="30" r="2"/></g></svg>'); background-size: 60px 60px;"></div>
    </div>

    <div class="relative max-w-md w-full">
        <!-- Main Card -->
        <div class="bg-white/80 backdrop-blur-sm shadow-2xl rounded-2xl border border-sage-green/20 overflow-hidden transform transition-all duration-300 hover:shadow-3xl">
            <!-- Header Section -->
            <div class="px-8 pt-8 pb-6 bg-gradient-to-r from-deep-olive to-sage-green text-white relative overflow-hidden">
                <!-- Decorative Elements -->
                <div class="absolute top-0 right-0 w-32 h-32 bg-white/10 rounded-full -translate-y-16 translate-x-16"></div>
                <div class="absolute bottom-0 left-0 w-24 h-24 bg-white/5 rounded-full translate-y-12 -translate-x-12"></div>

                <div class="relative z-10 text-center">
                    <!-- Logo -->
                    <div class="mx-auto h-20 w-20 bg-white/20 backdrop-blur-sm rounded-2xl flex items-center justify-center mb-4 transform transition-transform duration-300 hover:scale-105 shadow-lg">
                        <span class="text-white font-bold text-2xl">BA</span>
                    </div>

                    <!-- Title -->
                    <h1 class="text-2xl font-bold mb-2">Welcome Back</h1>
                    <p class="text-white/90 text-sm">Sign in to your BonAppetit account</p>
                </div>
            </div>

            <!-- Form Section -->
            <div class="px-8 py-6">
                <form class="space-y-6" method="post" @submit="handleSubmit">
                    {% csrf_token %}

                    <!-- Username Field -->
                    <div class="space-y-2">
                        <label for="{{ form.username.id_for_label }}" class="block text-sm font-semibold text-charcoal">
                            <i class="fas fa-user mr-2 text-sage-green"></i>Username or Email
                        </label>
                        <div class="relative">
                            <input type="text"
                                   name="{{ form.username.name }}"
                                   id="{{ form.username.id_for_label }}"
                                   class="w-full px-4 py-3 border-2 border-sage-green/30 rounded-xl focus:outline-none focus:border-burnt-orange focus:ring-4 focus:ring-burnt-orange/20 transition-all duration-300 bg-white/70 backdrop-blur-sm placeholder-gray-400"
                                   placeholder="Enter your username or email"
                                   x-model="formData.username"
                                   @input="validateField('username')"
                                   :class="{'border-red-400 focus:border-red-400 focus:ring-red-400/20': errors.username, 'border-green-400 focus:border-green-400 focus:ring-green-400/20': validFields.username}"
                                   required>
                            <!-- Validation Icon -->
                            <div class="absolute right-3 top-1/2 transform -translate-y-1/2">
                                <i x-show="validFields.username" class="fas fa-check text-green-500 transition-opacity duration-300"></i>
                                <i x-show="errors.username" class="fas fa-exclamation-circle text-red-500 transition-opacity duration-300"></i>
                            </div>
                        </div>
                        <p x-show="errors.username" x-text="errors.username" class="text-red-500 text-sm mt-1 transition-opacity duration-300"></p>
                    </div>

                    <!-- Password Field -->
                    <div class="space-y-2">
                        <label for="{{ form.password.id_for_label }}" class="block text-sm font-semibold text-charcoal">
                            <i class="fas fa-lock mr-2 text-sage-green"></i>Password
                        </label>
                        <div class="relative">
                            <input :type="showPassword ? 'text' : 'password'"
                                   name="{{ form.password.name }}"
                                   id="{{ form.password.id_for_label }}"
                                   class="w-full px-4 py-3 pr-12 border-2 border-sage-green/30 rounded-xl focus:outline-none focus:border-burnt-orange focus:ring-4 focus:ring-burnt-orange/20 transition-all duration-300 bg-white/70 backdrop-blur-sm placeholder-gray-400"
                                   placeholder="Enter your password"
                                   x-model="formData.password"
                                   @input="validateField('password')"
                                   :class="{'border-red-400 focus:border-red-400 focus:ring-red-400/20': errors.password, 'border-green-400 focus:border-green-400 focus:ring-green-400/20': validFields.password}"
                                   required>
                            <!-- Password Toggle -->
                            <button type="button"
                                    @click="showPassword = !showPassword"
                                    class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-sage-green transition-colors duration-200">
                                <i :class="showPassword ? 'fas fa-eye-slash' : 'fas fa-eye'"></i>
                            </button>
                        </div>
                        <p x-show="errors.password" x-text="errors.password" class="text-red-500 text-sm mt-1 transition-opacity duration-300"></p>
                    </div>

                    <!-- Django Form Errors -->
                    {% if form.errors %}
                    <div class="bg-red-50 border-l-4 border-red-400 p-4 rounded-lg" x-data="{ show: true }" x-show="show" x-transition>
                        <div class="flex items-start">
                            <div class="flex-shrink-0">
                                <i class="fas fa-exclamation-triangle text-red-400"></i>
                            </div>
                            <div class="ml-3 flex-1">
                                <h3 class="text-sm font-medium text-red-800 mb-2">
                                    Please correct the following errors:
                                </h3>
                                <div class="text-sm text-red-700 space-y-1">
                                    {% for field, field_errors in form.errors.items %}
                                        {% for error in field_errors %}
                                            <div class="flex items-center">
                                                <i class="fas fa-times-circle mr-2"></i>
                                                <span>{{ error }}</span>
                                            </div>
                                        {% endfor %}
                                    {% endfor %}
                                </div>
                            </div>
                            <button @click="show = false" class="flex-shrink-0 ml-3 text-red-400 hover:text-red-600 transition-colors">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                    {% endif %}

                    <!-- Remember Me & Forgot Password -->
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <input id="remember-me" name="remember-me" type="checkbox"
                                   class="h-4 w-4 text-burnt-orange focus:ring-burnt-orange border-sage-green rounded transition-colors duration-200">
                            <label for="remember-me" class="ml-3 block text-sm text-charcoal font-medium">
                                Remember me
                            </label>
                        </div>

                        <div class="text-sm">
                            <a href="#" class="font-medium text-burnt-orange hover:text-deep-olive transition-colors duration-200 hover:underline">
                                Forgot password?
                            </a>
                        </div>
                    </div>

                    <!-- Submit Button -->
                    <div class="space-y-4">
                        <button type="submit"
                                :disabled="isSubmitting"
                                class="group relative w-full flex justify-center py-4 px-6 border border-transparent text-base font-semibold rounded-xl text-white bg-gradient-to-r from-burnt-orange to-deep-olive hover:from-deep-olive hover:to-burnt-orange focus:outline-none focus:ring-4 focus:ring-burnt-orange/30 disabled:opacity-50 disabled:cursor-not-allowed transform transition-all duration-300 hover:scale-[1.02] hover:shadow-lg">

                            <!-- Loading Spinner -->
                            <div x-show="isSubmitting" class="absolute left-4 top-1/2 transform -translate-y-1/2">
                                <div class="animate-spin rounded-full h-5 w-5 border-2 border-white border-t-transparent"></div>
                            </div>

                            <!-- Sign In Icon -->
                            <span x-show="!isSubmitting" class="absolute left-4 top-1/2 transform -translate-y-1/2">
                                <i class="fas fa-sign-in-alt text-white/80 group-hover:text-white transition-colors duration-200"></i>
                            </span>

                            <!-- Button Text -->
                            <span x-text="isSubmitting ? 'Signing in...' : 'Sign In'" class="transition-all duration-200"></span>
                        </button>
                    </div>

                </form>
            </div>

            <!-- Footer Section -->
            <div class="px-8 py-6 bg-gray-50/80 border-t border-sage-green/20">
                <div class="text-center space-y-4">
                    <p class="text-sm text-gray-600">
                        Don't have an account?
                        <a href="{% url 'savory_events:register' %}" class="font-semibold text-burnt-orange hover:text-deep-olive transition-colors duration-200 hover:underline">
                            Create one now
                        </a>
                    </p>

                    <!-- Social Login Options (Future Enhancement) -->
                    <div class="flex items-center justify-center space-x-4 pt-4 border-t border-gray-200">
                        <span class="text-xs text-gray-500">Secure login powered by BonAppetit</span>
                        <div class="flex space-x-2">
                            <div class="w-2 h-2 bg-sage-green rounded-full animate-pulse"></div>
                            <div class="w-2 h-2 bg-burnt-orange rounded-full animate-pulse" style="animation-delay: 0.2s;"></div>
                            <div class="w-2 h-2 bg-deep-olive rounded-full animate-pulse" style="animation-delay: 0.4s;"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Additional Help Text -->
        <div class="mt-6 text-center">
            <p class="text-sm text-gray-600">
                Need help? <a href="#" class="text-burnt-orange hover:text-deep-olive transition-colors duration-200 font-medium">Contact Support</a>
            </p>
        </div>
    </div>
</div>

<!-- Alpine.js Component -->
<script>
function loginForm() {
    return {
        formData: {
            username: '',
            password: ''
        },
        errors: {},
        validFields: {},
        showPassword: false,
        isSubmitting: false,

        validateField(field) {
            // Clear previous errors
            delete this.errors[field];
            delete this.validFields[field];

            const value = this.formData[field];

            if (field === 'username') {
                if (!value || value.length < 3) {
                    this.errors.username = 'Username must be at least 3 characters';
                } else {
                    this.validFields.username = true;
                }
            }

            if (field === 'password') {
                if (!value || value.length < 6) {
                    this.errors.password = 'Password must be at least 6 characters';
                } else {
                    this.validFields.password = true;
                }
            }
        },

        handleSubmit(event) {
            // Validate all fields before submission
            this.validateField('username');
            this.validateField('password');

            // If there are errors, prevent submission
            if (Object.keys(this.errors).length > 0) {
                event.preventDefault();
                return;
            }

            // Show loading state
            this.isSubmitting = true;

            // Let the form submit naturally
            // The loading state will be reset when the page reloads or redirects
        }
    }
}
</script>

{% endblock %}
