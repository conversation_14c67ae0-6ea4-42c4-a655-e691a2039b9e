{% extends 'management/base.html' %}

{% block title %}Menu Management{% endblock %}
{% block page_title %}Menu Management{% endblock %}

{% block content %}
<!-- Menu Statistics -->
<div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
    <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="h-8 w-8 bg-blue-500 rounded-md flex items-center justify-center">
                        <i class="fas fa-utensils text-white"></i>
                    </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Total Items</dt>
                        <dd class="text-lg font-medium text-gray-900">{{ stats.total_items }}</dd>
                    </dl>
                </div>
            </div>
        </div>
    </div>

    <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="h-8 w-8 bg-green-500 rounded-md flex items-center justify-center">
                        <i class="fas fa-check-circle text-white"></i>
                    </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Available</dt>
                        <dd class="text-lg font-medium text-gray-900">{{ stats.available_items }}</dd>
                    </dl>
                </div>
            </div>
        </div>
    </div>

    <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="h-8 w-8 bg-purple-500 rounded-md flex items-center justify-center">
                        <i class="fas fa-tags text-white"></i>
                    </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Categories</dt>
                        <dd class="text-lg font-medium text-gray-900">{{ stats.categories|length }}</dd>
                    </dl>
                </div>
            </div>
        </div>
    </div>

    <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="h-8 w-8 bg-orange-500 rounded-md flex items-center justify-center">
                        <i class="fas fa-dollar-sign text-white"></i>
                    </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Avg. Price</dt>
                        <dd class="text-lg font-medium text-gray-900">${{ stats.avg_price|floatformat:2 }}</dd>
                    </dl>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Filters and Add Button -->
<div class="bg-white shadow rounded-lg mb-6">
    <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex items-center justify-between">
            <h3 class="text-lg font-medium text-gray-900">Menu Items</h3>
            <button onclick="openAddItemModal()" 
                    class="bg-deep-olive text-white px-4 py-2 rounded-md hover:bg-burnt-orange transition-colors duration-200">
                <i class="fas fa-plus mr-2"></i>
                Add Menu Item
            </button>
        </div>
    </div>
    <div class="px-6 py-4">
        <form method="get" class="grid grid-cols-1 md:grid-cols-4 gap-4" id="filterForm">
            
            <!-- Search -->
            <div>
                <label for="search" class="block text-sm font-medium text-gray-700 mb-1">Search</label>
                <input type="text" 
                       name="search" 
                       id="search"
                       value="{{ search_query }}"
                       placeholder="Item name or description..."
                       class="block w-full border-gray-300 rounded-md shadow-sm focus:ring-deep-olive focus:border-deep-olive">
            </div>
            
            <!-- Category Filter -->
            <div>
                <label for="category" class="block text-sm font-medium text-gray-700 mb-1">Category</label>
                <select name="category" 
                        id="category"
                        class="block w-full border-gray-300 rounded-md shadow-sm focus:ring-deep-olive focus:border-deep-olive">
                    <option value="">All Categories</option>
                    {% for value, label in category_choices %}
                    <option value="{{ value }}" {% if category_filter == value %}selected{% endif %}>{{ label }}</option>
                    {% endfor %}
                </select>
            </div>
            
            <!-- Availability Filter -->
            <div>
                <label for="availability" class="block text-sm font-medium text-gray-700 mb-1">Availability</label>
                <select name="availability" 
                        id="availability"
                        class="block w-full border-gray-300 rounded-md shadow-sm focus:ring-deep-olive focus:border-deep-olive">
                    <option value="">All Items</option>
                    <option value="available" {% if availability_filter == 'available' %}selected{% endif %}>Available Only</option>
                    <option value="unavailable" {% if availability_filter == 'unavailable' %}selected{% endif %}>Unavailable Only</option>
                </select>
            </div>
            
            <!-- Filter Actions -->
            <div class="flex items-end space-x-2">
                <button type="submit" 
                        class="flex-1 bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors duration-200">
                    <i class="fas fa-search mr-2"></i>
                    Filter
                </button>
                <a href="{% url 'savory_events:management_menu' %}" 
                   class="px-3 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50">
                    <i class="fas fa-times"></i>
                </a>
            </div>
        </form>
    </div>
</div>

<!-- Menu Items Grid -->
<div class="bg-white shadow rounded-lg" id="menuItemsContainer">
    <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900">Menu Items ({{ menu_items|length }} items)</h3>
    </div>
    
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 p-6" id="menuItemsGrid">
        {% for item in menu_items %}
        <div class="border border-gray-200 rounded-lg overflow-hidden hover:shadow-lg transition-shadow duration-200" 
             id="menuItem-{{ item.id }}"
             data-item-id="{{ item.id }}">
            
            <!-- Item Image -->
            {% if item.image %}
            <div class="h-48 bg-gray-200 overflow-hidden">
                <img src="{{ item.image.url }}" 
                     alt="{{ item.name }}"
                     class="w-full h-full object-cover"
                     loading="lazy">
            </div>
            {% else %}
            <div class="h-48 bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center">
                <div class="text-center text-gray-400">
                    <i class="fas fa-image text-4xl mb-2"></i>
                    <p class="text-sm">No Image</p>
                </div>
            </div>
            {% endif %}
            
            <!-- Item Content -->
            <div class="p-4">
                <!-- Item Header -->
                <div class="flex items-start justify-between mb-3">
                    <div class="flex-1">
                        <h4 class="text-lg font-medium text-gray-900 mb-1">{{ item.name }}</h4>
                        <span class="inline-flex px-2 py-1 text-xs font-medium rounded-full 
                            {% if item.category == 'appetizer' %}bg-blue-100 text-blue-800
                            {% elif item.category == 'main' %}bg-green-100 text-green-800
                            {% elif item.category == 'dessert' %}bg-purple-100 text-purple-800
                            {% else %}bg-orange-100 text-orange-800{% endif %}">
                            {{ item.get_category_display }}
                        </span>
                    </div>
                <div class="flex items-center space-x-1">
                    <!-- Availability Toggle -->
                    <button onclick="toggleAvailability({{ item.id }})" 
                            class="p-1 rounded-md {% if item.is_available %}text-green-600 hover:text-green-800{% else %}text-red-600 hover:text-red-800{% endif %}"
                            title="{% if item.is_available %}Mark as unavailable{% else %}Mark as available{% endif %}">
                        <i class="fas {% if item.is_available %}fa-eye{% else %}fa-eye-slash{% endif %}"></i>
                    </button>
                    
                    <!-- Edit Button -->
                    <button onclick="openEditItemModal({{ item.id }})" 
                            class="p-1 text-blue-600 hover:text-blue-800 rounded-md"
                            title="Edit item">
                        <i class="fas fa-edit"></i>
                    </button>
                    
                    <!-- Delete Button -->
                    <button onclick="deleteMenuItem({{ item.id }})" 
                            class="p-1 text-red-600 hover:text-red-800 rounded-md"
                            title="Delete item">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
                </div>
                
                <!-- Item Details -->
                <div class="space-y-2">
                    <p class="text-sm text-gray-600">{{ item.description|truncatewords:15 }}</p>
                    
                    <div class="flex items-center justify-between">
                        <div class="text-lg font-bold text-gray-900">
                            ${{ item.price_per_person|floatformat:2 }}<span class="text-sm font-normal text-gray-500">/person</span>
                        </div>
                        <div class="flex items-center">
                            {% if item.is_available %}
                                <span class="inline-flex items-center px-2 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800">
                                    <i class="fas fa-check-circle mr-1"></i>
                                    Available
                                </span>
                            {% else %}
                                <span class="inline-flex items-center px-2 py-1 text-xs font-medium rounded-full bg-red-100 text-red-800">
                                    <i class="fas fa-times-circle mr-1"></i>
                                    Unavailable
                                </span>
                            {% endif %}
                        </div>
                    </div>
                    
                    <!-- Statistics -->
                    {% if item.order_count or item.total_revenue %}
                    <div class="grid grid-cols-2 gap-2 pt-2 border-t border-gray-100">
                        <div class="text-center">
                            <div class="text-sm font-medium text-gray-900">{{ item.order_count|default:0 }}</div>
                            <div class="text-xs text-gray-500">Orders</div>
                        </div>
                        <div class="text-center">
                            <div class="text-sm font-medium text-gray-900">${{ item.total_revenue|default:0|floatformat:0 }}</div>
                            <div class="text-xs text-gray-500">Revenue</div>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
        {% empty %}
        <div class="col-span-full text-center py-12">
            <div class="text-gray-500">
                <i class="fas fa-utensils text-6xl mb-4 text-gray-300"></i>
                <h3 class="text-lg font-medium text-gray-900 mb-2">No menu items found</h3>
                <p class="text-sm">Try adjusting your filters or add a new menu item.</p>
                <button onclick="openAddItemModal()" 
                        class="inline-flex items-center mt-4 px-4 py-2 bg-deep-olive text-white rounded-md hover:bg-burnt-orange transition-colors duration-200">
                    <i class="fas fa-plus mr-2"></i>
                    Add First Menu Item
                </button>
            </div>
        </div>
        {% endfor %}
    </div>
</div>

<!-- Add/Edit Menu Item Modal -->
<div id="menuItemModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden" style="z-index: 1000;">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900" id="modalTitle">Add Menu Item</h3>
                <button onclick="closeMenuItemModal()" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            
            <form id="menuItemForm" enctype="multipart/form-data">
                {% csrf_token %}
                <input type="hidden" id="itemId" name="item_id">
                <input type="hidden" id="removeImage" name="remove_image" value="false">
                
                <div class="space-y-4">
                    <!-- Image Upload -->
                    <div>
                        <label for="itemImage" class="block text-sm font-medium text-gray-700 mb-1">Item Image</label>
                        <div class="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md" id="imageDropZone">
                            <div class="space-y-1 text-center">
                                <div id="imagePreview" class="hidden">
                                    <img id="previewImg" src="" alt="Preview" class="mx-auto h-32 w-32 object-cover rounded-md">
                                    <div class="mt-2 flex justify-center space-x-2">
                                        <button type="button" onclick="removeImagePreview()" class="text-red-600 hover:text-red-800 text-sm">
                                            <i class="fas fa-trash mr-1"></i>Remove
                                        </button>
                                        <button type="button" onclick="document.getElementById('itemImage').click()" class="text-blue-600 hover:text-blue-800 text-sm">
                                            <i class="fas fa-edit mr-1"></i>Change
                                        </button>
                                    </div>
                                </div>
                                <div id="imageUploadPrompt">
                                    <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                                        <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                                    </svg>
                                    <div class="flex text-sm text-gray-600">
                                        <label for="itemImage" class="relative cursor-pointer bg-white rounded-md font-medium text-deep-olive hover:text-burnt-orange focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-deep-olive">
                                            <span>Upload a file</span>
                                        </label>
                                        <p class="pl-1">or drag and drop</p>
                                    </div>
                                    <p class="text-xs text-gray-500">PNG, JPG, GIF, WebP up to 5MB</p>
                                </div>
                            </div>
                        </div>
                        <input id="itemImage" 
                               name="image" 
                               type="file" 
                               accept="image/*"
                               class="sr-only"
                               onchange="previewImage(this)">
                    </div>
                    
                    <!-- Name -->
                    <div>
                        <label for="itemName" class="block text-sm font-medium text-gray-700 mb-1">Item Name *</label>
                        <input type="text" 
                               id="itemName" 
                               name="name"
                               required
                               class="block w-full border-gray-300 rounded-md shadow-sm focus:ring-deep-olive focus:border-deep-olive">
                    </div>
                    
                    <!-- Description -->
                    <div>
                        <label for="itemDescription" class="block text-sm font-medium text-gray-700 mb-1">Description *</label>
                        <textarea id="itemDescription" 
                                  name="description"
                                  rows="3"
                                  required
                                  class="block w-full border-gray-300 rounded-md shadow-sm focus:ring-deep-olive focus:border-deep-olive"></textarea>
                    </div>
                    
                    <!-- Category -->
                    <div>
                        <label for="itemCategory" class="block text-sm font-medium text-gray-700 mb-1">Category *</label>
                        <select id="itemCategory" 
                                name="category"
                                required
                                class="block w-full border-gray-300 rounded-md shadow-sm focus:ring-deep-olive focus:border-deep-olive">
                            <option value="">Select Category</option>
                            {% for value, label in category_choices %}
                            <option value="{{ value }}">{{ label }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    
                    <!-- Price -->
                    <div>
                        <label for="itemPrice" class="block text-sm font-medium text-gray-700 mb-1">Price per Person *</label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <span class="text-gray-500 sm:text-sm">$</span>
                            </div>
                            <input type="number" 
                                   id="itemPrice" 
                                   name="price_per_person"
                                   step="0.01"
                                   min="0.01"
                                   required
                                   class="block w-full pl-7 border-gray-300 rounded-md shadow-sm focus:ring-deep-olive focus:border-deep-olive">
                        </div>
                    </div>
                    
                    <!-- Availability -->
                    <div class="flex items-center">
                        <input type="checkbox" 
                               id="itemAvailable" 
                               name="is_available"
                               checked
                               class="h-4 w-4 text-deep-olive focus:ring-deep-olive border-gray-300 rounded">
                        <label for="itemAvailable" class="ml-2 block text-sm text-gray-700">
                            Available for ordering
                        </label>
                    </div>
                </div>
                
                <!-- Modal Buttons -->
                <div class="flex items-center justify-end space-x-3 mt-6">
                    <button type="button" 
                            onclick="closeMenuItemModal()"
                            class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50">
                        Cancel
                    </button>
                    <button type="submit" 
                            id="submitButton"
                            class="px-4 py-2 bg-deep-olive text-white rounded-md hover:bg-burnt-orange transition-colors duration-200">
                        <span id="submitText">Add Item</span>
                        <i id="submitSpinner" class="fas fa-spinner fa-spin ml-2 hidden"></i>
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Success/Error Messages -->
<div id="messageContainer" class="fixed top-20 right-5 z-50"></div>

{% csrf_token %}
{% endblock %}

{% block extra_scripts %}
<script>
let isModalOpen = false;
let currentEditingId = null;

// Open Add Item Modal
function openAddItemModal() {
    document.getElementById('modalTitle').textContent = 'Add Menu Item';
    document.getElementById('submitText').textContent = 'Add Item';
    document.getElementById('itemId').value = '';
    document.getElementById('menuItemForm').reset();
    document.getElementById('itemAvailable').checked = true;
    hideImagePreview();
    currentEditingId = null;
    showModal();
}

// Open Edit Item Modal
function openEditItemModal(itemId) {
    document.getElementById('modalTitle').textContent = 'Edit Menu Item';
    document.getElementById('submitText').textContent = 'Update Item';
    currentEditingId = itemId;
    
    // Fetch item data
    fetch(`/management/menu/get/${itemId}/`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const item = data.menu_item;
                document.getElementById('itemId').value = item.id;
                document.getElementById('itemName').value = item.name;
                document.getElementById('itemDescription').value = item.description;
                document.getElementById('itemCategory').value = item.category;
                document.getElementById('itemPrice').value = item.price_per_person;
                document.getElementById('itemAvailable').checked = item.is_available;
                
                // Handle existing image
                if (item.image_url) {
                    showImagePreview(item.image_url);
                } else {
                    hideImagePreview();
                }
                
                showModal();
            } else {
                showMessage(data.error, 'error');
            }
        })
        .catch(error => {
            showMessage('Error loading item data', 'error');
        });
}

// Show Modal
function showModal() {
    document.getElementById('menuItemModal').classList.remove('hidden');
    isModalOpen = true;
}

// Close Modal
function closeMenuItemModal() {
    document.getElementById('menuItemModal').classList.add('hidden');
    isModalOpen = false;
    currentEditingId = null;
}

// Handle Form Submission
document.getElementById('menuItemForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const submitButton = document.getElementById('submitButton');
    const submitText = document.getElementById('submitText');
    const submitSpinner = document.getElementById('submitSpinner');
    
    // Show loading state
    submitButton.disabled = true;
    submitSpinner.classList.remove('hidden');
    
    const formData = new FormData(this);
    const url = currentEditingId ? 
        `/management/menu/update/${currentEditingId}/` : 
        '/management/menu/create/';
    
    fetch(url, {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
        },
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showMessage(data.message, 'success');
            closeMenuItemModal();
            
            if (currentEditingId) {
                updateMenuItemInDOM(data.menu_item);
            } else {
                // Reload page to show new item
                window.location.reload();
            }
        } else {
            showMessage(data.error, 'error');
        }
    })
    .catch(error => {
        showMessage('Error saving menu item', 'error');
    })
    .finally(() => {
        // Reset button state
        submitButton.disabled = false;
        submitSpinner.classList.add('hidden');
    });
});

// Update Menu Item in DOM
function updateMenuItemInDOM(item) {
    const itemElement = document.getElementById(`menuItem-${item.id}`);
    if (itemElement) {
        // Update item name
        const nameElement = itemElement.querySelector('h4');
        if (nameElement) nameElement.textContent = item.name;
        
        // Update category badge
        const categoryElement = itemElement.querySelector('.inline-flex.px-2.py-1');
        if (categoryElement) {
            categoryElement.textContent = item.category_display;
            // Update category color classes if needed
        }
        
        // Update description
        const descElement = itemElement.querySelector('.text-sm.text-gray-600');
        if (descElement) descElement.textContent = item.description;
        
        // Update price
        const priceElement = itemElement.querySelector('.text-lg.font-bold');
        if (priceElement) {
            priceElement.innerHTML = `$${item.price_per_person.toFixed(2)}<span class="text-sm font-normal text-gray-500">/person</span>`;
        }
        
        // Update availability
        const availabilityElement = itemElement.querySelector('.inline-flex.items-center.px-2.py-1.text-xs');
        if (availabilityElement) {
            if (item.is_available) {
                availabilityElement.className = 'inline-flex items-center px-2 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800';
                availabilityElement.innerHTML = '<i class="fas fa-check-circle mr-1"></i>Available';
            } else {
                availabilityElement.className = 'inline-flex items-center px-2 py-1 text-xs font-medium rounded-full bg-red-100 text-red-800';
                availabilityElement.innerHTML = '<i class="fas fa-times-circle mr-1"></i>Unavailable';
            }
        }
    }
}

// Toggle Availability
function toggleAvailability(itemId) {
    fetch(`/management/menu/toggle/${itemId}/`, {
        method: 'POST',
        headers: {
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
        },
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showMessage(data.message, 'success');
            
            // Update the item in DOM
            const itemElement = document.getElementById(`menuItem-${itemId}`);
            const toggleButton = itemElement.querySelector(`button[onclick="toggleAvailability(${itemId})"]`);
            const availabilityElement = itemElement.querySelector('.inline-flex.items-center.px-2.py-1.text-xs');
            
            if (data.is_available) {
                toggleButton.className = 'p-1 rounded-md text-green-600 hover:text-green-800';
                toggleButton.innerHTML = '<i class="fas fa-eye"></i>';
                toggleButton.title = 'Mark as unavailable';
                availabilityElement.className = 'inline-flex items-center px-2 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800';
                availabilityElement.innerHTML = '<i class="fas fa-check-circle mr-1"></i>Available';
            } else {
                toggleButton.className = 'p-1 rounded-md text-red-600 hover:text-red-800';
                toggleButton.innerHTML = '<i class="fas fa-eye-slash"></i>';
                toggleButton.title = 'Mark as available';
                availabilityElement.className = 'inline-flex items-center px-2 py-1 text-xs font-medium rounded-full bg-red-100 text-red-800';
                availabilityElement.innerHTML = '<i class="fas fa-times-circle mr-1"></i>Unavailable';
            }
        } else {
            showMessage(data.error, 'error');
        }
    })
    .catch(error => {
        showMessage('Error toggling availability', 'error');
    });
}

// Delete Menu Item
function deleteMenuItem(itemId) {
    if (!confirm('Are you sure you want to delete this menu item? This action cannot be undone.')) {
        return;
    }
    
    fetch(`/management/menu/delete/${itemId}/`, {
        method: 'POST',
        headers: {
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
        },
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showMessage(data.message, 'success');
            // Remove item from DOM
            const itemElement = document.getElementById(`menuItem-${itemId}`);
            if (itemElement) {
                itemElement.remove();
            }
        } else {
            showMessage(data.error, 'error');
        }
    })
    .catch(error => {
        showMessage('Error deleting menu item', 'error');
    });
}

// Show Message
function showMessage(message, type) {
    const messageContainer = document.getElementById('messageContainer');
    const messageId = 'message-' + Date.now();
    
    const messageElement = document.createElement('div');
    messageElement.id = messageId;
    messageElement.className = `rounded-md p-4 mb-4 fade-in ${type === 'error' ? 'bg-red-50' : 'bg-green-50'}`;
    messageElement.innerHTML = `
        <div class="flex">
            <div class="flex-shrink-0">
                <i class="fas ${type === 'error' ? 'fa-exclamation-circle text-red-400' : 'fa-check-circle text-green-400'}"></i>
            </div>
            <div class="ml-3">
                <p class="text-sm font-medium ${type === 'error' ? 'text-red-800' : 'text-green-800'}">${message}</p>
            </div>
            <div class="ml-auto pl-3">
                <button onclick="document.getElementById('${messageId}').remove()" 
                        class="inline-flex ${type === 'error' ? 'text-red-400 hover:text-red-600' : 'text-green-400 hover:text-green-600'}">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
    `;
    
    messageContainer.appendChild(messageElement);
    
    // Auto-remove after 5 seconds
    setTimeout(() => {
        const element = document.getElementById(messageId);
        if (element) {
            element.remove();
        }
    }, 5000);
}

// Close modal on outside click
document.getElementById('menuItemModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeMenuItemModal();
    }
});

// Close modal on Escape key
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape' && isModalOpen) {
        closeMenuItemModal();
    }
});

// Image handling functions
function previewImage(input) {
    if (input.files && input.files[0]) {
        const file = input.files[0];
        
        // Check file size (5MB limit)
        if (file.size > 5 * 1024 * 1024) {
            showMessage('Image file too large. Maximum size is 5MB.', 'error');
            input.value = '';
            return;
        }
        
        // Check file type
        const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
        if (!allowedTypes.includes(file.type)) {
            showMessage('Invalid image format. Please use JPEG, PNG, GIF, or WebP.', 'error');
            input.value = '';
            return;
        }
        
        const reader = new FileReader();
        reader.onload = function(e) {
            showImagePreview(e.target.result);
        };
        reader.readAsDataURL(file);
    }
}

function showImagePreview(imageSrc) {
    document.getElementById('previewImg').src = imageSrc;
    document.getElementById('imagePreview').classList.remove('hidden');
    document.getElementById('imageUploadPrompt').classList.add('hidden');
    document.getElementById('removeImage').value = 'false';
}

function hideImagePreview() {
    document.getElementById('imagePreview').classList.add('hidden');
    document.getElementById('imageUploadPrompt').classList.remove('hidden');
    document.getElementById('previewImg').src = '';
}

function removeImagePreview() {
    hideImagePreview();
    document.getElementById('itemImage').value = '';
    document.getElementById('removeImage').value = 'true';
}

// Drag and drop functionality
const dropZone = document.getElementById('imageDropZone');

dropZone.addEventListener('dragover', function(e) {
    e.preventDefault();
    dropZone.classList.add('border-deep-olive', 'bg-cream');
});

dropZone.addEventListener('dragleave', function(e) {
    e.preventDefault();
    dropZone.classList.remove('border-deep-olive', 'bg-cream');
});

dropZone.addEventListener('drop', function(e) {
    e.preventDefault();
    dropZone.classList.remove('border-deep-olive', 'bg-cream');
    
    const files = e.dataTransfer.files;
    if (files.length > 0) {
        document.getElementById('itemImage').files = files;
        previewImage(document.getElementById('itemImage'));
    }
});
</script>
{% endblock %}
