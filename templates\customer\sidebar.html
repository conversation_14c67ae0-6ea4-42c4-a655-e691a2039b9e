<!-- Customer Dashboard Sidebar -->
<div class="flex flex-col h-screen bg-white shadow-lg">
    <!-- Logo/Header -->
    <div class="flex items-center h-16 px-6 bg-deep-olive">
        <div class="flex items-center">
            <div class="w-8 h-8 bg-burnt-orange rounded-full flex items-center justify-center mr-3">
                <span class="text-white font-bold text-sm">BA</span>
            </div>
            <div class="text-white">
                <h1 class="text-lg font-bold">BonAppetit</h1>
                <p class="text-xs text-sage-green">Customer Portal</p>
            </div>
        </div>
    </div>

    <!-- User Info -->
    <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <div class="h-10 w-10 rounded-full bg-burnt-orange flex items-center justify-center">
                    <span class="text-white font-medium text-sm">
                        {{ user.first_name.0|default:user.username.0 }}{{ user.last_name.0|default:"" }}
                    </span>
                </div>
            </div>
            <div class="ml-3">
                <p class="text-sm font-medium text-charcoal">{{ user.get_full_name|default:user.username }}</p>
                <p class="text-xs text-gray-500">{{ user.email }}</p>
            </div>
        </div>
    </div>

    <!-- Navigation Menu -->
    <nav class="flex-1 px-4 py-4 space-y-2">
        <!-- Dashboard -->
        <a href="{% url 'savory_events:customer_dashboard' %}" 
           class="group flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors
                  {% if request.resolver_match.url_name == 'customer_dashboard' %}
                      bg-burnt-orange text-white
                  {% else %}
                      text-charcoal hover:bg-sage-green hover:text-white
                  {% endif %}">
            <svg class="mr-3 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5v4M16 5v4"></path>
            </svg>
            Dashboard
        </a>

        <!-- New Booking -->
        <a href="{% url 'savory_events:booking' %}" 
           class="group flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors
                  {% if request.resolver_match.url_name == 'booking' or request.resolver_match.url_name == 'booking_wizard' %}
                      bg-burnt-orange text-white
                  {% else %}
                      text-charcoal hover:bg-sage-green hover:text-white
                  {% endif %}">
            <svg class="mr-3 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
            </svg>
            New Booking
        </a>

        <!-- My Bookings -->
        <a href="{% url 'savory_events:customer_bookings' %}" 
           class="group flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors
                  {% if request.resolver_match.url_name == 'customer_bookings' or request.resolver_match.url_name == 'customer_booking_detail' %}
                      bg-burnt-orange text-white
                  {% else %}
                      text-charcoal hover:bg-sage-green hover:text-white
                  {% endif %}">
            <svg class="mr-3 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
            </svg>
            My Bookings
        </a>

        <!-- Menu -->
        <a href="{% url 'savory_events:menu' %}" 
           class="group flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors
                  {% if request.resolver_match.url_name == 'menu' %}
                      bg-burnt-orange text-white
                  {% else %}
                      text-charcoal hover:bg-sage-green hover:text-white
                  {% endif %}">
            <svg class="mr-3 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 10h16M4 14h16M4 18h16"></path>
            </svg>
            Browse Menu
        </a>

        <!-- Services -->
        <a href="{% url 'savory_events:services' %}" 
           class="group flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors
                  {% if request.resolver_match.url_name == 'services' %}
                      bg-burnt-orange text-white
                  {% else %}
                      text-charcoal hover:bg-sage-green hover:text-white
                  {% endif %}">
            <svg class="mr-3 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
            </svg>
            Our Services
        </a>

        <!-- Profile -->
        <a href="{% url 'savory_events:customer_profile' %}" 
           class="group flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors
                  {% if request.resolver_match.url_name == 'customer_profile' %}
                      bg-burnt-orange text-white
                  {% else %}
                      text-charcoal hover:bg-sage-green hover:text-white
                  {% endif %}">
            <svg class="mr-3 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
            </svg>
            My Profile
        </a>

        <!-- Support/Help -->
        <a href="{% url 'savory_events:contact' %}" 
           class="group flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors
                  {% if request.resolver_match.url_name == 'contact' %}
                      bg-burnt-orange text-white
                  {% else %}
                      text-charcoal hover:bg-sage-green hover:text-white
                  {% endif %}">
            <svg class="mr-3 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            Help & Support
        </a>
    </nav>

    <!-- Bottom section -->
    <div class="p-4 border-t border-gray-200">
        <!-- Quick Actions -->
        <div class="mb-4">
            <p class="text-xs font-medium text-gray-500 uppercase tracking-wider mb-3">Quick Actions</p>
            <div class="space-y-2">
                <a href="{% url 'savory_events:booking_wizard' %}" 
                   class="block w-full text-center px-3 py-2 text-xs font-medium text-burnt-orange border border-burnt-orange rounded-md hover:bg-burnt-orange hover:text-white transition-colors">
                    Book Event (Wizard)
                </a>
                <a href="{% url 'savory_events:menu' %}" 
                   class="block w-full text-center px-3 py-2 text-xs font-medium text-sage-green border border-sage-green rounded-md hover:bg-sage-green hover:text-white transition-colors">
                    View Menu
                </a>
            </div>
        </div>

        <!-- Logout -->
        <a href="{% url 'savory_events:logout' %}" 
           class="group flex items-center w-full px-3 py-2 text-sm font-medium text-red-600 hover:bg-red-50 rounded-md transition-colors">
            <svg class="mr-3 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
            </svg>
            Sign Out
        </a>
    </div>
</div>
