{% extends 'base.html' %}

{% block title %}Contact Us - BonAppetit Catering{% endblock %}

{% block content %}
<div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <div class="text-center mb-12">
        <h1 class="text-4xl font-bold text-charcoal mb-4">Contact Us</h1>
        <p class="text-lg text-gray-600">
            Get in touch to discuss your catering needs.
        </p>
    </div>
    
    <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
        <div class="bg-white rounded-lg shadow-lg p-6">
            <h3 class="text-xl font-semibold text-charcoal mb-4">Contact Information</h3>
            <div class="space-y-4">
                <div>
                    <h4 class="font-medium text-charcoal">Phone</h4>
                    <p class="text-gray-600">(*************</p>
                </div>
                <div>
                    <h4 class="font-medium text-charcoal">Email</h4>
                    <p class="text-gray-600"><EMAIL></p>
                </div>
                <div>
                    <h4 class="font-medium text-charcoal">Hours</h4>
                    <p class="text-gray-600">Monday - Friday: 9 AM - 6 PM<br>Saturday: 10 AM - 4 PM<br>Sunday: Closed</p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg shadow-lg p-6">
            <h3 class="text-xl font-semibold text-charcoal mb-4">Send us a Message</h3>
            <form class="space-y-4" 
                  x-data="{ submitted: false, errors: {}, loading: false }"
                  up-submit up-target="#contact-form-response"
                  @submit.prevent="loading = true; $el.submit();">
                {% csrf_token %}
                <div id="contact-form-response" class="mb-4"></div>
                
                <div>
                    <label for="name" class="block text-sm font-medium text-charcoal mb-2">Name *</label>
                    <input type="text" id="name" name="name" required
                           x-data="{ focused: false, value: '' }"
                           x-model="value"
                           @focus="focused = true"
                           @blur="focused = false"
                           :class="{ 'border-red-500': errors.name, 'border-sage-green': !errors.name }"
                           class="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-burnt-orange transition-colors">
                    <div x-show="errors.name" x-text="errors.name" class="mt-1 text-sm text-red-600"></div>
                </div>
                
                <div>
                    <label for="email" class="block text-sm font-medium text-charcoal mb-2">Email *</label>
                    <input type="email" id="email" name="email" required
                           x-data="{ focused: false, value: '' }"
                           x-model="value"
                           @focus="focused = true"
                           @blur="focused = false"
                           :class="{ 'border-red-500': errors.email, 'border-sage-green': !errors.email }"
                           class="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-burnt-orange transition-colors">
                    <div x-show="errors.email" x-text="errors.email" class="mt-1 text-sm text-red-600"></div>
                </div>
                
                <div>
                    <label for="message" class="block text-sm font-medium text-charcoal mb-2">Message *</label>
                    <textarea id="message" name="message" rows="4" required
                              x-data="{ focused: false, value: '' }"
                              x-model="value"
                              @focus="focused = true"
                              @blur="focused = false"
                              :class="{ 'border-red-500': errors.message, 'border-sage-green': !errors.message }"
                              class="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-burnt-orange transition-colors"
                              placeholder="Tell us about your catering needs..."></textarea>
                    <div x-show="errors.message" x-text="errors.message" class="mt-1 text-sm text-red-600"></div>
                </div>
                
                <button type="submit" 
                        :disabled="loading"
                        :class="{ 'opacity-50 cursor-not-allowed': loading }"
                        class="w-full bg-burnt-orange hover:bg-opacity-80 text-white font-medium py-2 px-4 rounded-md transition-all duration-200 flex items-center justify-center">
                    <span x-show="!loading">Send Message</span>
                    <span x-show="loading" class="flex items-center">
                        <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Sending...
                    </span>
                </button>
            </form>
        </div>
    </div>
</div>
{% endblock %}
