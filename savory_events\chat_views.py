"""
Chat views for BonAppetit catering application.
"""

import json
import uuid
import logging
from typing import Dict, Any

from django.shortcuts import render, get_object_or_404
from django.http import JsonResponse, HttpResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods, require_POST
from django.contrib.auth.decorators import login_required
from django.utils.decorators import method_decorator
from django.views import View
from django.core.paginator import Paginator
from django.db.models import Q
from django.utils import timezone

from .models import ChatConversation, ChatMessage, ChatEscalation
from .services import ChatbotService, ChatSessionService

logger = logging.getLogger(__name__)


class ChatView(View):
    """Main chat interface view."""
    
    def get(self, request):
        """Render chat interface."""
        # Generate session ID if not exists
        session_id = request.session.get('chat_session_id')
        if not session_id:
            session_id = str(uuid.uuid4())
            request.session['chat_session_id'] = session_id
        
        # Get or create conversation
        user = request.user if request.user.is_authenticated else None
        conversation = ChatSessionService.get_or_create_conversation(session_id, user)
        
        # Get recent messages
        messages = conversation.messages.filter(is_visible=True).order_by('created_at')
        
        # Get quick actions
        chatbot_service = ChatbotService()
        quick_actions = chatbot_service.get_quick_actions()
        
        context = {
            'conversation': conversation,
            'messages': messages,
            'quick_actions': quick_actions,
            'session_id': session_id,
        }
        
        return render(request, 'chat/chat_interface.html', context)


@require_POST
@csrf_exempt
def send_message(request):
    """Handle sending chat messages."""
    try:
        data = json.loads(request.body)
        message_content = data.get('message', '').strip()
        action = data.get('action')  # For quick actions
        
        if not message_content and not action:
            return JsonResponse({'error': 'Message content is required'}, status=400)
        
        # Get session ID
        session_id = request.session.get('chat_session_id')
        if not session_id:
            session_id = str(uuid.uuid4())
            request.session['chat_session_id'] = session_id
        
        # Get or create conversation
        user = request.user if request.user.is_authenticated else None
        conversation = ChatSessionService.get_or_create_conversation(session_id, user)
        
        # Initialize chatbot service
        chatbot_service = ChatbotService()
        
        # Handle quick actions
        if action:
            if action == 'escalate':
                # Create escalation
                escalation, created = ChatEscalation.objects.get_or_create(
                    conversation=conversation,
                    defaults={
                        'reason': 'User requested human assistance',
                        'status': 'pending',
                        'priority': 'normal'
                    }
                )
                
                # Add system message
                ChatSessionService.add_message(
                    conversation,
                    'system',
                    'Your conversation has been escalated to our human support team. A representative will assist you shortly.'
                )
                
                response_text = chatbot_service.handle_quick_action(action)
            else:
                response_text = chatbot_service.handle_quick_action(action)
                message_content = f"[Quick Action: {action}]"
        else:
            # Get conversation context
            context = ChatSessionService.get_conversation_context(conversation)
            
            # Generate AI response
            response_text, should_escalate = chatbot_service.generate_response(message_content, context)
            
            # Handle escalation if needed
            if should_escalate:
                escalation, created = ChatEscalation.objects.get_or_create(
                    conversation=conversation,
                    defaults={
                        'reason': 'Automatic escalation based on message content',
                        'status': 'pending',
                        'priority': 'normal'
                    }
                )
        
        # Save user message
        user_message = ChatSessionService.add_message(
            conversation,
            'user',
            message_content,
            {'user_agent': request.META.get('HTTP_USER_AGENT', '')}
        )
        
        # Save bot response
        bot_message = ChatSessionService.add_message(
            conversation,
            'bot',
            response_text,
            {'generated_at': timezone.now().isoformat()}
        )
        
        return JsonResponse({
            'success': True,
            'user_message': {
                'id': user_message.id,
                'content': user_message.content,
                'timestamp': user_message.created_at.isoformat(),
                'type': 'user'
            },
            'bot_response': {
                'id': bot_message.id,
                'content': bot_message.content,
                'timestamp': bot_message.created_at.isoformat(),
                'type': 'bot'
            }
        })
        
    except json.JSONDecodeError:
        return JsonResponse({'error': 'Invalid JSON data'}, status=400)
    except Exception as e:
        logger.error(f"Error in send_message: {str(e)}")
        return JsonResponse({'error': 'Internal server error'}, status=500)


@require_http_methods(["GET"])
def get_messages(request):
    """Get chat messages for current conversation."""
    try:
        session_id = request.session.get('chat_session_id')
        if not session_id:
            return JsonResponse({'messages': []})
        
        try:
            conversation = ChatConversation.objects.get(session_id=session_id)
        except ChatConversation.DoesNotExist:
            return JsonResponse({'messages': []})
        
        # Get messages with pagination
        page = request.GET.get('page', 1)
        messages = conversation.messages.filter(is_visible=True).order_by('created_at')
        
        paginator = Paginator(messages, 50)  # 50 messages per page
        page_obj = paginator.get_page(page)
        
        messages_data = []
        for message in page_obj:
            messages_data.append({
                'id': message.id,
                'content': message.content,
                'type': message.message_type,
                'timestamp': message.created_at.isoformat(),
            })
        
        return JsonResponse({
            'messages': messages_data,
            'has_next': page_obj.has_next(),
            'has_previous': page_obj.has_previous(),
            'current_page': page_obj.number,
            'total_pages': paginator.num_pages,
        })
        
    except Exception as e:
        logger.error(f"Error in get_messages: {str(e)}")
        return JsonResponse({'error': 'Internal server error'}, status=500)


@require_POST
def initialize_session(request):
    """Initialize chat session."""
    try:
        # Generate session ID if not exists
        session_id = request.session.get('chat_session_id')
        if not session_id:
            session_id = str(uuid.uuid4())
            request.session['chat_session_id'] = session_id

        return JsonResponse({'success': True, 'session_id': session_id})

    except Exception as e:
        logger.error(f"Error initializing session: {str(e)}")
        return JsonResponse({'error': 'Internal server error'}, status=500)


@require_POST
def clear_conversation(request):
    """Clear current conversation."""
    try:
        session_id = request.session.get('chat_session_id')
        if session_id:
            try:
                conversation = ChatConversation.objects.get(session_id=session_id)
                conversation.is_active = False
                conversation.save()
            except ChatConversation.DoesNotExist:
                pass
        
        # Generate new session ID
        new_session_id = str(uuid.uuid4())
        request.session['chat_session_id'] = new_session_id
        
        return JsonResponse({'success': True, 'new_session_id': new_session_id})
        
    except Exception as e:
        logger.error(f"Error in clear_conversation: {str(e)}")
        return JsonResponse({'error': 'Internal server error'}, status=500)


# Admin views for chat management
@login_required
def admin_chat_conversations(request):
    """Admin view for managing chat conversations."""
    if not request.user.is_staff:
        return JsonResponse({'error': 'Unauthorized'}, status=403)
    
    # Get conversations with filters
    conversations = ChatConversation.objects.select_related('user', 'customer').prefetch_related('messages')
    
    # Apply filters
    status_filter = request.GET.get('status', 'all')
    if status_filter == 'active':
        conversations = conversations.filter(is_active=True)
    elif status_filter == 'escalated':
        conversations = conversations.filter(escalation__isnull=False)
    
    search_query = request.GET.get('search', '')
    if search_query:
        conversations = conversations.filter(
            Q(user__username__icontains=search_query) |
            Q(customer__name__icontains=search_query) |
            Q(session_id__icontains=search_query)
        )
    
    # Pagination
    paginator = Paginator(conversations.order_by('-updated_at'), 20)
    page = request.GET.get('page', 1)
    page_obj = paginator.get_page(page)
    
    context = {
        'conversations': page_obj,
        'status_filter': status_filter,
        'search_query': search_query,
    }
    
    return render(request, 'management/chat_conversations.html', context)


@login_required
def admin_chat_detail(request, conversation_id):
    """Admin view for detailed chat conversation."""
    if not request.user.is_staff:
        return JsonResponse({'error': 'Unauthorized'}, status=403)
    
    conversation = get_object_or_404(
        ChatConversation.objects.select_related('user', 'customer'),
        id=conversation_id
    )
    
    messages = conversation.messages.order_by('created_at')
    escalation = getattr(conversation, 'escalation', None)
    
    context = {
        'conversation': conversation,
        'messages': messages,
        'escalation': escalation,
    }
    
    return render(request, 'management/chat_detail.html', context)


@login_required
@require_POST
def admin_escalation_update(request, escalation_id):
    """Update escalation status."""
    if not request.user.is_staff:
        return JsonResponse({'error': 'Unauthorized'}, status=403)
    
    try:
        escalation = get_object_or_404(ChatEscalation, id=escalation_id)
        
        data = json.loads(request.body)
        status = data.get('status')
        resolution_notes = data.get('resolution_notes', '')
        
        if status in dict(ChatEscalation.ESCALATION_STATUS_CHOICES):
            escalation.status = status
            escalation.assigned_staff = request.user
            escalation.resolution_notes = resolution_notes
            
            if status == 'resolved':
                escalation.resolved_at = timezone.now()
            
            escalation.save()
            
            return JsonResponse({'success': True})
        else:
            return JsonResponse({'error': 'Invalid status'}, status=400)
            
    except json.JSONDecodeError:
        return JsonResponse({'error': 'Invalid JSON data'}, status=400)
    except Exception as e:
        logger.error(f"Error updating escalation: {str(e)}")
        return JsonResponse({'error': 'Internal server error'}, status=500)
