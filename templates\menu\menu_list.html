{% extends 'base.html' %}

{% block title %}Our Menu - BonAppetit Catering{% endblock %}

{% block content %}
<div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <div class="text-center mb-8">
        <h1 class="text-4xl font-bold text-charcoal mb-4">Our Delicious Menu</h1>
        <p class="text-lg text-gray-600 max-w-2xl mx-auto">
            Choose from our carefully crafted selection of appetizers, main dishes, desserts, and beverages.
        </p>
    </div>
    
    <div x-data="menuSelection({{ menu_items|safe }})" class="space-y-8">
        <!-- Category Filter -->
        <div class="bg-white rounded-xl shadow-lg border border-sage-green p-8">
            <div class="text-center mb-6">
                <h2 class="text-2xl font-bold text-charcoal mb-2">Filter by Category</h2>
                <p class="text-gray-600">Browse our menu by category to find exactly what you're looking for</p>
            </div>
            <div class="flex flex-wrap justify-center gap-3">
                <button @click="setActiveCategory('all')" 
                        :class="activeCategory === 'all' ? 'bg-burnt-orange text-white shadow-lg scale-105' : 'bg-cream text-charcoal hover:bg-sage-green hover:text-white border border-sage-green'"
                        class="px-6 py-3 rounded-full font-semibold transition-all duration-300 transform hover:scale-105 flex items-center space-x-2">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
                    </svg>
                    <span>All Items</span>
                </button>
                <button @click="setActiveCategory('appetizer')" 
                        :class="activeCategory === 'appetizer' ? 'bg-burnt-orange text-white shadow-lg scale-105' : 'bg-cream text-charcoal hover:bg-sage-green hover:text-white border border-sage-green'"
                        class="px-6 py-3 rounded-full font-semibold transition-all duration-300 transform hover:scale-105 flex items-center space-x-2">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 15.546c-.523 0-1.046.151-1.5.454a2.704 2.704 0 01-3 0 2.704 2.704 0 00-3 0 2.704 2.704 0 01-3 0 2.704 2.704 0 00-3 0 2.704 2.704 0 01-3 0A1.5 1.5 0 013 14.546V6a3 3 0 113 3V4.5A1.5 1.5 0 017.5 3h9A1.5 1.5 0 0118 4.5v4.546c.523 0 1.046.151 1.5.454z"/>
                    </svg>
                    <span>Appetizers</span>
                </button>
                <button @click="setActiveCategory('main')" 
                        :class="activeCategory === 'main' ? 'bg-burnt-orange text-white shadow-lg scale-105' : 'bg-cream text-charcoal hover:bg-sage-green hover:text-white border border-sage-green'"
                        class="px-6 py-3 rounded-full font-semibold transition-all duration-300 transform hover:scale-105 flex items-center space-x-2">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v13m0-13V6a2 2 0 112 2h-2zm0 0V5.5A2.5 2.5 0 109.5 8H12zm-7 4h14M5 12a2 2 0 110-4h14a2 2 0 110 4M5 12v7a2 2 0 002 2h10a2 2 0 002-2v-7"/>
                    </svg>
                    <span>Main Dishes</span>
                </button>
                <button @click="setActiveCategory('dessert')" 
                        :class="activeCategory === 'dessert' ? 'bg-burnt-orange text-white shadow-lg scale-105' : 'bg-cream text-charcoal hover:bg-sage-green hover:text-white border border-sage-green'"
                        class="px-6 py-3 rounded-full font-semibold transition-all duration-300 transform hover:scale-105 flex items-center space-x-2">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 15.546c-.523 0-1.046.151-1.5.454a2.704 2.704 0 01-3 0 2.704 2.704 0 00-3 0 2.704 2.704 0 01-3 0 2.704 2.704 0 00-3 0 2.704 2.704 0 01-3 0A1.5 1.5 0 013 14.546V6a3 3 0 113 3V4.5A1.5 1.5 0 017.5 3h9A1.5 1.5 0 0118 4.5v4.546c.523 0 1.046.151 1.5.454z"/>
                    </svg>
                    <span>Desserts</span>
                </button>
                <button @click="setActiveCategory('drink')" 
                        :class="activeCategory === 'drink' ? 'bg-burnt-orange text-white shadow-lg scale-105' : 'bg-cream text-charcoal hover:bg-sage-green hover:text-white border border-sage-green'"
                        class="px-6 py-3 rounded-full font-semibold transition-all duration-300 transform hover:scale-105 flex items-center space-x-2">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z"/>
                    </svg>
                    <span>Beverages</span>
                </button>
            </div>
        </div>
        
        <!-- Menu Items Grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <!-- Fallback message if no items -->
            <template x-if="filteredItems.length === 0">
                <div class="col-span-full text-center py-12 text-gray-500 text-xl">
                    No menu items available at the moment. Please check back later!
                </div>
            </template>
            <!-- Menu items -->
            <template x-for="item in filteredItems" :key="item.id">
                <div class="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transform hover:-translate-y-2 transition-all duration-300 border border-gray-100">
                    <!-- Image Section -->
                    <div class="relative h-56 bg-gradient-to-br from-sage-green to-deep-olive overflow-hidden">
                        <template x-if="item.image">
                            <img :src="item.image ? item.image : '/static/default_menu_item.png'" 
                                 :alt="item.name"
                                 class="w-full h-full object-cover hover:scale-105 transition-transform duration-300"
                                 loading="lazy"
                                 @error="$event.target.style.display = 'none'; $event.target.nextElementSibling.style.display = 'flex'">
                        </template>
                        <div x-show="!item.image" class="absolute inset-0 flex items-center justify-center text-white">
                            <div class="text-center">
                                <div class="w-16 h-16 bg-white bg-opacity-20 rounded-full flex items-center justify-center mx-auto mb-3">
                                    <svg class="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
                                        <path d="M8.1 13.34l2.83-2.83L3.91 3.5c-1.56 1.56-1.56 4.09 0 5.66l4.19 4.18zm6.78-1.81c1.53.71 3.68.21 5.27-1.38 1.91-1.91 2.28-4.65.81-6.12-1.46-1.46-4.2-1.1-6.12.81-1.59 1.59-2.09 3.74-1.38 5.27L3.7 19.87l1.41 1.41L12 14.41l6.88 6.88 1.41-1.41-6.88-6.88 1.37-1.37z"/>
                                    </svg>
                                </div>
                                <p class="text-lg font-semibold" x-text="getCategoryName(item.category)"></p>
                                <p class="text-sm opacity-90">Featured Item</p>
                            </div>
                        </div>
                        <!-- Price Badge -->
                        <div class="absolute top-4 right-4">
                            <span class="bg-burnt-orange text-white px-3 py-1 rounded-full text-sm font-bold shadow-lg" 
                                  x-text="'$' + item.price"></span>
                        </div>
                        <!-- Category Badge -->
                        <div class="absolute bottom-4 left-4">
                            <span class="bg-white bg-opacity-90 text-charcoal text-xs px-3 py-1 rounded-full font-medium shadow-sm" 
                                  x-text="getCategoryName(item.category)"></span>
                        </div>
                    </div>
                    
                    <!-- Content Section -->
                    <div class="p-6">
                        <div class="mb-3">
                            <h3 class="text-xl font-bold text-charcoal mb-1 leading-tight" x-text="item.name"></h3>
                        </div>
                        <p class="text-gray-600 text-sm leading-relaxed mb-6" x-text="item.description"></p>
                        
                        <!-- Action Section -->
                        <div class="flex items-center justify-between pt-4 border-t border-gray-100">
                            <div class="flex items-center space-x-3">
                                <div class="flex items-center">
                                    <svg class="w-4 h-4 text-yellow-500 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                                    </svg>
                                    <span class="text-xs text-gray-500">Popular</span>
                                </div>
                            </div>
                            <label class="flex items-center cursor-pointer group">
                                <input type="checkbox" 
                                       :value="item.id"
                                       x-model="selectedItems"
                                       @change="updateSelection(item)"
                                       hx-post="{% url 'savory_events:htmx_menu_update' %}"
                                       hx-target="#menu-summary"
                                       hx-swap="innerHTML"
                                       hx-include="[name='selected_items']"
                                       class="h-4 w-4 text-burnt-orange focus:ring-burnt-orange border-2 border-sage-green rounded mr-2 transition-colors">
                                <span class="text-sm font-semibold text-charcoal group-hover:text-burnt-orange transition-colors">
                                    Add to Order
                                </span>
                            </label>
                        </div>
                    </div>
                </div>
            </template>
        </div>
        
        <!-- Selection Summary -->
        <div class="bg-white rounded-xl shadow-xl border border-sage-green p-8 sticky bottom-4" x-show="selectedItems.length > 0" 
             x-transition:enter="transition ease-out duration-300"
             x-transition:enter-start="opacity-0 transform translate-y-4"
             x-transition:enter-end="opacity-100 transform translate-y-0">
            <div id="menu-summary">
                <div class="flex items-center mb-6">
                    <div class="w-10 h-10 bg-burnt-orange rounded-full flex items-center justify-center mr-3">
                        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.5 5M7 13l2.5 5m0 0L17 13m0 0l2.5 5M9.5 18h5"></path>
                        </svg>
                    </div>
                    <div>
                        <h3 class="text-2xl font-bold text-charcoal">Your Order</h3>
                        <p class="text-sm text-gray-600" x-text="selectedItems.length + ' item' + (selectedItems.length !== 1 ? 's' : '') + ' selected'"></p>
                    </div>
                </div>
                
                <div class="space-y-3 mb-6">
                    <template x-for="item in getSelectedItemDetails()" :key="item.id">
                        <div class="bg-cream rounded-lg p-4 flex justify-between items-center hover:bg-opacity-80 transition-colors">
                            <div class="flex-1">
                                <h4 class="font-semibold text-charcoal text-sm" x-text="item.name"></h4>
                                <p class="text-xs text-gray-600" x-text="getCategoryName(item.category)"></p>
                            </div>
                            <div class="flex items-center space-x-3">
                                <span class="font-bold text-burnt-orange" x-text="'$' + item.price"></span>
                                <button @click="removeItem(item.id)" 
                                        class="w-6 h-6 bg-red-100 hover:bg-red-200 rounded-full flex items-center justify-center text-red-600 hover:text-red-700 transition-colors">
                                    <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                    </svg>
                                </button>
                            </div>
                        </div>
                    </template>
                </div>
                
                <div class="border-t-2 border-sage-green pt-6 mb-6">
                    <div class="flex justify-between items-center mb-2">
                        <span class="text-lg font-semibold text-charcoal">Subtotal per person:</span>
                        <span class="text-2xl font-bold text-burnt-orange" x-text="'$' + calculateTotal()"></span>
                    </div>
                    <div class="bg-sage-green bg-opacity-10 rounded-lg p-3">
                        <p class="text-xs text-charcoal text-center">
                            <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            Final pricing may vary based on guest count and customizations
                        </p>
                    </div>
                </div>
                
                <div class="text-center">
                    <a href="/booking/" 
                       class="w-full inline-block bg-gradient-to-r from-burnt-orange to-deep-olive hover:from-deep-olive hover:to-burnt-orange text-white font-bold py-4 px-8 rounded-xl transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl text-lg">
                        <svg class="w-5 h-5 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3a4 4 0 118 0v4m-4 8v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2z"></path>
                        </svg>
                        Proceed to Booking
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Hidden inputs for HTMX -->
        <template x-for="itemId in selectedItems" :key="itemId">
            <input type="hidden" name="selected_items" :value="itemId">
        </template>
    </div>
</div>

<script>
function menuSelection(menuItemsData) {
    return {
        activeCategory: 'all',
        selectedItems: [],
        menuItems: menuItemsData,
        
        get filteredItems() {
            if (this.activeCategory === 'all') {
                return this.menuItems;
            }
            return this.menuItems.filter(item => item.category === this.activeCategory);
        },
        
        setActiveCategory(category) {
            this.activeCategory = category;
        },
        
        getCategoryName(category) {
            const names = {
                'appetizer': 'Appetizer',
                'main': 'Main Dish',
                'dessert': 'Dessert',
                'drink': 'Beverage'
            };
            return names[category] || category;
        },
        
        updateSelection(item) {
            // HTMX will handle the server-side update
        },
        
        removeItem(itemId) {
            const index = this.selectedItems.indexOf(itemId);
            if (index > -1) {
                this.selectedItems.splice(index, 1);
                // Trigger HTMX update
                document.querySelector(`input[value="${itemId}"]`).checked = false;
                htmx.trigger(document.querySelector(`input[value="${itemId}"]`), 'change');
            }
        },
        
        getSelectedItemDetails() {
            return this.menuItems.filter(item => this.selectedItems.includes(item.id));
        },
        
        calculateTotal() {
            return this.getSelectedItemDetails()
                .reduce((total, item) => total + item.price, 0)
                .toFixed(2);
        }
    }
}
</script>
{% endblock %}
